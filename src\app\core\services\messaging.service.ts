import { Injectable } from '@angular/core';
import { Subject, Observable, BehaviorSubject } from 'rxjs';

export interface Message {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
}

@Injectable({
  providedIn: 'root'
})
export class MessagingService {
  private messageSubject = new BehaviorSubject<{ type: 'success' | 'error' | 'warning' | 'info'; message: string } | null>(null);

  constructor() { }

  getMessage(): Observable<{ type: 'success' | 'error' | 'warning' | 'info'; message: string } | null> {
    return this.messageSubject.asObservable();
  }

  success(message: string, noAutoClear?: boolean): void {
    this.messageSubject.next({ type: 'success', message });
    if (!noAutoClear) {
      this.autoClear();
    }
  }

  error(message: string): void {
    this.messageSubject.next({ type: 'error', message });
  }

  warning(message: string): void {
    this.messageSubject.next({ type: 'warning', message });
  }

  info(message: string): void {
    this.messageSubject.next({ type: 'info', message });
  }

  closeModal() {
    this.messageSubject.next(null);
  }

  private autoClear(): void {
    setTimeout(() => {
      this.messageSubject.next(null);
    }, 4000);
  }
}

