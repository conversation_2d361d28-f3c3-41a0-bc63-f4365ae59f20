import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReviewComponent } from './review.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgbDropdownModule, NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgxPayPalModule } from 'ngx-paypal';
import { ReviewThemeComponent } from './review-theme/review-theme.component';
import { Review3Component } from './review3/review3.component';
import { Review2Component } from './review2/review2.component';

const routes: Routes = [
  // { path: '', component: ReviewComponent },
  { path: '', component: ReviewThemeComponent }
];

@NgModule({
  declarations: [ReviewComponent, ReviewThemeComponent, Review3Component, Review2Component],
  imports: [RouterModule.forChild(routes), SharedModule, NgbModule, NgxPayPalModule, NgbDropdownModule],
})
export class ReviewModule { }
