import { Cur<PERSON>cy<PERSON>ip<PERSON>, formatDate, ViewportScroller, LocationStrategy } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { NgForm } from '@angular/forms';
import { ReferralService } from 'src/app/core/services/referral.service';
import { Referral } from 'src/app/core/models/referral';
import { ReferralPending } from 'src/app/core/models/referral-pending';
import { NotificationService } from 'src/app/core/services/notification.service';
import { MessagingService } from 'src/app/core/services/messaging.service';

@Component({
  selector: 'app-referral2',
  templateUrl: './referral2.component.html',
  styleUrls: ['./referral2.component.scss']
})
export class Referral2Component implements OnInit {

  subs = new Subscription();
  isLoading = false;
  error = null;
  errorMessage = null;
  errorChangePassword = null;

  isModelLoading = false;
  Modelerror = null;

  isEmailModelLoading = false;
  ModelEmailerror = null;

  copyTextChange = false;
  tableExpand = false;

  user: User;
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;
  previousPage: any;
  phoneNumber: string;
  emailAddress: string;
  refferUrl: string;

  restaurant: Restaurant = new Restaurant();
  referral: Referral = new Referral();
  referralPendings: ReferralPending[] = [];

  options = { query: null, page: 1, per_page: 10, customer_id: null };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private currencyPipe: CurrencyPipe,
    private restaurantService: RestaurantService,
    private referralService: ReferralService,
    private locationStrategy: LocationStrategy,
    private notificationService: NotificationService,
    private messagingService: MessagingService,
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    this.options.customer_id = user?.id;
    this.refferUrl = location.origin + this.locationStrategy.getBaseHref() + "auth?referral=" + this.user.referral_code;

    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    this.modalOptions = {
      backdrop: 'static',
      backdropClass: 'customBackdrop',
      windowClass: 'rounded-modal'
    };
    this.fetchRestaurant();
    this.fetchReferral();
    this.fetchReferralPending();
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
      }, err => this.error = err)
    );
  }

  fetchReferral() {
    this.isLoading = true;

    this.subs.add(this.referralService.show('1')
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.referral = res;
      }, err => this.error = err)
    );
  }

  fetchReferralPending() {
    this.isLoading = true;

    this.subs.add(this.referralService.getReferralPending({ nopaginate: 1 })
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        (res) => {
          this.referralPendings = res;
        }, (err) => {
          this.referralPendings = [];
        }
      )
    )
  }

  copyMessage(val: string) {
    const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = this.refferUrl;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
    this.copyTextChange = true;
    this.messagingService.info("Referral link copied !!");
  }

  smsReferral(model) {
    this.openModal(model);
  }

  emailReferral(model) {
    this.openModal(model);
  }

  keyPress(event: any) {
    const pattern = /[0-9\+\-\ ]/;

    let inputChar = String.fromCharCode(event.charCode);
    if (event.keyCode != 8 && !pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  validatePhone() {
    this.isModelLoading = true;
    this.Modelerror = false;

    if (!this.phoneNumber || this.phoneNumber.length < 10) {
      this.Modelerror = 'Please enter valid phone number';
      this.isModelLoading = false;
    } else {
      this.subs.add(
        this.userService.sendLink({ phone_number: this.phoneNumber, message: this.refferUrl }).
          pipe(finalize(() => this.isModelLoading = false))
          .subscribe(
            (res) => {
              this.phoneNumber = '';
              this.messagingService.success("Referral link send successfully !!");
              this.modalService.dismissAll();
            },
            (err) => {
              this.Modelerror = err;
            }
          )
      )
    }
  }

  validateEmail() {
    this.isEmailModelLoading = true;
    this.ModelEmailerror = false;

    if (!this.emailAddress) {
      this.ModelEmailerror = 'Please enter valid email';
      this.isEmailModelLoading = false;
    } else {
      this.subs.add(
        this.userService.sendLinkEmail({ email: this.emailAddress, message: this.refferUrl }).
          pipe(finalize(() => this.isEmailModelLoading = false))
          .subscribe(
            (res) => {
              this.emailAddress = '';
              this.messagingService.success("Referral link send successfully !!");
              this.modalService.dismissAll();
            },
            (err) => {
              this.ModelEmailerror = err;
            }
          )
      )
    }

  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
