import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InformationComponent } from './information.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgxPayPalModule } from 'ngx-paypal';
import { InfoThemeComponent } from './info-theme/info-theme.component';
import { Information2Component } from './information2/information2.component';

const routes: Routes = [
  // { path: '', component: InformationComponent },
  { path: '', component: InfoThemeComponent },
];

@NgModule({
  declarations: [InformationComponent, InfoThemeComponent, Information2Component],
  imports: [RouterModule.forChild(routes), SharedModule, NgbModule, NgxPayPalModule],
})
export class InformationModule { }
