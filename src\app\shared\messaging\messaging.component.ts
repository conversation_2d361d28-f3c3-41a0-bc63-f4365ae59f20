import { Component, OnInit, SimpleChanges } from '@angular/core';
import { Observable } from 'rxjs';
import { MessagingService } from 'src/app/core/services/messaging.service';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-messaging',
  templateUrl: './messaging.component.html',
  styleUrls: ['./messaging.component.scss'],
  animations: [
    trigger('popupAnimation', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(-20px)' }),
        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ]),
      transition(':leave', [
        animate('300ms ease-in', style({ opacity: 0, transform: 'translateY(-20px)' }))
      ])
    ])
  ]

})
export class MessagingComponent implements OnInit {
  message$: Observable<{ type: 'success' | 'error' | 'warning' | 'info'; message: string } | null>;

  constructor(private messagingService: MessagingService) {
    this.message$ = this.messagingService.getMessage();
  }

  ngOnInit(): void { }
  close() {
    this.messagingService.closeModal();
  }
}


