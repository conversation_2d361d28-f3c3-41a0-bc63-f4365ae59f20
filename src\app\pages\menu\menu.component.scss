.menu-item {
  .menu-details {
    width: 100%;
  }

  .add-item {
    cursor: pointer;
  }
}

.cart-wrapper-fixed {
  position: fixed;
  z-index: 1;
  top: 70px !important;
}

.order-types {
  box-shadow: 0px 7px 9px 0px #3e3e3e21;
}

.accordion-button:not(.collapsed) {
  color: #fff;
  background-color: var(--primary);
  border: none;
}

.footer-category {
  position: fixed;
  right: 12px;
  bottom: 10px;
  width: 250px;
  z-index: 12;
  max-height: 300px;
  letter-spacing: 0.3px;
  text-align: left;
  overflow-y: scroll;
}

.alergy-icon {
  padding-left: 3px;
}

.customActiveMenu {
  color: #fff;
  background-color: var(--primary);
}
