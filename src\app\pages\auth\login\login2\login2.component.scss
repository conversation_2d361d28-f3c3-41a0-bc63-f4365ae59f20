::ng-deep .rounded-modal > .modal-dialog > .modal-content {
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.forgot-modal {
  border-radius: 1rem;

  .forgot-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .forgot-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      min-width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
    }

    .forgot-modal-title {
      font-size: 1.5rem;
    }
  }
}

.forgot-modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  column-gap: 1rem;
  margin-top: 1rem;

  .forgot-modal-close {
    background-color: var(--primary);
    color: #fff;
    padding: 0.75rem;
    border: none;
    border-radius: 2rem;
    font-size: 1rem;
    font-weight: 500;

    &:hover {
      color: var(--primary);
      border: 1px solid var(--primary);
      background-color: #fff;
    }
  }
}

.change-pass-modal {
  border-radius: 1rem;

  .change-pass-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .change-pass-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      min-width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
    }

    .change-pass-modal-title {
      font-size: 1.5rem;
    }
  }
}

.login-button {
  background-color: var(--primary);
  color: #fff;
  padding: 10px 5px;
  border: 1px solid #fff;
  border-radius: 2rem;
  font-size: 1rem;
  font-weight: 500;
  width: 100%;
  transition: all 0.3s ease;

  &:hover {
    color: var(--primary);
    border: 1px solid var(--primary);
    background-color: #fff;
  }
}

::ng-deep ng-otp-input input {
  border-radius: 0.5rem !important;
  font-size: 1.25rem !important;
  font-weight: 500 !important;
  width: 3rem !important;
  height: 3rem !important;

  @media (max-width: 576px) {
    border-radius: 0.5rem !important;
    font-size: 1.25rem !important;
    font-weight: 500 !important;
    width: 2.25rem !important;
    height: 2.25rem !important;
  }
}

.custom_input {
  text-align: center;
}
