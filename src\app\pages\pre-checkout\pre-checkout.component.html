<div class="container mt-2 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-xs-6 col-sm-6 mt-2">
      <div class="bg-white p-3 body-box-shadow rounded" *ngIf="order.order_type == 'delivery'">
        <h5 class="col-sm-12 col-xs-12 text-center" *ngIf="order.order_type == 'delivery'">
          Delivery Address & Timings</h5>
        <div class="col-sm-12 col-xs-12 p-2" *ngIf="order.order_type == 'delivery'">
          <div class="fw-bold pb-2 cursor" data-target="#addressModal" (click)="addAddress(addressModal)"
            data-toggle="modal">
            <img src="./assets/icons/plus.png"> Add New Delivery Address
          </div>

          <div *ngIf="errorAddress">
            <span class="text-danger fw-bold">{{ errorAddress }}</span>
          </div>

          <div *ngFor=" let addressBook of addressBooks; let i=index">
            <label class="form-check-label fw-bold col-12 p-1 text-truncate text-dark"
              for="inlineRadio{{addressBook.id}}">
              <input [(ngModel)]="order.address_id" type="radio" class="form-check-input"
                (ngModelChange)="showDeliveryCharge(addressBook)" name="checkout_address"
                [id]="'inlineRadio'+addressBook.id" [value]="addressBook.id">
              {{addressBook.title}} , {{addressBook.flat_no != addressBook.flat_no?",": ''}}
              {{addressBook.address}}
            </label>
          </div>
        </div>
      </div>

      <div class="bg-white mt-2 p-3 body-box-shadow rounded">
        <h5 class="col-12 mt-2 px-2">{{order.order_type | titlecase}} Details</h5>
        <div class="form-check col-12 py-1" *ngIf="restaurant.currentStatus == 'Open'">
          <label class="form-check-label px-2" for="deliverytime_now">
            <input type="radio" class="form-check-input" id="deliverytime_now" [(ngModel)]="order.assoonas"
              (ngModelChange)="deliveryNow('now')" name="deliverytime" value="now">ASAP</label>
        </div>
        <div class="form-check col-12 py-1">
          <label class="form-check-label px-2" for="deliverytime_later">
            <input type="radio" class="form-check-input" id="deliverytime_later" [(ngModel)]="order.assoonas"
              (ngModelChange)="deliveryNow('later')" name="deliverytime" value="later">Later</label>
        </div>

        <div class="col-md-12 col-xs-12 d-md-flex" *ngIf="order.assoonas == 'later'">
          <!-- Date &  -->
          <p class="col-md-4 col-xs-12">Choose {{order.order_type | titlecase}} Time</p>
          <!-- <div class="col-md-4 col-xs-12 p-2">
            <input type="text" class="form-control bg-white cursor" [minDate]="minDate" [maxDate]="maxDate"
              [(ngModel)]="deliveryDate" ngbDatepicker #d="ngbDatepicker" (click)="d.toggle()" (dateSelect)="d.toggle()"
              name="delivery_date" id="delivery_date" (dateSelect)="onSelect($event)" readonly />
          </div> -->
          <div class="col-md-4 col-xs-12 p-2">
            <input type="text" name="delivery_time" id="delivery_time" [(ngModel)]="order.delivery_time" value="Close"
              *ngIf="timeSlots.length == 0" class="form-control bg-white" placeholder="Close" readonly />
            <select class="form-control" [(ngModel)]="order.delivery_time" name="delivery_time" id="delivery_time"
              *ngIf="timeSlots.length > 0">
              <option disabled>Select Time Slot</option>
              <option *ngFor="let time of timeSlots;let j=idx" value="{{time}}" [selected]="j === 0">{{time}}</option>
            </select>
          </div>
        </div>
        <div *ngIf="errorTime">
          <span class="text-danger fw-bold">{{ errorTime }}</span>
        </div>
        <p class="col-12 p-1" style="border-bottom: dashed 1px;">Note : Please note that the
          {{order.order_type}} time is not always guaranteed and may vary depending on traffic and product
          availability.
        </p>

        <p class="col-12 fw-bold">Any instructions for {{order.order_type}} (optional)</p>
        <textarea rows="4" class="col-12 border border-default" nz-input [(ngModel)]="order.order_description"
          name="order_description" id="order_description" placeholder="Enter Your Comment Here"> </textarea>
      </div>
    </div>

    <div class="col-md-6 mt-2">
      <div class="bg-white body-box-shadow rounded p-2">
        <div class="text-center">
          <h5 class=" text-center"> Your {{order.order_type | titlecase}} Order</h5>
          <div class=" col-12 d-flex order-types" *ngIf="false">
            <div class="fw-bold col-6 p-2 h-100 cursor rounded-start" (click)="orderType('pickup')"
              [ngClass]="{'bg-primary text-white' : order.order_type == 'pickup'}">
              <a>
                Pickup
                <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_pickup != 'Yes'">Unavailable</p>
              </a>
            </div>
            <div class="fw-bold col-6 p-2 h-100 cursor rounded-end" (click)="orderType('delivery')"
              [ngClass]="{'bg-primary text-white' : order.order_type == 'delivery'}">
              <a>
                Delivery
                <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_delivery != 'Yes'">Unavailable</p>
              </a>
            </div>
          </div>
          <div class="col-12 text-primary fw-bold pb-2 border-bottom">
            You have {{carts.length > 0 ? carts.length :''}} items
          </div>
          <div class="card-body cart p-0" *ngIf="carts.length != 0"
            style="max-height: calc(100vh - 30vh);overflow-y: auto;">
            <div class="col-12 text-start" *ngIf="carts.length > 0">
              <div class="fw-bold p-1 col-12  border-bottom" *ngFor=" let cart of carts; let i=index">
                <div class="col-12 d-flex p-1">
                  <div class="col-1 text-center">{{i+1}}</div>
                  <div class="col-11" style="padding-left: 5px;">
                    <div class="w-100 ml-1">{{cart.menu_name}}</div>
                    <div class="w-100" style="font-size: 12px;color: #999;">{{cart.subaddons_name}}</div>
                  </div>
                </div>
                <div class="col-12 d-flex">
                  <div class="col-1"></div>
                  <a class="col-1 text-center cursor" (click)="updateToCart(cart,'remove')">
                    <img src="./assets/icons/minus.png" alt="item plus icon tiffintom">
                  </a>
                  <span class="col-1 text-center">{{cart.quantity}}</span>
                  <a class="col-1 text-center cursor" (click)="updateToCart(cart,'add')">
                    <img src="./assets/icons/plus.png" alt="item plus icon tiffintom">
                  </a>
                  <div class="col-5"></div>
                  <div class="col-3 d-flex">
                    <span class="col-9 text-center">{{convertNumber(cart.total_price)}}</span>
                    <div class="col-2">
                      <a class="menu-item-qty-icon cursor" (click)="updateToCart(cart,'delete')">
                        <img src="./assets/icons/x-mark.png" alt="clear product from cart tiffintom">
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <span class="col-12 fw-bold text-primary" *ngIf="restaurant.suggest_product_count > 0">Did you forget
                ?</span>
              <div class="col-12" *ngFor="let category of categories;let i=index">
                <div class="col-12" *ngFor="let menu of category.menu_suggested;let j=index">
                  <p class="col-12 fw-bold px-3 py-1 mb-0 text-default"
                    *ngIf="menu.menu_addon == 'No' && menu.price_option == 'single' && menu.is_suggest == '1'">
                    <span class="d-flex justify-content-between">
                      <span class="text-start">{{ menu?.menu_name }}</span>
                      <span class="text-end">
                        <div class="add-item align-items-center cursor" *ngIf="menu.variants?.length == 1" (click)="
                        addItemToCart(menuModal,menu,menu.id,menu.variants[0].id)">
                          <img src="./assets/icons/plus.png" alt="Add Item" class="mx-2" />
                        </div>
                      </span>
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12"
            *ngIf="restaurant.online_order == 'Yes' && carts.length != 0 && order.order_type == 'delivery'">
            <button class="btn bg-primary text-white w-100 fw-bold p-2 cursor" (click)="validatePrecheckout()"
              [disabled]="isPrecheckoutLoading || getGrandTotal() < restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes'"
              [ngClass]="{'disabled': getGrandTotal() < restaurant.minimum_order,'disabled': restaurant.restaurant_delivery != 'Yes'}">
              <p class="m-0 d-flex justify-content-between fs-5">
                <span class="text-start">{{ restaurant.currentStatus == 'Open' ? 'Total' : 'Pre Order'}} </span>
                <span class="ms-2 spinner-border text-white text-center" *ngIf="isPrecheckoutLoading" role="status">
                  <span class="visually-hidden">Loading...</span>
                </span>
                <span class="spacing text-end">{{convertNumber(getGrandTotal())}}</span>
              </p>
            </button>
          </div>
          <div class="col-md-12"
            *ngIf="restaurant.online_order == 'Yes' && carts.length != 0 && order.order_type == 'pickup'">
            <button class="btn bg-primary text-white w-100 fw-bold p-2 cursor" (click)="validatePrecheckout()"
              [disabled]="isPrecheckoutLoading || restaurant.restaurant_pickup != 'Yes'"
              [ngClass]="{'disabled': restaurant.restaurant_pickup != 'Yes'}">
              <p class="m-0 d-flex justify-content-between fs-5">
                <span class="text-start">{{ restaurant.currentStatus == 'Open' ? 'Total' : 'Pre Order' }}</span>
                <span class="ms-2 spinner-border text-white text-center" *ngIf="isPrecheckoutLoading" role="status">
                  <span class="visually-hidden">Loading...</span>
                </span>
                <span class="spacing text-end">{{convertNumber(getGrandTotal())}}</span>
              </p>
            </button>
          </div>
          <div class="col-md-12 p-1 text-start"
            *ngIf="carts.length != 0 && order.order_type == 'delivery' && restaurant.minimum_order >= getGrandTotal()">
            <span>Minimum Order {{convertNumber(restaurant.minimum_order)}}</span>
          </div>
          <div class="col-sm-12 empty-cart-cls text-center" *ngIf="carts.length == 0">
            <img src="assets/cartitem.png" class="img-fluid m-4">
            <p><strong>No Item(s) Added</strong></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #addressModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Add New Deliver Address
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">PostCode</label>
      <div class="col-sm-12 col-xs-12 d-flex justify-content-between">
        <input type="text" class="form-control col-md-5 col-xs-12 w-50" id="zipcode" name="zipcode"
          [(ngModel)]="addressBookAdd.zipcode" required />
        <button class="btn bg-primary col-md-5 col-xs-12 text-white align-items-right cursor"
          [disabled]="isPostcodeLoading" style="width:4cm;" (click)="findzipcode(addressBookAdd.zipcode)">
          <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isPostcodeLoading"
            role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          Find Address
        </button>
      </div>
      <div *ngIf="Postcodeerror">
        <span class="text-danger">{{ Postcodeerror }}</span>
      </div>
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Address Title</label>
      <input type="text" class="form-control col-md-8" id="title" name="title" [(ngModel)]="addressBookAdd.title" />
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Door no / Flat no</label>
      <input type="text" class="form-control col-md-8" id="flat_no" name="flat_no" [(ngModel)]="addressBookAdd.flat_no"
        required />
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Address</label>
      <input type="text" class="form-control col-md-8" id="address" name="address" [(ngModel)]="addressBookAdd.address"
        required />
    </div>
    <input type="hidden" class="form-control col-md-8" id="latitude" name="latitude"
      [(ngModel)]="addressBookAdd.latitude" />
    <input type="hidden" class="form-control col-md-8" id="longitude" name="longitude"
      [(ngModel)]="addressBookAdd.longitude" />
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isModelLoading" class="btn btn-primary text-white cursor"
      (click)="validateAddress(addressModal)">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Add Address
    </button>
  </div>
</ng-template>