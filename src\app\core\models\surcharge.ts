export class Surcharge {
  id: string;
  restaurant_id: string;
  surcharge_name: string;
  surcharge_amount: number;

  status: boolean = true;

  created: string;
  modified: string;

  static toFormData(reward: Surcharge) {
    const formData = new FormData();

    if (reward.id) formData.append('id', reward.id);
    if (reward.restaurant_id) formData.append('restaurant_id', reward.restaurant_id);
    if (reward.surcharge_name) formData.append('surcharge_name', reward.surcharge_name);
    if (reward.surcharge_amount) formData.append('surcharge_amount', reward.surcharge_amount.toString());

    return formData;
  }
}
