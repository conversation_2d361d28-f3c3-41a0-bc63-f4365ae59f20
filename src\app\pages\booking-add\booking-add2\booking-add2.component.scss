.submit_button {
  border-radius: 2rem;

  &:hover {
    background-color: #fff !important;
    color: var(--primary) !important;
    border: 1px solid var(--primary) !important;
  }
}

::ng-deep .rounded-modal > .modal-dialog > .modal-content {
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.otp-modal {
  border-radius: 1rem;

  .otp-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .otp-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      min-width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
    }

    .otp-modal-title {
      font-size: 1.5rem;
    }
  }

  .otp-modal-close {
    background-color: var(--primary);
    color: #fff;
    padding: 0.75rem;
    border: none;
    border-radius: 2rem;
    font-size: 1rem;
    font-weight: 500;

    &:hover {
      color: var(--primary);
      border: 1px solid var(--primary);
      background-color: #fff;
    }
  }
}

.profile-modal {
  border-radius: 1rem;

  .profile-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .profile-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      min-width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
    }

    .profile-modal-title {
      font-size: 1.5rem;
    }
  }

  .profile-modal-close {
    width: 100%;
    background-color: var(--primary);
    color: #fff;
    padding: 0.75rem;
    border: none;
    border-radius: 2rem;
    font-size: 1rem;
    font-weight: 500;

    &:hover {
      color: var(--primary);
      border: 1px solid var(--primary);
      background-color: #fff;
    }
  }
}
