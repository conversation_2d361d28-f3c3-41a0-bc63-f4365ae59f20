<div *ngIf="message$ | async as msg" class="popup-container" @popupAnimation>

  <ng-container *ngIf="msg.type === 'success'">
    <div class="container box-body">
      <div class="message-box _success">
        <div class="d-flex align-items-center justify-content-center">
          <div class="wrapper">
            <svg class="checkMark success" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
              <circle class="checkMark_circle_success" cx="26" cy="26" r="25" fill="none" />
              <path class="checkMark_check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" stroke-linecap="round" />
            </svg>
          </div>
          <h4 class="color_success">Success!</h4>
        </div>
        <p>{{ msg.message }}</p>
        <button class="success" (click)="close()">
          Okay
        </button>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="msg.type === 'error'">
    <div class="container box-body">
      <div class="message-box _success">
        <div class="d-flex align-items-center justify-content-center">
          <div class="wrapper">
            <svg class="checkMark error" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
              <circle class="checkMark_circle_error" cx="26" cy="26" r="25" fill="none" />
              <path class="checkMark_check" stroke-linecap="round" fill="none" d="M16 16 36 36 M36 16 16 36" />
            </svg>
          </div>
          <h4 class="color_red">Sorry :(</h4>
        </div>
        <p>{{ msg.message }}</p>
        <button class="error" (click)="close()">
          Try Again
        </button>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="msg.type === 'warning'">
    <div class="container box-body">
      <div class="message-box _success">
        <div class="d-flex align-items-center justify-content-center">
          <div class="wrapper">
            <svg class="checkMark warning" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
              <circle class="checkMark_circle_warning" cx="26" cy="26" r="25" fill="none" />
              <path class="checkMark_check warning" stroke-linecap="round" fill="none"
                d="M26 10 L42 38 H10 Z M26 20 L26 28 M26 32 A1 1 1 1 1 26 36 A1 1 1 1 1 26 32" />
            </svg>
          </div>
          <h4 class="color_warning">Warning!</h4>
        </div>
        <p>{{ msg.message }}</p>
        <button class="warning" (click)="close()">
          Okay
        </button>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="msg.type === 'info'">
    <div class="container box-body">
      <div class="message-box _success">
        <div class="d-flex align-items-center justify-content-center">
          <div class="wrapper">
            <svg class="checkMark info" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
              <circle class="checkMark_circle_info" cx="26" cy="26" r="25" fill="none" />
              <path class="checkMark_check" stroke-linecap="round" fill="none"
                d="M26 20 L26 40 M26 8 A2 2 0 1 1 26 12 A2 2 0 1 1 26 8" />
            </svg>
          </div>
          <h4 class="color_info">Info</h4>
        </div>
        <p>{{ msg.message }}</p>
        <button class="info" (click)="close()">
          Okay
        </button>
      </div>
    </div>
  </ng-container>

</div>