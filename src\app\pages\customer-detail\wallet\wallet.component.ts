import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { WalletHistory } from 'src/app/core/models/wallet-history';
import { WalletHistoryService } from 'src/app/core/services/wallet-history.service';
import { StripeCustomer } from 'src/app/core/models/stripe-customer';
import { StripeCustomerService } from 'src/app/core/services/stripe-customer.service';
import { NotificationService } from 'src/app/core/services/notification.service';
import { MessagingService } from 'src/app/core/services/messaging.service';
declare var Stripe;

@Component({
  selector: 'app-wallet',
  templateUrl: './wallet.component.html',
  styleUrls: ['./wallet.component.scss'],
})
export class WalletComponent implements OnInit, OnDestroy {

  subs = new Subscription();
  isLoading = false;
  error = null;

  isModelLoading = false;
  Modelerror = null;

  isModelPaymentLoading = false;
  ModelPaymenterror = null;

  user: User;
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;
  previousPage: any;

  restaurant: Restaurant = new Restaurant();
  WalletHistories: WalletHistory[] = [];
  walletHistory: WalletHistory = new WalletHistory();
  stripeCustomers: StripeCustomer[] = [];
  stripeCustomer: StripeCustomer = new StripeCustomer();

  totalWalletHistories = 0;
  addAmount: number = 0;
  cardId: string;
  last_page = 0;

  options = { query: null, page: 1, per_page: 10, customer_id: null };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private currencyPipe: CurrencyPipe,
    private restaurantService: RestaurantService,
    private walletHistoryService: WalletHistoryService,
    private stripeCustomerService: StripeCustomerService,
    private notificationService: NotificationService,
    private messagingService: MessagingService,
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    this.options.customer_id = user?.id;
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    this.modalOptions = {
      backdrop: 'static',
      size: 'lg',
      backdropClass: 'customBackdrop',
    };
    this.fetchRestaurant();
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
        if (this.restaurant?.site_setting?.finance_stripe_mode == 'Test') {
          var publishKey = this.restaurant?.site_setting?.finance_stripe_publishkeyTest
        } else {
          var publishKey = this.restaurant?.site_setting?.finance_stripe_publishkey
        }
        this.stripe = Stripe(publishKey);
      }, err => this.error = err)
    );
  }

  fetchWallletHistory() {
    this.isLoading = true;
    this.Modelerror = null;

    this.subs.add(this.walletHistoryService.get(this.options)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.WalletHistories = res.data;
        this.totalWalletHistories = res.total;
        this.last_page = res.last_page;
      }, err => { this.WalletHistories = []; this.totalWalletHistories = 0; this.Modelerror = err })
    );
  }

  fetchCards() {
    this.subs.add(
      this.stripeCustomerService
        .get({ service_type: 'normal', customer_id: this.userId, nopaginate: "1" })
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.stripeCustomers = res;
          },
          (err) => {
            this.stripeCustomers = [];
          }
        )
    );
  }

  loadPage(page: number) {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.fetchWallletHistory();
    }
  }

  walletView(model) {
    this.fetchWallletHistory();
    this.openModal(model);
  }

  loadWalllet(model) {
    this.fetchCards();
    this.openModal(model);
  }

  addMoney(amount) {
    this.addAmount = amount
  }

  loadMoney() {
    this.isModelPaymentLoading = true;
    this.ModelPaymenterror = null;

    if (!this.addAmount || this.addAmount <= 0) {
      this.ModelPaymenterror = 'Please enter amount';
      this.isModelPaymentLoading = false;
      return;
    }

    if (!this.cardId) {
      this.ModelPaymenterror = 'Please select card';
      this.isModelPaymentLoading = false;
      return;
    }

    if (!this.ModelPaymenterror) {
      this.stripeCustomer = this.stripeCustomers.find(card => card.id == this.cardId);
      if (this.restaurant?.site_setting?.minimum_wallet > this.addAmount) {
        this.ModelPaymenterror = 'Please enter minimum £' + this.restaurant?.site_setting?.minimum_wallet + ' amount';
        this.isModelPaymentLoading = false;
      } else {
        this.createPaymentIntent(this.stripeCustomer);
      }
    }

  }

  async createPaymentIntent(stripeCustomerNew) {
    this.stripeCustomer = stripeCustomerNew;
    // this.stripeCustomer.restaurant_id = this.restaurant_id;
    this.stripeCustomer.amount = this.addAmount;
    this.subs.add(
      this.stripeCustomerService
        .payment_intent(this.stripeCustomer)
        .pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.continueToPayment(res.payment_intent_id);
          },
          (err) => {
            this.ModelPaymenterror = err;
            this.isModelPaymentLoading = false;
          }
        )
    );
  }

  async continueToPayment(paymentIntentId) {
    this.stripe.confirmCardPayment(paymentIntentId).then((result) => {
      if (result.error) {
        this.isModelPaymentLoading = false;
        this.ModelPaymenterror = 'Sorry your Payment Faild! Please try again';
      } else {
        if (result.paymentIntent.status === 'succeeded') {
          this.walletHistory.customer_id = this.user.id;
          this.walletHistory.purpose = 'Money added in wallet';
          this.walletHistory.transaction_type = 'Credited';
          this.walletHistory.amount = this.addAmount;
          this.subs.add(this.walletHistoryService.create(this.walletHistory)
            .pipe(finalize(() => this.isModelPaymentLoading = false))
            .subscribe(res => {
              this.fetchMe();
            }, err => this.ModelPaymenterror = err)
          );
        }
      }
    });
  }

  fetchMe() {
    this.subs.add(this.userService.me()
      .pipe(finalize(() => this.isModelPaymentLoading = false))
      .subscribe(res => {
        this.user = res;
        this.userService.saveUser(res);
        this.messagingService.success("Money added into wallet successfully!!");
        this.modalService.dismissAll();
      }, err => this.ModelPaymenterror = err)
    );
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
