import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import {
  NgbModal,
  ModalDismissReasons,
  NgbModalOptions,
  NgbActiveModal,
} from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Order } from 'src/app/core/models/order';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { OrderService } from 'src/app/core/services/order.service';
import { AnimationOptions } from 'ngx-lottie';

@Component({
  selector: 'app-order-detail2',
  templateUrl: './order-detail2.component.html',
  styleUrls: ['./order-detail2.component.scss']
})
export class OrderDetail2Component implements OnInit {
  subs = new Subscription();
  isLoading = false;
  error = null;
  isModelLoading = false;
  Modelerror = null;
  user: User;
  restaurant: Restaurant = new Restaurant();
  order: Order = new Order();
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;


  milliSecondsInASecond = 1000;
  hoursInADay = 24;
  minutesInAnHour = 60;
  SecondsInAMinute = 60;

  public timeDifference;
  public secondsToDday;
  public minutesToDday;
  public hoursToDday;
  public daysToDday;


  orderStatuses = [
    {
      "title": "Order Placed",
      "checked": true,
      "status": 'Pending',
      "order_type": "all"
    },
    {
      "title": "Order Accepted",
      "checked": false,
      "status": 'Accepted',
      "order_type": "all"
    },
    {
      "title": "Preparing",
      "checked": false,
      "status": 'Preparing',
      "order_type": "delivery"
    },
    {
      "title": "On the way",
      "checked": false,
      "status": 'Wait',
      "order_type": "delivery"
    },
    {
      "title": "Delivered",
      "checked": false,
      "status": 'Delivered',
      "order_type": "all"
    }
  ]

  orderRejectStatuses = [
    {
      "title": "Order Placed",
      "checked": true,
      "status": 'Pending',
      "order_type": "all"
    },
    {
      "title": "Order has been Rejected",
      "checked": true,
      "status": 'Failed',
      "order_type": "all"
    },
  ]

  optionPending: AnimationOptions = {
    path: './assets/Animations/Waitingtoaccept.json',
  };
  optionAccepted: AnimationOptions = {
    path: './assets/Animations/OrderPlaced.json',
  };
  optionDelivered: AnimationOptions = {
    path: './assets/Animations/Fooddelivered.json',
  };
  optionCollected: AnimationOptions = {
    path: './assets/Animations/Foodontheway.json',
  };
  optionWaiting: AnimationOptions = {
    path: './assets/Animations/Placed.json',
  };
  optionFailed: AnimationOptions = {
    path: './assets/Animations/Orderrejected.json',
  };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private restaurantService: RestaurantService,
    private currencyPipe: CurrencyPipe,
    private orderService: OrderService,
  ) { }

  ngOnInit(): void {
    this.order.id = atob(this.route.snapshot.paramMap.get('id'));
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    this.modalOptions = {
      backdrop: 'static',
      size: 'lg',
      backdropClass: 'customBackdrop',
    };
    //Restaurant Find 
    this.fetchUser();
    this.fetchRestaurant();
    this.fetchOrder();

    setTimeout(() => { this.ngOnInit() }, 10000);
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
      }, err => this.error = err)
    );
  }

  fetchOrder() {
    this.isLoading = true;

    this.subs.add(this.orderService.show(this.order.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        (res) => {
          this.order = res;
          if (this.userId != this.order.customer_id) {
            this.router.navigateByUrl('/menu');
          }
          if (this.order.status != 'Failed') {
            this.orderStatuses.forEach(statuses => {
              if (this.order.status == 'Delivered') {
                statuses.checked = true;
              }
              if ((this.order.status == 'Collected') && statuses.status != 'Delivered') {
                statuses.checked = true;
              }
              if ((this.order.status == 'Waiting' || this.order.status == 'Driver Accepted') && (statuses.status != 'Delivered' && statuses.status != 'Wait')) {
                statuses.checked = true;
              }
              if (this.order.status == 'Accepted' && (statuses.status != 'Delivered' && statuses.status != 'Wait' && statuses.status != 'Preparing')) {
                statuses.checked = true;
              }
              if (this.order.status == 'Pending' && statuses.status == 'Pending') {
                statuses.checked = true;
              }
            });
          }
          if (this.order.status == 'Accepted') {
            this.startTimer()
          }
        }, (err) => {
          this.router.navigateByUrl('/menu');
        }
      )
    )
  }

  fetchUser() {
    this.isLoading = true;
    this.subs.add(this.userService.show(this.user.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        (res) => {
          this.userService.saveUser(res);
          this.user = res;
        }, (err) => {
        }
      )
    )
  }

  startTimer() {
    this.subs.add(interval(1000).subscribe(x => { this.getTimeDifference(); }));
  }

  private getTimeDifference() {
    this.timeDifference = new Date(this.order.delivery_date + " " + this.order.preparation).getTime() - new Date().getTime();
    this.allocateTimeUnits(this.timeDifference);
  }

  private allocateTimeUnits(timeDifference) {
    this.secondsToDday = Math.floor((timeDifference) / (this.milliSecondsInASecond) % this.SecondsInAMinute);
    this.minutesToDday = Math.floor((timeDifference) / (this.milliSecondsInASecond * this.minutesInAnHour) % this.SecondsInAMinute);
    this.hoursToDday = Math.floor((timeDifference) / (this.milliSecondsInASecond * this.minutesInAnHour * this.SecondsInAMinute) % this.hoursInADay);
    this.daysToDday = Math.floor((timeDifference) / (this.milliSecondsInASecond * this.minutesInAnHour * this.SecondsInAMinute * this.hoursInADay));
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    if (date) {
      return formatDate(date, format ? format : 'yyyy-MM-dd hh:mm a', 'en_US')
    } else {
      return null;
    }
  }

  getPaidVia() {
    return this.order.payment_method == 'cod' ? 'Cash' : this.order.payment_method == 'Stripe' ? 'Credit/Debit Card' : 'Paypal'
  }

  getPaidViaNo() {
    return this.order.payment_method == 'Stripe' ? 'Credit/Debit Card' : this.order.payment_method == 'paypal' ? 'Paypal' : 'Wallet'
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
