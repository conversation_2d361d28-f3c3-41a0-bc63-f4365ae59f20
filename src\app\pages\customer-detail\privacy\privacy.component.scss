// @use 'style.scss'; // Path to _variables.scss Notice how we don't include the underscore or file extension

.menu-item {
  .menu-details {
    width: 100%;
  }

  .add-item {
    cursor: pointer;
  }
}

.fees-table {
  background: #fff;
  border-radius: 6px;
  overflow: auto;
  box-shadow: 0px 2px 5px rgba($color: #000000, $alpha: 0.1);
  text-align: center;

  tr td {
    padding: 8px !important;
    font-size: 15px;
    border-bottom: 1px solid #e1e1e1;
  }

  th {
    font-weight: 600;
    padding: 8px !important;
    text-transform: uppercase;
    background: linear-gradient(180deg, #fafafa, #f2f2f2);
  }
}

.btn {
  &.active {
    background: var(--primary);
    color: #fff;
  }
}

// CSS For Theme 2 & Theme 3
.update-btn {
  background: var(--primary);
  color: white;
  border: 1px solid #fff;
  padding: 8px 14px;
  border-radius: 2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: fit-content;
  margin-left: auto;

  &:hover {
    color: var(--primary);
    border: 1px solid var(--primary);
    background-color: #fff;
  }
}

.switch-button {
  button {
    &:first-child {
      border-radius: 1rem 0rem 0rem 1rem;
    }

    &:last-child {
      border-radius: 0rem 1rem 1rem 0rem;
    }
  }
}
