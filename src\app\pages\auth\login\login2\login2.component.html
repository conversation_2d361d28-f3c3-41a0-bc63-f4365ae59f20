<form nz-form class="login-form mx-auto" #loginForm="ngForm" (ngSubmit)="login(loginForm)">
  <div class="alert alert-danger alert-dismissible fade show" role="alert" *ngIf="errorMessage">
    {{ errorMessage }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
  </div>
  <ng-container *ngIf="!isOtpLogin">
    <div class="mb-2">
      <label for="email" class="form-label m-0">Email</label>
      <input name="email" type="email" class="form-control"
        [ngClass]="{'is-invalid': (email.dirty || loginForm.submitted) && email.invalid}" id="email"
        placeholder="Enter email" [(ngModel)]="user.username" #email="ngModel" required />
      <div class="invalid-feedback" *ngIf="(email.dirty || loginForm.submitted) && email.invalid">
        <small *ngIf="email.errors?.required">Email is required</small>
      </div>
    </div>
    <div class="mb-2">
      <label for="password" class="form-label m-0">Password</label>
      <input name="password" type="password" class="form-control"
        [ngClass]="{'is-invalid': (password.dirty || loginForm.submitted) && password.invalid}" id="password"
        placeholder="Enter password" [(ngModel)]="user.password" #password="ngModel" required />
      <div class="invalid-feedback" *ngIf="(password.dirty || loginForm.submitted) && password.invalid">
        <small *ngIf="password.errors?.required">Password is required</small>
      </div>
    </div>

    <div class="mb-2 text-end">
      <label class="form-label cursor" (click)="forgotPassword(forgotModel)">Forgot Password ?</label>
    </div>
  </ng-container>
  <ng-container *ngIf="isOtpLogin">
    <div class="mb-2" *ngIf="!isOtpSend">
      <label for="phone_number" class="form-label m-0">Phone Number</label>
      <input name="phone_number" type="number" class="form-control"
        [ngClass]="{'is-invalid': (phone_number.dirty || loginForm.submitted) && phone_number.invalid}"
        id="phone_number" placeholder="Enter phone number" [(ngModel)]="user.phone_number" #phone_number="ngModel"
        required />
      <div class="invalid-feedback" *ngIf="(phone_number.dirty || loginForm.submitted) && phone_number.invalid">
        <small *ngIf="phone_number.errors?.required">Phone Number is required</small>
      </div>
    </div>

    <div class="my-4" *ngIf="isOtpSend">
      <ng-otp-input class="text-center custom_input" allowNumbersOnly="true" inputClass="custom_input" #ngOtpInput
        [config]="config" (onInputChange)="onOtpChange($event)"></ng-otp-input>

      <div class="invalid-feedback" *ngIf="otp.length != 6">
        <small *ngIf="otp.length != 6">OTP is required</small>
      </div>

      <div class="text-center">
        <button type="button" class="btn btn-primary text-white" (click)="resendOtp()">Resend OTP </button>
      </div>
    </div>
  </ng-container>
  <div class="d-flex flex-column align-items-center justify-content-center">
    <button type="submit" [disabled]="isLoading" class="login-button">
      <div class="ms-2 spinner-border" *ngIf="isLoading" style="width: 20px;height: 20px;" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Login
    </button>
    <p style="cursor: pointer;" class="mt-2 text-primary" *ngIf="!isOtpLogin" (click)="isOtpLogin = !isOtpLogin">Login
      With OTP?</p>
    <p style="cursor: pointer;" class="mt-2 text-primary" *ngIf="isOtpLogin" (click)="isOtpLogin = !isOtpLogin">Login
      With Password?</p>
  </div>
</form>

<ng-template #forgotModel let-modal>
  <div class="forgot-modal p-3">
    <div class="forgot-modal-header">
      <button class="forgot-close-btn" (click)="modal.dismiss('Cross click')">
        <i class="fas fa-times"></i>
      </button>
      <h4 class="forgot-modal-title text-primary fw-bold" id="modal-basic-title">
        Forgot Password
      </h4>
    </div>
    <!-- Body -->
    <div class="modal-body my-2">
      <div class="col-sm-12 col-xs-12 form-group">
        <label>Email</label>
        <input type="email" class="form-control" id="email" name="email" [(ngModel)]="forgotUser.username"
          [disabled]="isEmailVerify" required placeholder="Enter your email" />
      </div>

      <div class="col-sm-12 col-xs-12 form-group mt-2" *ngIf="isEmailVerify">
        <label>Otp</label>
        <input type="text" class="form-control" id="email_otp" name="email_otp" [(ngModel)]="forgotUser.email_otp"
          required placeholder="Enter your valide otp" />
      </div>

      <div *ngIf="Modelerror">
        <span class="text-danger">{{ Modelerror }}</span>
      </div>
    </div>
    <!-- Footer -->
    <div class="forgot-modal-footer">
      <!-- Validate Email -->
      <button class="forgot-modal-close w-100" [disabled]="isModelLoading" (click)="validateEmail()"
        *ngIf="!isEmailVerify">
        <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
          role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        Submit
      </button>
      <!-- Validate Otp -->
      <button class="forgot-modal-close w-100" [disabled]="isModelLoading" (click)="validateOtp()"
        *ngIf="isEmailVerify">
        <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
          role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        Submit
      </button>
    </div>
  </div>
</ng-template>

<ng-template #changePasswordModel let-modal>
  <div class="change-pass-modal p-3">
    <div class="change-pass-modal-header">
      <button class="forgot-close-btn" (click)="modal.dismiss('Cross click')">
        <i class="fas fa-times"></i>
      </button>
      <h4 class="change-pass-modal-title text-primary fw-bold" id="modal-basic-title">
        Change Password
      </h4>
    </div>
    <!-- Body -->
    <div class="modal-body my-2">
      <div class="col-sm-12 col-xs-12 form-group">
        <label>New Password</label>
        <input type="password" class="form-control" id="password" name="password" [(ngModel)]="forgotUser.password"
          required placeholder="Enter your password" />
      </div>
      <div class="col-sm-12 col-xs-12 mt-2 form-group">
        <label>Confirm Password</label>
        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword"
          [(ngModel)]="forgotUser.confirmPassword" required placeholder="Enter your confirm password" />
      </div>
      <div *ngIf="ModelPassworderror">
        <span class="text-danger">{{ ModelPassworderror }}</span>
      </div>
    </div>
    <!-- Footer -->
    <div class="change-pass-modal-footer">
      <button class="change-pass-modal-close w-100" (click)="validatePassword()" *ngIf="isEmailVerify">
        <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelPasswordLoading"
          role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        Submit
      </button>
    </div>
  </div>
</ng-template>