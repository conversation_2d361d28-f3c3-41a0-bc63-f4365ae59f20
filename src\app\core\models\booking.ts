export class Booking {
  id: string;
  customer_id: string;
  restaurant_id: string;
  booking_id: string;
  customer_name: string;
  guest_count: string;
  booking_email: string;
  booking_phone: string;
  booking_instruction: string;
  booking_date: string;
  booking_time: string;
  status: string = 'Processing';
  type: string;
  cancel_reason: string;
  booking_amount: number = 0;
  card_id: string = '';
  txn_id: string = '';

  created: string;
  updated: string;

  static toFormData(booking: Booking) {
    const formData = new FormData();

    if (booking.id) formData.append('id', booking.id);
    if (booking.customer_id) formData.append('customer_id', booking.customer_id);
    if (booking.restaurant_id) formData.append('restaurant_id', booking.restaurant_id);
    if (booking.booking_id) formData.append('booking_id', booking.booking_id);
    if (booking.customer_name) formData.append('customer_name', booking.customer_name);
    if (booking.guest_count) formData.append('guest_count', booking.guest_count);
    if (booking.booking_email) formData.append('booking_email', booking.booking_email);
    if (booking.booking_phone) formData.append('booking_phone', booking.booking_phone);
    if (booking.booking_instruction) formData.append('booking_instruction', booking.booking_instruction);
    if (booking.booking_date) formData.append('booking_date', booking.booking_date);
    if (booking.booking_time) formData.append('booking_time', booking.booking_time);
    if (booking.status) formData.append('status', booking.status);
    if (booking.type) formData.append('type', booking.type);
    if (booking.cancel_reason) formData.append('cancel_reason', booking.cancel_reason);
    if (booking.booking_amount) formData.append('booking_amount', booking.booking_amount.toString());
    if (booking.card_id) formData.append('card_id', booking.card_id);
    if (booking.txn_id) formData.append('txn_id', booking.txn_id);

    return formData;
  }
}
