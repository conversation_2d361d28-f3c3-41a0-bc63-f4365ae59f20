<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class="row" *ngIf="!isLoading">
    <div class="col-12 bg-white d-md-flex w-100 body-box-shadow p-2" style="min-height: 500px;border-radius: 1rem;">
      <div class="row w-100 m-0 p-0">
        <div class="col-12 col-xl-2 p-2">
          <ul class="list-group body-box-shadow py-3 w-100 flex-row flex-xl-column flex-wrap">
            <a routerLink="dashboard">
              <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">
                Dashboard
              </li>
            </a>
            <a routerLink="orders">
              <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">My Order
              </li>
            </a>
            <a routerLink="bookings">
              <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">My Booking
              </li>
            </a>
            <a routerLink="addresses">
              <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">Address Book
              </li>
            </a>
            <a routerLink="profile">
              <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">Profile
              </li>
            </a>
            <a routerLink="payments">
              <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">Payments
              </li>
            </a>
            <a routerLink="wallet"
              *ngIf="restaurant?.site_setting?.wallet_available == 'Yes' && restaurant.wallet_payment == 'Yes'">
              <li class=" list-group-item" nzMatchRouter [routerLinkActive]="['active']">My Wallet
              </li>
            </a>
            <a routerLink="rewards" *ngIf="reward?.reward_option == 'Yes' && restaurant.reward_option == 'Yes'">
              <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">Reward Point
              </li>
            </a>
            <a routerLink="privacy-settings">
              <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">Privacy Setting
              </li>
            </a>
            <a routerLink="referral" *ngIf="referral.referral_option == 'Yes'">
              <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">Referral Friend
              </li>
            </a>
          </ul>
        </div>

        <div class="col-12 col-xl-10 p-0">
          <router-outlet></router-outlet>
        </div>
      </div>
    </div>
  </div>
</div>