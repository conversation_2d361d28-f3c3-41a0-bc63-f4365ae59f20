import { Variant } from './variant';

export class Menu {
  id?: string;
  restaurant_id?: string; // : 652,
  category_id?: string; // : 1282,
  menu_name?: string; // : "Pizza Samosa",
  is_suggest?: number; // : 0,
  price_option?: string; // : "single",
  menu_description?: string; // : "",
  menu_image?: string; // : null,
  menu_type?: string; // : "veg",
  menu_addon?: string; // : "No",
  popular_dish?: string; // : "No",
  featured?: string; // : "No",
  spicy_dish?: string; // : "no_option",
  favourite?: string; // : "N",
  status?: number; // : 1,
  delete_status?: string; // : "N",
  created?: string; // : "2021-04-05 12:02:16",
  modified?: string; // : "2021-04-05 12:02:16",
  sortorder?: string; // : 57510,
  short_name?: string; // : "PS",
  vegetarian?: number; // : 0,
  contains_nuts?: number; // : 0,
  milk?: number; // : 0,
  mustard?: number; // : 0,
  eggs?: number; // : 0,
  fish?: number; // : 0,
  gluten_free?: number; // : 0,
  halal?: number; // : 0,
  koshar?: number; // : 0,
  vegan?: number; // : 0,
  crustaceans?: number; // : 0,
  molluscs?: number; // : 0,
  sulphur_dioxide?: number; // : 0,
  sulphites?: number; // : 0,
  product_percentage?: number; // : 0
  product_order_type: string;
  product_day: string;
  variants: Variant[];

  // New Variables
  qty: number = 0;
  image_url: string;
}
