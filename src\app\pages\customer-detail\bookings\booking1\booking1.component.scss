// @use 'style.scss'; // Path to _variables.scss Notice how we don't include the underscore or file extension

.menu-item {
  .menu-details {
    width: 100%;
  }

  .add-item {
    cursor: pointer;
  }
}

.cart-wrapper-fixed {
  position: fixed;
  z-index: 1;
  top: 70px !important;
}

.order-types {
  box-shadow: 0px 7px 9px 0px #3e3e3e21;
}

.footer-category {
  position: fixed;
  right: 12px;
  bottom: 10px;
  width: 220px;
  z-index: 12;
  max-height: 300px;
  letter-spacing: .3px;
  text-align: left;
  overflow-y: scroll;
}

.active {
  background-color: var(--primary);
  color: #ffffff;
}

.list-group-item a.active:after {
  color: #ffffff !important;
}

.list-group-item a {
  font-weight: 500;
}

.list-group-item a.active {
  background-color: #fff;
  color: #000 !important;
}

ngb-rating {
  font-size: 25px;
  color: gold;
}
