<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-xs-12 col-sm-12 bg-white rounded body-box-shadow p-2">
      <h5 class="text-center" *ngIf="!bookingPayment">Make a Reservation</h5>
      <h5 class="d-flex justify-content-between" *ngIf="bookingPayment">
        <a nzType="link" style="width: auto; margin-right: 16px;" (click)="bookingPayment = false">
          <i class="fa fa-arrow-left" aria-hidden="true"></i>
        </a>
        <span>Make a Payment</span>
        <span></span>
      </h5>

      <div class="col-xs-12 text-center py-3" *ngIf="success">
        <span class="alert alert-success">
          Your booking request sent successfully
        </span>
      </div>

      <div class="col-xs-12 text-center py-3" *ngIf="error">
        <span class="alert alert-danger">
          {{error}}
        </span>
      </div>

      <form method="post" accept-charset="utf-8" class="form-horizontal">

        <div class="col-md-10 col-sm-10 col-xs-12" *ngIf="!bookingPayment">

          <div class="col-md-12 col-12-10 col-xs-12 d-md-flex pt-2 justify-content-between">
            <label class="col-md-4 col-sm-4 col-xs-12 text-end py-2 fw-bold" for="guest_count">Guest Count</label>
            <div class="col-md-8 col-sm-8 col-xs-12 px-2">
              <select name="guest_count" [(ngModel)]="booking.guest_count" class="form-control" id="guest_count">
                <option value="" disabled>Select Guest</option>
                <option *ngFor="let item of noOfGuest;" [value]="item" selected>{{item}}</option>
              </select>
            </div>
          </div>

          <div class="col-md-12 col-xs-12 d-md-flex pt-2">
            <label class="col-md-4 col-sm-4 col-xs-12 text-end py-2 fw-bold" for=" booking_date">Date / Time</label>
            <div class="col-md-8 col-sm-8 col-xs-12 px-2 d-md-flex">
              <div class="col-sm-6 col-xs-12 py-2">
                <input type="text" name="booking_date" id="booking_date" [(ngModel)]="bookingDate" [minDate]="minDate"
                  [maxDate]="maxDate" ngbDatepicker #d="ngbDatepicker" (click)="d.toggle()" (dateSelect)="d.toggle()"
                  (dateSelect)="onSelect($event)" class="form-control" readonly>
              </div>
              <div class="col-sm-6 col-xs-12 py-2 px-2">
                <input type="text" name="booking_time" id="booking_time" [(ngModel)]="booking.booking_time"
                  value="Close" *ngIf="timeSlots.length == 0" class="form-control bg-white" placeholder="Close"
                  readonly />
                <select id="booking_time" [(ngModel)]="booking.booking_time" class="form-control px-1"
                  name="booking_time" *ngIf="timeSlots.length > 0">
                  <option value="">Select Time</option>
                  <option *ngFor="let time of timeSlots;let j=idx" value="{{time}}" [selected]="j === 0">{{time}}
                  </option>
                </select>
              </div>
            </div>
          </div>

          <div class="col-md-12 col-xs-12 d-md-flex pt-2">
            <label class="col-md-4 col-sm-4 col-xs-12 text-end py-2 fw-bold" for=" customer_name">Name</label>
            <div class="col-md-8 col-sm-8 col-xs-12 px-2">
              <input type="text" name="customer_name" [(ngModel)]="booking.customer_name" class="form-control"
                id="customer_name">
            </div>
          </div>

          <div class="col-md-12 col-xs-12 d-md-flex pt-2">
            <label class="col-md-4 col-sm-4 col-xs-12 text-end py-2 fw-bold" for=" booking_email">Email</label>
            <div class="col-md-8 col-sm-8 col-xs-12 px-2">
              <input type="text" name="booking_email" [(ngModel)]="booking.booking_email" class="form-control"
                id="booking_email">
            </div>
          </div>

          <div class="col-md-12 col-xs-12 d-md-flex pt-2">
            <label class="col-md-4 col-sm-4 col-xs-12 text-end py-2 fw-bold" for=" booking_phone">Phone</label>
            <div class="col-md-8 col-sm-8 col-xs-12 px-2">
              <input type="text" name="booking_phone" (keypress)="keyPress($event)" (blur)="onlyNumeric()"
                minlength="10" maxlength="11" class="form-control" [(ngModel)]="booking.booking_phone"
                id="booking_phone">
            </div>
          </div>

          <div class="col-md-12 col-xs-12 d-md-flex pt-2">
            <label class="col-md-4 col-sm-4 col-xs-12 text-end py-2 fw-bold" for=" booking_instruction">Your
              Instructions</label>
            <div class="col-md-8 col-sm-8 col-xs-12 px-2">
              <textarea name="booking_instruction" [(ngModel)]="booking.booking_instruction" class="form-control"
                id="booking_instruction" rows="5"></textarea>
            </div>
          </div>

          <div class="col-md-12 col-xs-12 d-md-flex">
            <div class="col-md-4 col-xs-4"></div>
            <div class="col-md-8 col-xs-8 pt-3 text-center">
              <button class="btn btn-primary w-25 text-white cursor" value="Submit" type="button"
                [disabled]="isBookingLoading" (click)="validateBook();" id="submit"
                *ngIf="restaurant.booking_status == 'Yes' && restaurant.restaurant_booktable == 'Yes'">
                <div class="ms-2 spinner-border" style="width: 20px;height: 20px;" *ngIf="isBookingLoading"
                  role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                Submit
              </button>
            </div>
          </div>

          <div class="col-md-12 col-xs-12 pt-2 text-center">
            <span class=" btn btn-danger"
              *ngIf="restaurant.booking_status != 'Yes' || restaurant.restaurant_booktable != 'Yes'">Sorry we are not
              accepting any reservations at the moment.</span>
          </div>

        </div>

        <div class="col-md-10 col-sm-10 col-xs-12 pt-3" *ngIf="bookingPayment">
          <div class="row">
            <div class="col-md-4 col-sm-4 col-xs-12" *ngIf="bookingPayment"></div>
            <div class="col-md-8 col-sm-8 col-xs-12" *ngIf="bookingPayment">
              <div *ngFor=" let paymentMethod of restaurant?.payment_methods;">
                <div class="form-check-label fw-bold col-12 p-1 pb-0 text-truncate text-dark"
                  *ngIf="paymentMethod.payment_status == 'Y' && paymentMethod.payment_method_name == 'Stripe'">
                  <!-- [(ngModel)]="order.payment_method" -->

                  <img src="./assets/payment-logo/card.png" class="px-1" alt="credit card tiffintom"
                    *ngIf="paymentMethod.payment_method_name=='Stripe'">
                  {{ (paymentMethod.payment_method_name!='Stripe')?paymentMethod.payment_method_name:'Credit Card'}}
                </div>
                <div *ngIf="paymentMethod.payment_method_name=='Stripe' && paymentMethod.payment_status == 'Y'"
                  class="px-3 col-12">
                  <div class="col-12 d-flex">
                    <div class="col-8 m-t-10 m-b-10 no-padding m-t-xs-0">
                      <img src="./assets/payment-logo//american-express-logo.png" alt="credit card tiffintom"
                        class="px-1">
                      <img src="./assets/payment-logo//visa-logo.png" alt="credit card tiffintom" class="px-1">
                      <img src="./assets/payment-logo//master-card-logo.png" alt="credit card tiffintom" class="px-1">
                      <img src="./assets/payment-logo//master.png" alt="credit card tiffintom" class="px-1">
                    </div>
                    <span class="col-4 btn bg-primary text-white align-items-end w-auto cursor" data-target="#cardModal"
                      (click)="addCard(cardModal)">
                      Add Card
                    </span>
                  </div>
                  <div class="pt-2" *ngFor="let stripeCustomer of stripeCustomers;let i = index;">
                    <label class="form-check-label fw-bold col-12 p-1 pb-0 text-truncate text-dark"
                      for="ccard_select_{{stripeCustomer.id}}">
                      <input [(ngModel)]="booking.card_id" type="radio" class="form-check-input"
                        name="credit_card_choose" [id]="'ccard_select_'+stripeCustomer.id" [value]="stripeCustomer.id"
                        [checked]="i==0">
                      <img src="./assets/payment-logo/visa-logo.png" class="px-1"
                        *ngIf="stripeCustomer.card_brand == 'visa'">
                      <img src="./assets/payment-logo/master-card-logo.png" class="px-1"
                        *ngIf="stripeCustomer.card_brand == 'MasterCard'">
                      <img src="./assets/payment-logo/american-express-logo.png" class="px-1"
                        *ngIf="stripeCustomer.card_brand == 'American Express'">
                      <img src="./assets/payment-logo/master.png" class="px-1"
                        *ngIf="stripeCustomer.card_brand != 'visa' && stripeCustomer.card_brand != 'MasterCard' && stripeCustomer.card_brand != 'American Express'">
                      {{ stripeCustomer.payment_method_name}}
                      XXXX-XXXXXXXX-{{stripeCustomer.card_number}} Valid till
                      {{stripeCustomer.exp_month}}/{{stripeCustomer.exp_year}}
                    </label>
                  </div>
                </div>

              </div>
              <div *ngIf="error">
                <span class="text-danger fw-bold">{{ error }}</span>
              </div>


              <div class="col-md-12 col-xs-12 d-md-flex pt-4 pb-5">
                <button class="btn btn-primary w-100 text-white cursor d-flex justify-content-between" value="Submit"
                  type="button" [disabled]="isBookingLoading" (click)="validateBooking();" id="submit"
                  *ngIf="restaurant.booking_status == 'Yes' && restaurant.restaurant_booktable == 'Yes'">
                  <span>Make a payment</span>
                  <div class="ms-2 spinner-border" style="width: 20px;height: 20px;" *ngIf="isBookingLoading"
                    role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <span>Total: {{
                    convertNumber(restaurant.booking_payment
                    == 'single' ?
                    restaurant.booking_amount :
                    restaurant.booking_amount *
                    booking.guest_count) }}
                  </span>
                </button>
              </div>

            </div>
          </div>

        </div>

      </form>
    </div>

  </div>

  <ng-template #cardModal let-modal>
    <div class="modal-header bg-primary text-white">
      <h4 class="modal-title" id="modal-basic-title">
        Add Your New Card
      </h4>
      <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
        (click)="modal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <form (submit)="handleForm($event)">
        <div #cardElement id="customCardElement" class="body-box-shadow py-3"
          style='height: 2.6em !important; padding: .8em !important;'>
          <!-- A Stripe Element will be inserted here. -->
        </div>

        <div *ngIf="Modelerror">
          <span class="text-danger">{{ Modelerror }}</span>
        </div>
        <button type="submit" [disabled]="isModelLoading" class="mt-3 btn orng_btn m-t-10 bg-primary text-white">
          <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
            role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          Add New Card
        </button>

      </form>
    </div>
    <div class="modal-footer">
      <!-- <button type="button" [disabled]="isModelLoading" class="btn orng_btn m-t-10 bg-primary text-white">
      Add New Card
    </button>

    <div class="ms-2 spinner-border text-primary" *ngIf="isModelLoading" role="status">
      <span class="visually-hidden">Loading...</span>
    </div> -->
    </div>
  </ng-template>

  <ng-template #otpModal let-modal>
    <div class="modal-header bg-primary text-white">
      <h4 class="modal-title" id="modal-basic-title">
        <span class="fw-bold"
          *ngIf="(restaurant?.site_setting?.booking_verify_type == 'phone' || restaurant?.site_setting?.booking_verify_type == 'both') && !user.phone_verify">Phone
        </span>
        <span class="fw-bold"
          *ngIf="restaurant?.site_setting?.booking_verify_type == 'both' && !user.phone_verify && !user.email_verify"> &
        </span>
        <span class="fw-bold"
          *ngIf="(restaurant?.site_setting?.booking_verify_type == 'both' || restaurant?.site_setting?.booking_verify_type == 'mail') && !user.email_verify">Email
        </span>
        Verify
      </h4>
    </div>
    <div class="modal-body">
      <p>Your otp has been sent on this
        <span class="fw-bold"
          *ngIf="(restaurant?.site_setting?.booking_verify_type == 'phone' || restaurant?.site_setting?.booking_verify_type == 'both') && !user.phone_verify">{{user.phone_number}}
        </span>
        <span class="fw-bold"
          *ngIf="restaurant?.site_setting?.booking_verify_type == 'both' && !user.phone_verify && !user.email_verify">
          OR
        </span>
        <span class="fw-bold"
          *ngIf="(restaurant?.site_setting?.booking_verify_type == 'both' || restaurant?.site_setting?.booking_verify_type == 'mail') && !user.email_verify">{{user.username}}
        </span>
        <a class="cursor" (click)="profileUpdate(profileModal)" class="u-1"
          *ngIf="(restaurant?.site_setting?.signup_verify_type == 'phone' || restaurant?.site_setting?.signup_verify_type=='both') && !user.phone_verify">
          Edit</a>
      </p>
      <div class="col-sm-12 col-xs-12 form-group"
        *ngIf="(restaurant?.site_setting?.booking_verify_type == 'phone' || restaurant?.site_setting?.booking_verify_type == 'both') && !user.phone_verify">
        <input type="text" class="form-control col-md-8" id="otp" name="otp" [(ngModel)]="verifyOtp"
          placeholder="Please enter phone verification code" />
      </div>
      <div class=" col-sm-12 col-xs-12 form-group mt-2"
        *ngIf="(restaurant?.site_setting?.booking_verify_type == 'both' || restaurant?.site_setting?.booking_verify_type == 'mail') && !user.email_verify">
        <input type="text" class="form-control col-md-8" id="email_otp" name="email_otp" [(ngModel)]="user.email_otp"
          placeholder="Please enter email verification code" />
      </div>
      <div *ngIf="Modelotperror">
        <span class="text-danger">{{ Modelotperror }}</span>
      </div>
    </div>
    <div class="modal-footer justify-content-between">
      <button type="button" [disabled]="isModelOtpLoading"
        class="btn btn-outline-primary bg-primary text-white text-start cursor" (click)="resendOtp()">
        Re-send
      </button>
      <div class="ms-2 spinner-border text-primary" *ngIf="isModelOtpLoading" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <button type="button" [disabled]="isModelOtpLoading" class="btn btn-outline-primary bg-primary cursor text-white"
        (click)="validateOtp()">
        submit
      </button>
    </div>
  </ng-template>

  <ng-template #profileModal let-modal>
    <div class="modal-header bg-primary text-white">
      <h4 class="modal-title" id="modal-basic-title">
        Profile Update
      </h4>
    </div>
    <div class="modal-body">
      <form nz-form class="login-form mx-auto mb-1" #userForm="ngForm"
        (ngSubmit)="userForm.form.valid && updateUser(userForm)">

        <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="false">
          <label class="col-md-4">First Name</label>
          <input type="text" class="form-control col-md-8" id="first_name" name="first_name"
            [(ngModel)]="user.first_name" required #first_name="ngModel"
            [ngClass]="{ 'is-invalid': userForm.submitted && first_name.invalid }" />
          <div class="invalid-feedback" *ngIf="userForm.submitted && first_name.invalid">
            <span *ngIf="first_name.errors.required">First Name is required</span>
          </div>
        </div>

        <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="false">
          <label class="col-md-4">Last Name</label>
          <input type="text" class="form-control col-md-8" id="last_name" name="last_name" [(ngModel)]="user.last_name"
            required #last_name="ngModel" [ngClass]="{ 'is-invalid': userForm.submitted && last_name.invalid }" />
          <div class="invalid-feedback" *ngIf="userForm.submitted && last_name.invalid">
            <span *ngIf="last_name.errors.required">Last Name is required</span>
          </div>
        </div>

        <div class="col-sm-12 col-xs-12 form-group mb-3">
          <label class="col-md-4">Phone Number</label>
          <input type="text" class="form-control col-md-8" (keypress)="keyPress($event)" minlength="10" maxlength="11"
            id="phone_number" name="phone_number" [(ngModel)]="user.phone_number" required #phone_number="ngModel"
            [ngClass]="{ 'is-invalid': userForm.submitted && phone_number.invalid }" />
          <div class="invalid-feedback" *ngIf="userForm.submitted && phone_number.invalid">
            <span *ngIf="phone_number.errors.required">Phone Number is required</span>
            <span *ngIf="phone_number.errors.minlength">Phone Number minimum 10 digit</span>
            <span *ngIf="phone_number.errors.maxlength">Phone Number minimum 11 digit</span>
          </div>
        </div>

        <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="false">
          <label class="col-md-4">Username</label>
          <input type="text" class="form-control col-md-8" id="username" name="username" [(ngModel)]="user.username"
            readonly />
        </div>

        <div *ngIf="ModelProfileerror">
          <span class="text-danger">{{ ModelProfileerror }}</span>
        </div>

        <div class="col-sm-12 col-xs-12 form-group">
          <button type=" button" [disabled]="isProfileLoading"
            class="btn btn-outline-primary bg-primary cursor text-white">
            <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isProfileLoading"
              role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            Update
          </button>
        </div>
      </form>
    </div>
  </ng-template>