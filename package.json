{"name": "tiffintom-individual", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --base-href=/dynamic-menu-templates/", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "~12.1.2", "@angular/cdk": "^12.1.4", "@angular/common": "~12.1.2", "@angular/compiler": "^12.1.4", "@angular/core": "~12.1.2", "@angular/forms": "~12.1.2", "@angular/localize": "^12.1.4", "@angular/platform-browser": "~12.1.2", "@angular/platform-browser-dynamic": "~12.1.2", "@angular/router": "~12.1.2", "@ng-bootstrap/ng-bootstrap": "^10.0.0", "@popperjs/core": "^2.9.2", "@types/crypto-js": "^4.0.2", "angularx-social-login": "^4.0.1", "bootstrap": "^5.2.3", "bs-stepper": "^1.7.0", "crypto-js": "^4.1.1", "jquery": "^3.7.1", "lottie-web": "^5.7.13", "ng-circle-progress": "^1.6.0", "ng-otp-input": "^1.8.1", "ngx-lottie": "^7.0.3", "ngx-paypal": "^8.0.0", "ngx-slick-carousel": "^0.5.1", "ngx-tabset": "^2.2.0", "ngx-toastr": "^14.1.0", "popper.js": "^1.16.1", "rxjs": "~6.6.0", "slick-carousel": "^1.8.1", "tslib": "^2.2.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.1.2", "@angular/cli": "~12.1.2", "@angular/compiler-cli": "^12.1.4", "@types/jasmine": "~3.8.0", "@types/node": "^12.11.1", "jasmine-core": "~3.8.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~4.3.2"}}