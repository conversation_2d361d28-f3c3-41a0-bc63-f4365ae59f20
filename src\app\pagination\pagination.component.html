<nav aria-label="Page navigation example">
  <ul class="pagination" *ngIf="false">
    <li class="page-item" [class.disabled]="current_page==1"><a class="page-link"
        (click)="pageClick('prev')">Previous</a></li>
    <ng-container *ngFor="let o of counter(total_pages);let page = index">

      <li class="page-item" [class.active]="page+1==current_page"><a class="page-link"
          (click)="pageClick(page+1)">{{page+1}}</a></li>
    </ng-container>
    <li class="page-item" [class.disabled]="current_page==total_pages"><a class="page-link"
        (click)="pageClick('next')">Next</a></li>
  </ul>
  <ul class="pagination">
    <!-- <li class="page-item" [ngClass]="{disabled:current_page === 1}">
            <a class="page-link" (click)="setPage(1)">First</a>
        </li> -->
    <li class="page-item" [ngClass]="{disabled:current_page === 1}">
      <a class="page-link" (click)="setPage(current_page - 1)">Previous</a>
    </li>
    <li class="page-item" *ngFor="let page of pagedItems;" [ngClass]="{active:current_page === page.id}">
      <a class="page-link" (click)="setPage(page.id)">{{page.id}}</a>
    </li>
    <li class="page-item" [ngClass]="{disabled:current_page === total_pages}">
      <a class="page-link" (click)="setPage(current_page + 1)">Next</a>
    </li>
    <!-- <li class="page-item" [ngClass]="{disabled:current_page === total_pages}">
            <a class="page-link" (click)="setPage(total_pages)">Last</a>
        </li> -->
  </ul>
</nav>