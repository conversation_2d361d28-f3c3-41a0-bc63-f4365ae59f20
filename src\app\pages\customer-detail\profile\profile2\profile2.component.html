<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-12 pb-3">
      <h4>Profile Details</h4>
    </div>

    <div class="col-12 mb-2">
      <div class="col-12 border border body-box-shadow p-4" style="border-radius: 1rem;">
        <form nz-form class="login-form mx-auto" #userForm="ngForm"
          (ngSubmit)="userForm.form.valid && updateUser(userForm)">
          <div class="col-md-12 alert alert-danger alert-dismissible fade show" role="alert" *ngIf="errorMessage">
            {{ errorMessage }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>

          <div class="row">
            <div class="col-12 col-sm-6 form-group mb-3">
              <label class="col-md-4">First Name</label>
              <input type="text" class="form-control col-md-8" id="first_name" name="first_name"
                [(ngModel)]="user.first_name" required #first_name="ngModel"
                [ngClass]="{ 'is-invalid': userForm.submitted && first_name.invalid }" />
              <div class="invalid-feedback" *ngIf="userForm.submitted && first_name.invalid">
                <span *ngIf="first_name.errors.required">First Name is required</span>
              </div>
            </div>
            <div class="col-12 col-sm-6 form-group mb-3">
              <label class="col-md-4">Last Name</label>
              <input type="text" class="form-control col-md-8" id="last_name" name="last_name"
                [(ngModel)]="user.last_name" required #last_name="ngModel"
                [ngClass]="{ 'is-invalid': userForm.submitted && last_name.invalid }" />
              <div class="invalid-feedback" *ngIf="userForm.submitted && last_name.invalid">
                <span *ngIf="last_name.errors.required">Last Name is required</span>
              </div>
            </div>
            <div class="col-12 col-sm-6 form-group mb-3">
              <label class="col-md-4">Phone Number</label>
              <input type="text" class="form-control col-md-8" (keypress)="keyPress($event)" minlength="10"
                maxlength="11" id="phone_number" name="phone_number" [(ngModel)]="user.phone_number" required
                #phone_number="ngModel" [ngClass]="{ 'is-invalid': userForm.submitted && phone_number.invalid }" />
              <div class="invalid-feedback" *ngIf="userForm.submitted && phone_number.invalid">
                <span *ngIf="phone_number.errors.required">Phone Number is required</span>
                <span *ngIf="phone_number.errors.minlength">Phone Number minimum 10 digit</span>
                <span *ngIf="phone_number.errors.maxlength">Phone Number minimum 11 digit</span>
              </div>
            </div>
            <div class="col-12 col-sm-6 form-group mb-3">
              <label class="col-md-4">Username</label>
              <input type="text" class="form-control col-md-8" id="username" name="username" [(ngModel)]="user.username"
                readonly />
            </div>
            <div class="col-12 col-sm-6 form-group mb-3">
              <label class="col-md-4">image</label>
              <input #imageInput type="file" class="form-control col-md-8" id="image" name="image"
                [(ngModel)]="user.imageFile" (change)="onFileChanged($event)" />
            </div>
            <div class="col-12 form-group">
              <button type=" button" [disabled]="isProfileLoading" class="update-btn">
                <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isProfileLoading"
                  role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                Update
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <div class="col-12  my-2">
      <div class="col-12 border border body-box-shadow p-4" style="border-radius: 1rem;">
        <form nz-form class="login-form mx-auto" #changePasswordForm="ngForm"
          (ngSubmit)="changePasswordForm.form.valid && changePassword(changePasswordForm)">
          <div class="col-md-12 alert alert-danger alert-dismissible fade show" role="alert"
            *ngIf="errorChangePassword">
            {{ errorChangePassword }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
          <div class="row">
            <div class="col-12 form-group mb-3">
              <label class="col-md-4">Current Password</label>
              <input type="password" class="form-control col-md-8" id="current_password" name="current_password"
                [(ngModel)]="user.current_password" required #current_password="ngModel"
                [ngClass]="{ 'is-invalid': changePasswordForm.submitted && current_password.invalid }" />
              <div class="invalid-feedback" *ngIf="changePasswordForm.submitted && current_password.invalid">
                <span *ngIf="current_password.errors.required">Current Password is required</span>
              </div>
            </div>
            <div class="col-12 col-sm-6 form-group mb-3">
              <label class="col-md-4">New Password</label>
              <input type="password" class="form-control col-md-8" id="password" name="password"
                [(ngModel)]="user.password" required #password="ngModel"
                [ngClass]="{ 'is-invalid': changePasswordForm.submitted && password.invalid }" />
              <div class="invalid-feedback" *ngIf="changePasswordForm.submitted && password.invalid">
                <span *ngIf="password.errors.required">New Password is required</span>
              </div>
            </div>
            <div class="col-12 col-sm-6 form-group mb-3">
              <label class="col-md-4">Confirm Password</label>
              <input type="password" class="form-control col-md-8" id="confirmPassword" name="confirmPassword"
                [(ngModel)]="user.confirmPassword" required #confirmPassword="ngModel"
                [ngClass]="{ 'is-invalid': changePasswordForm.submitted && confirmPassword.invalid }" />
              <div class="invalid-feedback" *ngIf="changePasswordForm.submitted && confirmPassword.invalid">
                <span *ngIf="confirmPassword.errors.required">Confirm Password is required</span>
              </div>
            </div>
            <div class="col-12 form-group">
              <button type=" button" [disabled]="isChangePasswordLoading" class="change-pass-btn">
                <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;"
                  *ngIf="isChangePasswordLoading" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                Change Password
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<ng-template #addressModal let-modal>
  <div class="address-modal p-3">
    <div class="address-modal-header">
      <button class="address-close-btn" (click)="modal.dismiss('Cross click')">
        <i class="fas fa-times"></i>
      </button>
      <h4 class="address-modal-title text-primary fw-bold" id="modal-basic-title">
        Add New Deliver Address
      </h4>
    </div>
    <div class="modal-body">
      <div class="col-sm-12 col-xs-12 form-group">
        <label class="col-md-4">PostCode</label>
        <div class="col-sm-12 col-xs-12 d-flex justify-content-between">
          <input type="text" class="form-control col-md-5 col-xs-12 w-50" id="zipcode" name="zipcode"
            [(ngModel)]="addressBookAdd.zipcode" required />
          <button class="address-modal-close p-0 px-4" [disabled]="isPostcodeLoading"
            (click)="findzipcode(addressBookAdd.zipcode)">
            Find Address
          </button>
        </div>
        <div *ngIf="Postcodeerror">
          <span class="text-danger">{{ Postcodeerror }}</span>
        </div>
      </div>
      <div class="col-sm-12 col-xs-12 form-group">
        <label class="col-md-4">Address Title</label>
        <input type="text" class="form-control col-md-8" id="title" name="title" [(ngModel)]="addressBookAdd.title" />
      </div>
      <div class="col-sm-12 col-xs-12 form-group">
        <label class="col-md-4">Door no / Flat no</label>
        <input type="text" class="form-control col-md-8" id="flat_no" name="flat_no"
          [(ngModel)]="addressBookAdd.flat_no" required />
      </div>
      <div class="col-sm-12 col-xs-12 form-group">
        <label class="col-md-4">Address</label>
        <input type="text" class="form-control col-md-8" id="address" name="address"
          [(ngModel)]="addressBookAdd.address" required />
      </div>
      <input type="hidden" class="form-control col-md-8" id="latitude" name="latitude"
        [(ngModel)]="addressBookAdd.latitude" />
      <input type="hidden" class="form-control col-md-8" id="longitude" name="longitude"
        [(ngModel)]="addressBookAdd.longitude" />
      <div *ngIf="Modelerror">
        <span class="text-danger">{{ Modelerror }}</span>
      </div>
    </div>
    <button class="address-modal-close w-100" [disabled]="isModelLoading" (click)="validateAddress(addressModal)">
      Add Address
    </button>
  </div>
</ng-template>