import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BookingDetailComponent } from './booking-detail.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgxPayPalModule } from 'ngx-paypal';
import { LottieModule } from 'ngx-lottie';

const routes: Routes = [{ path: '', component: BookingDetailComponent }];

export function playerFactory() {
  return import(/* webpackChunkName: 'lottie-web' */ 'lottie-web');
}

@NgModule({
  declarations: [BookingDetailComponent],
  imports: [RouterModule.forChild(routes), SharedModule, NgbModule, NgxPayPalModule, LottieModule.forRoot({ player: playerFactory })],
})
export class BookingDetailModule { }
