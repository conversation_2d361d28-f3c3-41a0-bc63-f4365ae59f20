import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Location } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class LocationService {

  constructor(private router: Router, private location: Location) { }

  back(url: string) {
    if (history.length <= 2 || history.length == null) {
      this.router.navigateByUrl(url);
    } else {
      this.location.back();
    }
  }
}
