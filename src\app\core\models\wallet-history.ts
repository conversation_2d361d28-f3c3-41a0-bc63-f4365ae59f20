export class WalletHistory {
  id: string;
  customer_id: string;
  purpose: string;
  transaction_type: string;

  transaction_details: string;
  amount: number;
  status: boolean = true;

  created: string;
  modified: string;

  static toFormData(voucher: WalletHistory) {
    const formData = new FormData();

    if (voucher.customer_id) formData.append('customer_id', voucher.customer_id);
    if (voucher.purpose) formData.append('purpose', voucher.purpose);
    if (voucher.transaction_type) formData.append('transaction_type', voucher.transaction_type);
    if (voucher.transaction_details) formData.append('transaction_details', voucher.transaction_details);
    if (voucher.amount) formData.append('amount', voucher.amount.toString());

    return formData;
  }

}
