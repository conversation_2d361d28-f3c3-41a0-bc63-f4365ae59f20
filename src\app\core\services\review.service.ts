import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorHandler } from 'src/app/shared/error-handler';
import { environment } from 'src/environments/environment';
import { Review } from '../models/review';

@Injectable({
  providedIn: 'root',
})
export class ReviewService {
  private url = environment.apiBaseUrl + 'reviews/';
  public carts: Review[] = []
  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.restaurant_id) params = params.set('restaurant_id', options.restaurant_id);
    if (options.customer_id) params = params.set('customer_id', options.customer_id);
    if (options.status) params = params.set('status', options.status);
    if (options.query) params = params.set('query', options.query);
    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<Review> {
    return this.http.get<Review>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(review: Review): Observable<any> {
    return this.http.post<Review>(this.url, Review.toFormData(review))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(review: Review): Observable<any> {
    return this.http.post<Review>(this.url + review.id, Review.toFormData(review))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<any> {
    return this.http.delete<Review>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  // New Functions
  count(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.restaurant_id) params = params.set('restaurant_id', options.restaurant_id);

    return this.http.get<any>(`${this.url + 'count'}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }
}
