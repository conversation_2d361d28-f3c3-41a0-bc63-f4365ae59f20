import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { AddressBook } from 'src/app/core/models/address-book';
import { AddressBookService } from 'src/app/core/services/address-book.service';
import { NotificationService } from 'src/app/core/services/notification.service';
import { MessagingService } from 'src/app/core/services/messaging.service';

@Component({
  selector: 'app-address2',
  templateUrl: './address2.component.html',
  styleUrls: ['./address2.component.scss']
})
export class Address2Component implements OnInit {
  subs = new Subscription();
  isLoading = false;
  error = null;

  isModelLoading = false;
  Modelerror = null;

  isPostcodeLoading = false;
  Postcodeerror = null;

  user: User;
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;
  previousPage: any;

  restaurant: Restaurant = new Restaurant();
  addressBooks: AddressBook[] = [];
  addressBook: AddressBook = new AddressBook();
  addressBookAdd: AddressBook;

  options = { query: null, page: 1, per_page: 10, customer_id: null };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private currencyPipe: CurrencyPipe,
    private restaurantService: RestaurantService,
    private addressBookService: AddressBookService,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef,
    private messagingService: MessagingService,
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    this.options.customer_id = user?.id;
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    this.modalOptions = {
      backdrop: 'static',
      size: 'lg',
      backdropClass: 'customBackdrop',
      windowClass: 'rounded-modal'
    };
    this.fetchRestaurant();
    this.fetchAddresses();
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
        this.cdr.detectChanges();
      }, err => this.error = err)
    );
  }


  fetchAddresses() {
    this.subs.add(
      this.addressBookService
        .get({ customer_id: this.userId, nopaginate: "1" })
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.addressBooks = res;
            this.cdr.detectChanges();
          },
          (err) => {
            this.addressBooks = [];
          }
        )
    );
  }

  addAddress(model) {
    this.addressBookAdd = new AddressBook();
    this.openModal(model);
  }

  deleteAddress(addressBook: AddressBook) {
    this.isLoading = true; this.error = null;

    this.subs.add(this.addressBookService.delete(addressBook.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.fetchAddresses();
        this.messagingService.success("Address deleted successfully !!");
      }, err => { this.error = err; })
    );
  }

  findzipcode(postcode: string) {
    this.isPostcodeLoading = true;
    this.Postcodeerror = false;

    if (postcode == null || postcode == undefined) {
      this.Postcodeerror = 'Please enter valid postcode and press lookup button';
    } else {
      this.subs.add(this.userService.postcode(postcode)
        .pipe(finalize(() => this.isPostcodeLoading = false))
        .subscribe(res => {
          var address = '';
          if (res.street) {
            address += res.street;
          }
          if (res.post_town) {
            address += ',' + res.post_town;
          }
          if (res.post_code) {
            address += ',' + res.post_code;
          }

          this.addressBookAdd.latitude = res.latitude;
          this.addressBookAdd.longitude = res.longitude;
          this.addressBookAdd.zipcode = res.post_code;
          this.addressBookAdd.address = address;
        }, err => this.Postcodeerror = err)
      );
    }
  }

  validateAddress() {
    this.isModelLoading = true;
    this.Modelerror = false;

    if (this.addressBookAdd.zipcode == null) {
      this.Modelerror = 'Please enter valid postcode and press lookup button';
      this.isModelLoading = false;
    } else if (this.addressBookAdd.flat_no == null) {
      this.Modelerror = 'Please enter Flat no';
      this.isModelLoading = false;
    } else if (this.addressBookAdd.address == null) {
      this.Modelerror = 'Please enter valid postcode';
      this.isModelLoading = false;
    } else if (this.addressBookAdd.latitude == null) {
      this.Modelerror = 'Please enter valid postcode and press lookup button';
      this.isModelLoading = false;
    } else {
      this.addressBookAdd.user_id = this.userId;
      this.subs.add(
        this.addressBookService.create(this.addressBookAdd).
          pipe(finalize(() => this.isModelLoading = false))
          .subscribe(
            (res) => {
              this.fetchAddresses();
              this.messagingService.success("Address added successfully !!");
              this.modalService.dismissAll();
            },
            (err) => {
              this.Modelerror = err;
            }
          )
      )
    }
  }

  openModal(model) {
    this.Modelerror = null;
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
