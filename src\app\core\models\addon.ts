import { SubAddon } from './subaddon';

export class Addon {
  admin_name: string; //: "Pizzas"
  category_id: string; //: 6312
  created: string; //: "2021-02-26 10:35:56"
  delete_status: string; //: "N"
  id: string; //: 4188
  mainaddons_count: number; //: 1
  mainaddons_mini_count: number; //: 0
  mainaddons_name: string; //: "Base:"
  modified: string; //: "2021-02-26 10:35:56"
  restaurant_id: string; //: 3
  sortorder: string; //: 0
  status: string; //: "1"
  sub_addons: SubAddon[];
  //for local use only
  selected: boolean = false;
  selectedSubAddonId: string;
  max_error: boolean = false;
  min_error: boolean = false;
}
