<!-- <ng-template #noTheme> -->
<div class="loader-bg" *ngIf="isCheckoutLoading">
  <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
    <span class="visually-hidden text-center">Loading...</span>
  </div>
</div>

<div class="container mt-2 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-xs-6 col-sm-6 mt-2">
      <h5 class="col-sm-12 col-xs-12">Payment Method</h5>

      <div class="bg-white p-3 body-box-shadow rounded"
        *ngIf="restaurant?.site_setting?.wallet_available == 'Yes' && restaurant.wallet_payment == 'Yes'">
        <h5 class="col-sm-12 col-xs-12">My Wallet</h5>
        <div class="col-sm-12 col-md-12 col-xs-12 p-2">
          <label class="col-8" for="use_wallet">
            <input type="checkbox" (change)="walletAdd($event)" [(ngModel)]="order.payment_wallet"
              [value]="order.payment_wallet" [disabled]="user.wallet_amount <= 0" class="col-2 px-1" id="use_wallet"
              style="width: 20px;height: 20px;">
            <span class="col-sm-12 text-center">
              <img src="./assets/wallet.png" class="px-2">
              <strong>My Wallet</strong> Current Balance <strong>{{convertNumber(user.wallet_amount)}}</strong>
            </span>
          </label>
          <span class="col-5 text-center" style="font-size:small" *ngIf="order.wallet_amount >0" class="px-2">
            <strong>Paid From</strong> Wallet <strong>{{convertNumber(order.wallet_amount)}}</strong>
          </span>
        </div>
      </div>

      <div class="bg-white p-3 mt-2 body-box-shadow rounded"
        [class.d-none]="(order.payment_wallet && user.wallet_amount >= order.order_grand_total + order.charity_amount)">
        <div *ngFor=" let paymentMethod of restaurant?.payment_methods;">
          <label class="form-check-label fw-bold col-12 p-1 pb-0 text-truncate text-dark"
            *ngIf="paymentMethod.payment_status == 'Y'" for="inlineRadio{{paymentMethod.id}}">
            <input [(ngModel)]="order.payment_method" type="radio" class="form-check-input cursor"
              name="checkout_address" [id]="'inlineRadio'+paymentMethod.id" [value]="paymentMethod.payment_method_name"
              (click)="checkPaypalMin(paymentMethod.payment_method_name)"
              [disabled]="(order.order_sub_total <= restaurant.paypal_minimum_order && (paymentMethod.payment_method_name == 'Paypal') || (order.order_sub_total <= restaurant.card_minimum_order && paymentMethod.payment_method_name == 'Stripe'))? '' : null">

            <img src="./assets/payment-logo/paypal-small-logo.png" class="px-1" alt="paypal tiffintom"
              *ngIf="paymentMethod.payment_method_name=='Paypal'">
            <img src="./assets/payment-logo/cod2.png" class="px-1" alt="cod tiffintom"
              *ngIf="paymentMethod.payment_method_name=='COD' || paymentMethod.payment_method_name=='cod'">
            <img src="./assets/payment-logo/card.png" class="px-1" alt="credit card tiffintom"
              *ngIf="paymentMethod.payment_method_name=='Stripe'">
            {{ (paymentMethod.payment_method_name!='Stripe')?paymentMethod.payment_method_name:'Credit Card'}}

            <span class="text-danger fw-bold px-1"
              *ngIf="order.order_sub_total <= restaurant.paypal_minimum_order && paymentMethod.payment_method_name == 'Paypal'">
              Minimum {{ convertNumber(restaurant.paypal_minimum_order) }} Order required</span>
            <span class="text-danger fw-bold px-1"
              *ngIf="order.order_sub_total <= restaurant.card_minimum_order && paymentMethod.payment_method_name == 'Stripe'">
              Minimum {{ convertNumber(restaurant.card_minimum_order) }} Order required</span>
          </label>
          <div
            *ngIf="paymentMethod.payment_method_name=='Stripe' && paymentMethod.payment_status == 'Y' && order.payment_method=='Stripe'"
            class="px-3 col-12">
            <div class="col-12 d-flex">
              <div class="col-8 m-t-10 m-b-10 no-padding m-t-xs-0">
                <img src="./assets/payment-logo//american-express-logo.png" alt="credit card tiffintom" class="px-1">
                <img src="./assets/payment-logo//visa-logo.png" alt="credit card tiffintom" class="px-1">
                <img src="./assets/payment-logo//master-card-logo.png" alt="credit card tiffintom" class="px-1">
                <img src="./assets/payment-logo//master.png" alt="credit card tiffintom" class="px-1">
              </div>
              <span class="col-4 btn bg-primary text-white align-items-end w-auto cursor" data-target="#cardModal"
                (click)="addCard(cardModal)" *ngIf="stripeCustomers.length < 3">
                Add Card
              </span>
            </div>
            <div *ngFor=" let stripeCustomer of stripeCustomers;let i = index;">
              <label class="form-check-label fw-bold col-12 p-1 pb-0 text-truncate text-dark"
                for="ccard_select_{{stripeCustomer.id}}">
                <input [(ngModel)]="order.card_id" type="radio" class="form-check-input" name="credit_card_choose"
                  [id]="'ccard_select_'+stripeCustomer.id" [value]="stripeCustomer.id" [checked]="i==0">
                <img src="./assets/payment-logo/visa-logo.png" class="px-1" *ngIf="stripeCustomer.card_brand == 'visa'">
                <img src="./assets/payment-logo/master-card-logo.png" class="px-1"
                  *ngIf="stripeCustomer.card_brand == 'MasterCard'">
                <img src="./assets/payment-logo/american-express-logo.png" class="px-1"
                  *ngIf="stripeCustomer.card_brand == 'American Express'">
                <img src="./assets/payment-logo/master.png" class="px-1"
                  *ngIf="stripeCustomer.card_brand != 'visa' && stripeCustomer.card_brand != 'MasterCard' && stripeCustomer.card_brand != 'American Express'">
                {{ stripeCustomer.payment_method_name}}
                XXXX-XXXXXXXX-{{stripeCustomer.card_number}} Valid till
                {{stripeCustomer.exp_month}}/{{stripeCustomer.exp_year}}
              </label>
            </div>
          </div>

        </div>
        <div *ngIf="errorPaymentMethod">
          <span class="text-danger fw-bold">{{ errorPaymentMethod }}</span>
        </div>
      </div>

      <div class="bg-white p-3 mt-2 body-box-shadow rounded" *ngIf="order.order_type == 'delivery'">
        <h5 class="col-12">Your Delivery Address</h5>
        <div class="col-12 text-dark py-2">{{order.address}}</div>
        <div *ngIf="restaurant.driver_tip">
          <h5 class="col-12">Driver Tip</h5>
          <span class="btn text-white rounded bg-primary col-2 m-2 w-auto cursor"
            (click)="driverTip(1)">{{convertNumber(1)}}</span>
          <span class="btn text-white rounded bg-primary col-2 m-2 w-auto cursor"
            (click)="driverTip(2)">{{convertNumber(2)}}</span>
          <span class="btn text-white rounded bg-primary col-2 m-2 w-auto cursor"
            (click)="driverTip(5)">{{convertNumber(5)}}</span>
          <span class="text-start fw-bold col-4 p-3">Tip: {{convertNumber(order.driver_tip)}}</span>
          <span class="text-end fw-bold col-2 p-3" (click)="driverTip(0)">
            <img src="./assets/icons/x-mark.png" alt="clear driver tip tiffintom">
          </span>
        </div>
      </div>

    </div>

    <div class="col-md-6 mt-2">
      <h5 class=" text-center"> Your {{order.order_type | titlecase}} Order</h5>
      <div class="bg-white body-box-shadow rounded p-2">
        <div class="text-center">

          <div class=" col-12 d-flex order-types" *ngIf="false">
            <div class="fw-bold col-6 p-2 h-100 cursor rounded-start" (click)="orderType('pickup')"
              [ngClass]="{'bg-primary text-white' : order.order_type == 'pickup'}">
              <a>
                Pickup
                <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_pickup != 'Yes'">Unavailable</p>
              </a>
            </div>
            <div class="fw-bold col-6 p-2 h-100 cursor rounded-end" (click)="orderType('delivery')"
              [ngClass]="{'bg-primary text-white' : order.order_type == 'delivery'}">
              <a>
                Delivery
                <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_delivery != 'Yes'">Unavailable</p>
              </a>
            </div>
          </div>

          <div class="col-12 text-primary fw-bold pb-2 border-bottom">
            You have a {{order?.carts?.length > 0 ? order?.carts?.length :''}} items
          </div>

          <div class="card-body cart p-0" *ngIf="order?.carts?.length != 0"
            style="max-height: calc(100vh - 75vh);overflow-y: auto;">
            <div class="col-12 text-start" *ngIf="order?.carts?.length > 0">
              <div class="col-12 d-flex p-2 fw-bold border-bottom bg-light">
                <div class="col-9">Item Name</div>
                <div class="col-1 text-center">Qty</div>
                <div class="col-2 text-end">Total</div>
              </div>
              <div class="fw-bold p-1 col-12  border-bottom" *ngFor=" let cart of order.carts; let i=index">
                <div class="col-12 d-flex p-1">
                  <div class="col-9">
                    <div class="w-100 ml-1">{{cart.menu_name}}</div>
                    <div class="w-100" style="font-size: 12px;color: #999;">{{cart.subaddons_name}}</div>
                  </div>
                  <span class="col-1 text-center">{{cart.quantity}}</span>
                  <span class="col-2 text-end">{{convertNumber(cart.total_price)}}</span>
                </div>
              </div>
              <div class="col-12 d-flex p-2 fw-bold border-bottom bg-light" *ngIf="order?.applied_offers?.length > 0">
                <div class="col-12 text-center">Offer Items</div>
              </div>
              <div class="fw-bold p-1 col-12  border-bottom"
                *ngFor=" let appliedOffer of order.applied_offers; let i=index">
                <div class="col-12 d-flex p-1">
                  <div class="col-9">
                    <div class="w-100 ml-1">{{appliedOffer?.menu_name}}</div>
                    <div class="w-100" style="font-size: 12px;color: #999;">{{appliedOffer?.subaddons_name}}</div>
                  </div>
                  <span class="col-1 text-center">{{appliedOffer.quantity}}</span>
                  <span class="col-2 text-end">
                    {{appliedOffer.total_price==0?'Free':convertNumber(appliedOffer.total_price)}}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom">
            <span class="col-10 text-start">Sub Total</span>
            <span class="col-2 text-end">{{convertNumber(order.order_sub_total)}}</span>
          </div>

          <div *ngIf="surcharges.length > 0">
            <div class="col-12 p-2 d-flex justify-content-between border-bottom" *ngFor="let surcharge of surcharges">
              <span class="col-10 text-start">{{surcharge.surcharge_name}}</span>
              <span class="col-2 text-end">{{convertNumber(surcharge.surcharge_amount)}}</span>
            </div>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom" *ngIf="order.service_charge > 0">
            <span class="col-10 text-start">Service Charge</span>
            <span class="col-2 text-end">{{convertNumber(order.service_charge)}}</span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom"
            *ngIf="order.order_type == 'delivery' && order.delivery_charge > 0">
            <span class="col-10 text-start">Delivery Charge</span>
            <span class="col-2 text-end">{{convertNumber(order.delivery_charge)}}</span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between fw-bold border-bottom">
            <span class="col-10 text-start">Total Amount</span>
            <span class="col-2 text-end">{{convertNumber(order.order_sub_total + order.delivery_charge +
              surchargeAmount + order.service_charge)}}</span>
          </div>

          <div class="col-12 d-flex justify-content-between py-2" *ngIf="!order.voucher_amount">
            <input type="text" class="form-control w-100 mx-2" id="voucher_code" name="voucher_code"
              placeholder="Apply Coupon Code" [(ngModel)]="order.voucher_code" />
            <span class="btn bg-primary text-white align-items-right cursor" (click)="voucherCheck()">
              submit
            </span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="order.voucher_code && order.voucher_amount > 0">
            <span class="col-9 text-start">Voucher ({{order.voucher_code}})</span>
            <span class="col-3 text-end cursor">
              (-) {{convertNumber(order.voucher_amount)}}
              <img src="./assets/icons/x-mark.png" (click)="voucherRemove()" class="px-1" alt="clear voucher tiffintom">
            </span>
          </div>
          <span class="col-12 text-danger fw-bold p-1" *ngIf="errorVoucher">{{ errorVoucher }}</span>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="restaurant?.site_setting?.charity_message != '' && restaurant?.site_setting?.charity_amount > 0">
            <span class="col-9 text-start">
              <label class="col-12" for="use_charity">
                <input type="checkbox" (change)="charityAdd($event)" class="col-2 px-1" id="use_charity"
                  style="width: 15px;height: 15px;">
                {{restaurant?.site_setting?.charity_message}}
              </label>
            </span>
            <span class="col-3 text-end">
              {{convertNumber(restaurant.site_setting.charity_amount)}}
            </span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="order.rewardPoint || order.rewardPercentage > 0">
            <span class="col-9 text-start">
              <label class="col-12" for="use_redeem">
                <input type="checkbox" (change)="redeemAdd($event)" class="col-2 px-1" id="use_redeem"
                  style="width: 15px;height: 15px;">
                Redeem Amount ({{order.rewardPercentage}} %)
              </label>
            </span>
            <span class="col-3 text-end">
              (-) {{convertNumber(order.rewardPoint)}}
            </span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="order.offer_amount > 0">
            <span class="col-9 text-start">Offer {{order.offer_percentage?'('+order.offer_percentage+'%)':''}}</span>
            <span class="col-3 text-end">
              (-) {{convertNumber(order.offer_amount)}}
            </span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="order.wallet_amount > 0">
            <span class="col-9 text-start">
              <img src="./assets/wallet.png" class="px-2">
              My Wallet </span>
            <span class="col-3 text-end">
              (-) {{convertNumber(order.wallet_amount)}}
            </span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between fw-bold">
            <span class="col-10 text-start">Payable Amount</span>
            <span class="col-2 text-end">{{convertNumber(getGrandTotal())}}</span>
          </div>

          <div class="col-12" *ngIf="error">
            <span class="text-danger fw-bold">{{ error }}</span>
          </div>

          <div class="col-md-12 pt-2"
            *ngIf="restaurant.paypal_minimum_order <= order.order_sub_total && order.delivery_time != 'Close' && restaurant.online_order == 'Yes' && order?.carts?.length != 0 && order.order_type == 'delivery' && order.payment_method== 'Paypal'">
            <ngx-paypal [config]="payPalConfig" class="pt-2"></ngx-paypal>
          </div>
          <div class="col-md-12 pt-2"
            *ngIf="order.delivery_time != 'Close' && restaurant.online_order == 'Yes' && order?.carts?.length != 0 && order.order_type == 'pickup' && order.payment_method== 'Paypal'">
            <ngx-paypal [config]="payPalConfig" class="pt-2"></ngx-paypal>
          </div>

          <div class="col-md-12 pt-2"
            *ngIf="order.delivery_time != 'Close' && restaurant.online_order == 'Yes' && order?.carts?.length != 0 && order.order_type == 'delivery' && (order.payment_method == 'Stripe' || order.payment_method == 'COD' || order.payment_method == 'cod')">
            <button class="btn bg-primary text-white w-100 fw-bold p-2 cursor"
              [disabled]="isCheckoutLoading || order.order_sub_total <= restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes'"
              (click)="validateCheckout()"
              [ngClass]="{'disabled': order.order_sub_total < restaurant.minimum_order,'disabled': restaurant.restaurant_delivery != 'Yes'}">
              <p class="m-0 d-flex justify-content-between fs-5"><span class="text-start">
                  {{ restaurant.currentStatus == 'Open' ? 'Total' : 'Pre Order'}}
                </span>
                <span class="ms-2 spinner-border text-white text-center" *ngIf="isCheckoutLoading" role=" status">
                  <span class="visually-hidden">Loading...</span>
                </span>
                <span class="spacing text-end">
                  {{convertNumber(getGrandTotal())}}
                </span>
              </p>
            </button>
          </div>

          <div class="col-md-12 pt-2"
            *ngIf="order.delivery_time != 'Close' && restaurant.online_order == 'Yes' && order?.carts?.length != 0 && order.order_type == 'pickup' && (order.payment_method == 'Stripe' || order.payment_method == 'COD' || order.payment_method == 'cod')">
            <button class="btn bg-primary text-white w-100 fw-bold p-2 cursor"
              [disabled]="isCheckoutLoading || restaurant.restaurant_pickup != 'Yes'" (click)="validateCheckout()"
              [ngClass]="{'disabled': restaurant.restaurant_pickup != 'Yes'}">
              <p class="m-0 d-flex justify-content-between fs-5"><span class="text-start">
                  {{ restaurant.currentStatus == 'Open' ? 'Total' : 'Pre Order' }}
                </span>
                <span class="ms-2 spinner-border text-white text-center" *ngIf="isCheckoutLoading" role=" status">
                  <span class="visually-hidden">Loading...</span>
                </span>
                <span class="spacing text-end">{{convertNumber(getGrandTotal())}}</span>
              </p>
            </button>
          </div>

          <div class="ms-2 spinner-border text-primary" *ngIf="isLoading" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>

          <div class="blink_me text-primary col-md-12 pt-2" style="text-align: initial;" *ngIf="order.order_point > 0">
            <img src="assets/trophy.png" class="img-fluid px-2">
            <span class="fw-bold">You will earn {{order.order_point}} Points</span>
          </div>

          <div class="col-sm-12 empty-cart-cls text-center" *ngIf="order?.carts?.length == 0">
            <img src="assets/cartitem.png" class="img-fluid mb-4 mr-3">
            <p><strong>No Item(s) Added</strong></p>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>
<!-- </ng-template> -->

<ng-template #loadingData>
  <div class="row text-center align-middle justify-content-center">
    <div class="col-md-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
</ng-template>

<ng-template #cardModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Add Your New Card
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form (submit)="handleForm($event)">
      <div #cardElement id="customCardElement" class="body-box-shadow py-3"
        style='height: 2.6em !important; padding: .8em !important;'>
        <!-- A Stripe Element will be inserted here. -->
      </div>

      <div *ngIf="Modelerror">
        <span class="text-danger">{{ Modelerror }}</span>
      </div>
      <button type="submit" [disabled]="isModelLoading" class="mt-3 btn orng_btn m-t-10 bg-primary text-white">
        <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
          role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        Add New Card
      </button>

    </form>
  </div>
  <div class="modal-footer">
    <!-- <button type="button" [disabled]="isModelLoading" class="btn orng_btn m-t-10 bg-primary text-white">
      Add New Card
    </button>

    <div class="ms-2 spinner-border text-primary" *ngIf="isModelLoading" role="status">
      <span class="visually-hidden">Loading...</span>
    </div> -->
  </div>
</ng-template>

<ng-template #multiplePriceOfferModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      You qualify for free below products, please select {{eligibleQty}}
    </h4>
    <!-- <button type="button" class="close bg-primary text-white" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button> -->
  </div>
  <div class="modal-body">
    <ul class="list-group border-0">
      <li class="bg-light list-group-item border-0 text-dark" style="margin: 0 0 10px 0;"
        *ngFor="let eligibleOffer of order.eligible_offers; let i = index">
        <div class="row">
          <div class="checkbox checkbox-inline col-9" style="padding: 0px 10px 0px;margin: 7px 0 2px;">
            <input type="checkbox" id="offer_{{i}}" (ngModelChange)="selectCheckBox(eligibleOffer,$event,i)"
              class="offer_{{i}}" [(ngModel)]="eligibleOffer.selected" name="offer_radio_{{i}}">
            <label for="offer_{{i}}" style="padding-left: 15px;">{{
              eligibleOffer?.menu_name }}</label>
          </div>
          <div class="col-3 pt-1 text-center d-flex">
            <div class="col-4 cursor" (click)="updateToOffer(eligibleOffer,'update',i)">
              <img src="./assets/icons/minus.png" alt="item plus icon tiffintom">
            </div>
            <div class="col-4 border rounded">{{eligibleOffer.quantity}}</div>
            <div class="col-4 cursor" (click)="updateToOffer(eligibleOffer,'add',i)">
              <img src="./assets/icons/plus.png" alt="item plus icon tiffintom">
            </div>
          </div>
        </div>
      </li>
    </ul>
    <div *ngIf="OfferModelerror">
      <span class="text-danger">{{ OfferModelerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isOfferModelLoading || !isEligible"
      class="btn orng_btn m-t-10 bg-primary text-white cursor" (click)="validateOffer()">
      <div class="ms-2 spinner-border text-white" *ngIf="isOfferModelLoading" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Add Product
    </button>

  </div>
</ng-template>

<ng-template #otpModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      <span class="fw-bold"
        *ngIf="(restaurant?.site_setting?.order_verify_type == 'phone' || restaurant?.site_setting?.order_verify_type == 'both') && !user.phone_verify">Phone
      </span>
      <span class="fw-bold"
        *ngIf="restaurant?.site_setting?.order_verify_type == 'both' && !user.phone_verify && !user.email_verify"> &
      </span>
      <span class="fw-bold"
        *ngIf="(restaurant?.site_setting?.order_verify_type == 'both' || restaurant?.site_setting?.order_verify_type == 'mail') && !user.email_verify">Email
      </span>
      Verify
    </h4>
  </div>
  <div class="modal-body">
    <p>Your otp has been sent on this
      <span class="fw-bold"
        *ngIf="(restaurant?.site_setting?.order_verify_type == 'phone' || restaurant?.site_setting?.order_verify_type == 'both') && !user.phone_verify">{{user.phone_number}}
      </span>
      <span class="fw-bold"
        *ngIf="restaurant?.site_setting?.order_verify_type == 'both' && !user.phone_verify && !user.email_verify"> OR
      </span>
      <span class="fw-bold"
        *ngIf="(restaurant?.site_setting?.order_verify_type == 'both' || restaurant?.site_setting?.order_verify_type == 'mail') && !user.email_verify">{{user.username}}
      </span>
      <a (click)="profileUpdate(profileModal)" class="u-1 cursor"
        *ngIf="(restaurant?.site_setting?.signup_verify_type == 'phone' || restaurant?.site_setting?.signup_verify_type=='both') && !user.phone_verify">Edit</a>
    </p>
    <div class=" col-sm-12 col-xs-12 form-group"
      *ngIf="(restaurant?.site_setting?.order_verify_type == 'phone' || restaurant?.site_setting?.order_verify_type == 'both') && !user.phone_verify">
      <input type="text" class="form-control col-md-8" id="otp" name="otp" [(ngModel)]="verifyOtp"
        placeholder="Please enter phone verification code" />
    </div>
    <div class=" col-sm-12 col-xs-12 form-group mt-2"
      *ngIf="(restaurant?.site_setting?.order_verify_type == 'both' || restaurant?.site_setting?.order_verify_type == 'mail') && !user.email_verify">
      <input type="text" class="form-control col-md-8" id="email_otp" name="email_otp" [(ngModel)]="user.email_otp"
        placeholder="Please enter email verification code" />
    </div>
    <div *ngIf="Modelotperror">
      <span class="text-danger">{{ Modelotperror }}</span>
    </div>
  </div>
  <div class="modal-footer justify-content-between">
    <button type="button" [disabled]="isModelOtpLoading"
      class="btn btn-outline-primary bg-primary text-white text-start cursor" (click)="resendOtp()">
      Re-send
    </button>
    <div class="ms-2 spinner-border text-primary" *ngIf="isModelOtpLoading" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <button type="button" [disabled]="isModelOtpLoading" class="btn btn-outline-primary bg-primary text-white cursor"
      (click)="validateOtp()">
      submit
    </button>
  </div>
</ng-template>

<ng-template #profileModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Profile Update
    </h4>
  </div>
  <div class="modal-body">
    <form nz-form class="login-form mx-auto mb-1" #userForm="ngForm"
      (ngSubmit)="userForm.form.valid && updateUser(userForm)">

      <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="false">
        <label class="col-md-4">First Name</label>
        <input type="text" class="form-control col-md-8" id="first_name" name="first_name" [(ngModel)]="user.first_name"
          required #first_name="ngModel" [ngClass]="{ 'is-invalid': userForm.submitted && first_name.invalid }" />
        <div class="invalid-feedback" *ngIf="userForm.submitted && first_name.invalid">
          <span *ngIf="first_name.errors.required">First Name is required</span>
        </div>
      </div>

      <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="false">
        <label class="col-md-4">Last Name</label>
        <input type="text" class="form-control col-md-8" id="last_name" name="last_name" [(ngModel)]="user.last_name"
          required #last_name="ngModel" [ngClass]="{ 'is-invalid': userForm.submitted && last_name.invalid }" />
        <div class="invalid-feedback" *ngIf="userForm.submitted && last_name.invalid">
          <span *ngIf="last_name.errors.required">Last Name is required</span>
        </div>
      </div>

      <div class="col-sm-12 col-xs-12 form-group mb-3">
        <label class="col-md-4">Phone Number</label>
        <input type="text" class="form-control col-md-8" (keypress)="keyPress($event)" minlength="10" maxlength="11"
          id="phone_number" name="phone_number" [(ngModel)]="user.phone_number" required #phone_number="ngModel"
          [ngClass]="{ 'is-invalid': userForm.submitted && phone_number.invalid }" />
        <div class="invalid-feedback" *ngIf="userForm.submitted && phone_number.invalid">
          <span *ngIf="phone_number.errors.required">Phone Number is required</span>
          <span *ngIf="phone_number.errors.minlength">Phone Number minimum 10 digit</span>
          <span *ngIf="phone_number.errors.maxlength">Phone Number minimum 11 digit</span>
        </div>
      </div>

      <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="false">
        <label class="col-md-4">Username</label>
        <input type="text" class="form-control col-md-8" id="username" name="username" [(ngModel)]="user.username"
          readonly />
      </div>

      <div *ngIf="ModelProfileerror">
        <span class="text-danger">{{ ModelProfileerror }}</span>
      </div>

      <div class="col-sm-12 col-xs-12 form-group">
        <button type=" button" [disabled]="isProfileLoading"
          class="btn btn-outline-primary bg-primary text-white cursor">
          <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isProfileLoading"
            role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          Update
        </button>
      </div>

    </form>
  </div>
  <!-- <div class="modal-footer justify-content-between">
    <div class="ms-2 spinner-border text-primary" *ngIf="isModelProfileLoading" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <button type="button" [disabled]="isModelProfileLoading" class="btn btn-outline-dark bg-primary text-white"
      (click)="validateProfile()">
      Save
    </button>
  </div> -->
</ng-template>

<ng-template #placeModal let-modal>
  <div class="modal-body">
    <ng-lottie height="340px" [options]="optionPlace" loop autoplay containerClass="moving-box another-class">
    </ng-lottie>
  </div>
</ng-template>

<ng-template #itemNotAvailableModal let-modal>
  <div class="modal-header bg-primary text-white text-center justify-content-center">
    <h4 class="modal-title" id="modal-basic-title">
      Not Available
    </h4>
  </div>

  <div class="modal-body">
    <!-- Message for only Order Type mismatch -->
    <div class="col-sm-12 col-xs-12 form-group mb-3 text-center fw-bold pt-2"
      *ngIf="hasOrderTypeMismatch && !hasDayMismatch">
      Some items in your cart are not available for the selected order type.
    </div>

    <!-- Message for only Day mismatch -->
    <div class="col-sm-12 col-xs-12 form-group mb-3 text-center fw-bold pt-2"
      *ngIf="hasDayMismatch && !hasOrderTypeMismatch">
      Some items are not available on the selected day.
    </div>

    <!-- Message for both mismatches -->
    <div class="col-sm-12 col-xs-12 form-group mb-3 text-center fw-bold pt-2"
      *ngIf="hasOrderTypeMismatch && hasDayMismatch">
      Some items are not available for the selected order type or delivery day.
    </div>

    <!-- Buttons -->
    <div class="row pt-3">
      <div class="col-sm-6 col-xs-6 form-group pt-3">
        <button type="button" class="btn btn-primary w-100" (click)="handleItemNotAvailable('no')"
          style="border-radius: 1cm; border: none;">
          No
        </button>
      </div>
      <div class="col-sm-6 col-xs-6 form-group pt-3">
        <button type="button" class="btn btn-primary w-100" (click)="handleItemNotAvailable('yes')"
          style="border-radius: 1cm; border: none;">
          Yes
        </button>
      </div>
    </div>
  </div>
</ng-template>