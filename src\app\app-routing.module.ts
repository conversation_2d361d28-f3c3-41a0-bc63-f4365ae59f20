import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './core/auth.guard';
import { CustomerDetailComponent } from './pages/customer-detail/customer-detail.component';
import { ShellComponent } from './shell/shell.component';

const routes: Routes = [
  { path: '', pathMatch: 'full', redirectTo: 'menu' },
  {
    path: '',
    component: ShellComponent,
    canActivate: [AuthGuard],
    canActivateChild: [AuthGuard],
    children: [
      { path: 'auth', loadChildren: () => import('./pages/auth/auth.module').then((m) => m.AuthModule), },
      { path: 'menu', loadChildren: () => import('./pages/menu/menu.module').then((m) => m.MenuModule), },
      { path: 'pre-checkout', loadChildren: () => import('./pages/pre-checkout/pre-checkout.module').then((m) => m.PreCheckoutModule), },
      { path: 'checkout', loadChildren: () => import('./pages/checkout/checkout.module').then((m) => m.CheckoutModule), },
      { path: 'order-detail/:id', loadChildren: () => import('./pages/order-detail/order-detail.module').then((m) => m.OrderDetailModule), },
      { path: 'booking-detail/:id', loadChildren: () => import('./pages/booking-detail/booking-detail.module').then((m) => m.BookingDetailModule), },
      { path: 'booking', loadChildren: () => import('./pages/booking-add/booking-add.module').then((m) => m.BookingAddModule), },
      { path: 'review', loadChildren: () => import('./pages/review/review.module').then((m) => m.ReviewModule), },
      { path: 'info', loadChildren: () => import('./pages/information/information.module').then((m) => m.InformationModule), },
      { path: 'customer-detail', component: CustomerDetailComponent, canActivateChild: [AuthGuard], canActivate: [AuthGuard], loadChildren: () => import('./pages/customer-detail/customer-detail.module').then((m) => m.CustomerDetailModule), },
    ],
  },
  { path: '**', pathMatch: 'full', redirectTo: '/' },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule { }
