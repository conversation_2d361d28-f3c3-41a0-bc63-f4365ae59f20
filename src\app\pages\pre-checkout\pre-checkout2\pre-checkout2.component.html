<div class="container mt-2 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">
    <!-- Address Details -->
    <div class="col-12 col-lg-6 mt-2">

      <ng-container *ngIf="order.order_type == 'delivery'">
        <div class="address-details">
          <h5 class="col-sm-12 col-xs-12 text-center">
            Delivery Address & Timings
          </h5>
          <div class="address-list">
            <ng-container *ngFor=" let addressBook of addressBooks; let i=index">
              <label class="form-check-label fw-bold col-12 p-1 text-truncate text-dark"
                for="inlineRadio{{addressBook.id}}" style="cursor: pointer;">
                <input [(ngModel)]="order.address_id" type="radio" class="form-check-input"
                  (ngModelChange)="showDeliveryCharge(addressBook)" name="checkout_address"
                  [id]="'inlineRadio'+addressBook.id" [value]="addressBook.id">
                {{addressBook.title}} , {{addressBook.flat_no != addressBook.flat_no?",": ''}}
                {{addressBook.address}}
              </label>
            </ng-container>

            <div *ngIf="errorAddress">
              <span class="text-danger fw-bold">{{ errorAddress }}</span>
            </div>
          </div>
          <button type="button" class="add-address-btn" data-target="#addressModal" data-toggle="modal"
            (click)="addAddress(addressModal)">
            <i class="fas fa-plus"></i>
            Add New Delivery Address
          </button>
        </div>
      </ng-container>

      <div class="delivery-details">
        <h5 class="mt-2 px-2">{{order.order_type | titlecase}} Details</h5>
        <div class="form-check py-1" *ngIf="restaurant.currentStatus == 'Open'">
          <label class="form-check-label px-2" for="deliverytime_now" style="cursor: pointer;">
            <input type="radio" class="form-check-input" id="deliverytime_now" [(ngModel)]="order.assoonas"
              (ngModelChange)="deliveryNow('now')" name="deliverytime" value="now">
            ASAP
          </label>
        </div>
        <div class="form-check py-1">
          <label class="form-check-label px-2" for="deliverytime_later" style="cursor: pointer;">
            <input type="radio" class="form-check-input" id="deliverytime_later" [(ngModel)]="order.assoonas"
              (ngModelChange)="deliveryNow('later')" name="deliverytime" value="later">
            Later
          </label>
        </div>

        <ng-container *ngIf="order.assoonas == 'later'">
          <div class="choose-delivery-time">
            <p class="m-0">Choose {{order.order_type | titlecase}} Time</p>
            <div class="p-2">
              <input type="text" name="delivery_time" id="delivery_time" [(ngModel)]="order.delivery_time" value="Close"
                *ngIf="timeSlots.length == 0" class="form-control bg-white" placeholder="Close" readonly />

              <select class="form-control delivery-time-select" [(ngModel)]="order.delivery_time" name="delivery_time"
                id="delivery_time" *ngIf="timeSlots.length > 0">
                <option disabled>Select Time Slot</option>
                <option *ngFor="let time of timeSlots;let j=idx" value="{{time}}" [selected]="j === 0">{{time}}</option>
              </select>
            </div>
          </div>
        </ng-container>

        <div *ngIf="errorTime">
          <span class="text-danger fw-bold">{{ errorTime }}</span>
        </div>
        <p class="p-1" style="font-size: 12px; color: #999;border-bottom: dashed 1px;">
          Note : Please note that the {{order.order_type}} time is not always guaranteed and may vary depending on
          traffic and product availability.
        </p>
        <p class="fw-bold">Any instructions for {{order.order_type}} (optional)</p>
        <textarea rows="4" class="border border-default w-100" style="border-radius: 1rem;padding: 10px;" nz-input
          [(ngModel)]="order.order_description" name="order_description" id="order_description"
          placeholder="Enter Your Comment Here"> </textarea>
      </div>

    </div>
    <!-- Order Details -->
    <div class="col-12 col-lg-6 mt-2">
      <div class="order-details">
        <div class="delivery-pickup-wrapper" *ngIf="false">
          <!-- Delivery -->
          <button (click)="orderType('delivery')" class="pickup-btn"
            [ngClass]="{'active': order.order_type == 'delivery'}">
            <i class="fas fa-car me-1"></i>Delivery
            <br>
            <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_delivery != 'Yes'">Unavailable</p>
          </button>
          <!-- Pickup -->
          <button (click)="orderType('pickup')" class="delivery-btn"
            [ngClass]="{'active': order.order_type == 'pickup'}">
            <i class="fas fa-shopping-bag me-1"></i>Pickup
            <br>
            <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_pickup != 'Yes'">Unavailable</p>
          </button>
        </div>

        <h5 class="text-center fw-medium"> Your {{order.order_type | titlecase}} Order</h5>
        <div class="text-primary text-center fw-bold pb-2 border-bottom">
          You have {{carts.length > 0 ? carts.length :''}} items
        </div>

        <ng-container *ngIf="carts && (carts.length > 0);else noItem">
          <div class="cart-items-list">
            <!-- Cart Items -->
            <ng-container *ngFor="let cart of carts; let i = index">
              <div class="cart-item">
                <div class="d-flex flex-column">
                  <span class="item-name">{{ cart.menu_name }}</span>
                  <span class="text-muted item-description" style="font-size: 12px; color: #999;">
                    {{ cart.subaddons_name }}
                  </span>
                </div>
                <div class=" d-flex align-items-center justify-content-between">
                  <div class="quantity-controls">
                    <button class="delete-btn" (click)="updateToCart(cart, 'remove')">
                      <i class="fas fa-minus"></i>
                    </button>
                    <span>{{ cart.quantity }}</span>
                    <button class="add-btn" (click)="updateToCart(cart, 'add')">
                      <i class="fas fa-plus"></i>
                    </button>
                  </div>
                  <div class="quantity-controls">
                    <span class="item-total">{{ convertNumber(cart.total_price) }}</span>
                    <button class="delete-btn" (click)="updateToCart(cart, 'delete')">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </div>
              </div>
            </ng-container>
            <!-- Suggested Items -->
            <ng-container *ngIf="restaurant.suggest_product_count > 0">
              <div class="did-you-forget">
                Did you forget ?
              </div>
              <ng-container *ngFor="let category of categories;let i=index">
                <ng-container *ngFor="let menu of category.menu_suggested;let j=index">
                  <ng-container
                    *ngIf="menu.menu_addon == 'No' && menu.price_option == 'single' && menu.is_suggest == '1'">
                    <div class="cart-item suggested-item">
                      <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex flex-column">
                          <span class="item-name">{{ menu?.menu_name }}</span>
                        </div>
                        <div class="quantity-controls">
                          <button class="add-btn" *ngIf="menu.variants?.length == 1"
                            (click)="addItemToCart(menuModal,menu,menu.id,menu.variants[0].id)">
                            <i class="fas fa-plus"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </ng-container>
              </ng-container>
            </ng-container>
          </div>
          <!-- Checkout Button -->
          <ng-container *ngIf="restaurant.online_order == 'Yes' && carts.length != 0 && order.order_type == 'delivery'">
            <button type="button" (click)="validatePrecheckout()" class="checkout-btn"
              [ngClass]="{'disabled': (restaurant.minimum_order >= getGrandTotal() || restaurant.restaurant_delivery != 'Yes')}"
              [disabled]="isPrecheckoutLoading || getGrandTotal() < restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes'">
              <p class="m-0 d-flex justify-content-between fs-5">
                <span class="text-start">
                  {{ restaurant.currentStatus == 'Open' ? 'Checkout' : 'Pre Order'}}
                  <i class="fas fa-arrow-right"></i>
                </span>
                <span class="spacing text-end ms-4">{{convertNumber(getGrandTotal())}}</span>
              </p>
            </button>
          </ng-container>
          <ng-container *ngIf="restaurant.online_order == 'Yes' && carts.length != 0 && order.order_type == 'pickup'">
            <button type="button" (click)="validatePrecheckout()" class="checkout-btn"
              [ngClass]="{'disabled':(restaurant.minimum_order >= getGrandTotal()) || restaurant.restaurant_pickup != 'Yes'}"
              [disabled]="isPrecheckoutLoading || restaurant.restaurant_pickup != 'Yes'">
              <p class="m-0 d-flex justify-content-between fs-5">
                <span class="text-start">
                  {{ restaurant.currentStatus == 'Open' ? 'Checkout' : 'Pre Order' }}
                  <i class="fas fa-arrow-right"></i>
                </span>
                <span class="spacing text-end ms-4">{{convertNumber(getGrandTotal())}}</span>
              </p>
            </button>
          </ng-container>
          <!-- End Checkout Button -->
          <!-- Minimum Order -->
          <div class="col-md-12 p-1 py-2 text-center text-danger"
            *ngIf="carts.length != 0 && order.order_type == 'delivery' && restaurant.minimum_order >= getGrandTotal()">
            <strong>Minimum Order {{convertNumber(restaurant.minimum_order)}}</strong>
          </div>
        </ng-container>
        <ng-template #noItem>
          <div class="empty-cart-cls">
            <img src="assets/cartitem.png" class="img-fluid m-4">
            <span class="item-name">No Item(s) Added</span>
          </div>
        </ng-template>
      </div>
    </div>
  </div>
</div>

<ng-template #addressModal let-modal>
  <div class="address-modal p-3">
    <div class="address-modal-header">
      <button class="address-close-btn" (click)="modal.dismiss('Cross click')">
        <i class="fas fa-times"></i>
      </button>
      <h4 class="address-modal-title text-primary fw-bold" id="modal-basic-title">
        Add New Deliver Address
      </h4>
    </div>
    <div class="modal-body">
      <div class="col-sm-12 col-xs-12 form-group">
        <label class="col-md-4">PostCode</label>
        <div class="col-sm-12 col-xs-12 d-flex justify-content-between">
          <input type="text" class="form-control col-md-5 col-xs-12 w-50" id="zipcode" name="zipcode"
            [(ngModel)]="addressBookAdd.zipcode" required />
          <button class="address-modal-close p-0 px-4" [disabled]="isPostcodeLoading"
            (click)="findzipcode(addressBookAdd.zipcode)">
            Find Address
          </button>
        </div>
        <div *ngIf="Postcodeerror">
          <span class="text-danger">{{ Postcodeerror }}</span>
        </div>
      </div>
      <div class="col-sm-12 col-xs-12 form-group">
        <label class="col-md-4">Address Title</label>
        <input type="text" class="form-control col-md-8" id="title" name="title" [(ngModel)]="addressBookAdd.title" />
      </div>
      <div class="col-sm-12 col-xs-12 form-group">
        <label class="col-md-4">Door no / Flat no</label>
        <input type="text" class="form-control col-md-8" id="flat_no" name="flat_no"
          [(ngModel)]="addressBookAdd.flat_no" required />
      </div>
      <div class="col-sm-12 col-xs-12 form-group">
        <label class="col-md-4">Address</label>
        <input type="text" class="form-control col-md-8" id="address" name="address"
          [(ngModel)]="addressBookAdd.address" required />
      </div>
      <input type="hidden" class="form-control col-md-8" id="latitude" name="latitude"
        [(ngModel)]="addressBookAdd.latitude" />
      <input type="hidden" class="form-control col-md-8" id="longitude" name="longitude"
        [(ngModel)]="addressBookAdd.longitude" />
      <div *ngIf="Modelerror">
        <span class="text-danger">{{ Modelerror }}</span>
      </div>
    </div>
    <button class="address-modal-close w-100" [disabled]="isModelLoading" (click)="validateAddress(addressModal)">
      Add Address
    </button>
  </div>
</ng-template>