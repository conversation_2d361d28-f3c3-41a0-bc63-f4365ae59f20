import { Metatag } from "./meta-tag";
import { RestaurantPaymentMethod } from "./restaurant-payment";
import { RestaurantTiming } from "./restaurant-timing";
import { SiteSetting } from "./site-setting";
import { Surcharge } from "./surcharge";

export class Restaurant {
  id: string;
  user_id: string;

  //Client Detail
  contact_name: string;
  contact_phone: string;
  contact_email: string;
  contact_address: string;
  sourcelatitude: string;
  sourcelongitude: string;
  street_address: string;
  country_id: string;
  currency: string;
  timezone: string;
  currency_code: string;
  city_id: string;
  zipcode: string;

  //Restaurant Detail
  restaurant_name: string;
  seo_url: string;
  restaurant_phone: string;
  working_time: string;
  facebook_login: string;
  google_login: string;
  alcahol_available: string;
  alcahol_not_available: string;
  bring_your_alcahol: string;
  soft_drink: string;

  delivery_charge: string;

  suggest_product_count: number;
  // Delivery Timing
  currentStatus: string;
  currentDeliveryStatus: string;
  currentPickupStatus: string;
  monday_first_opentime: string;
  monday_first_closetime: string;
  monday_second_opentime: string;
  monday_second_closetime: string;
  tuesday_first_opentime: string;
  tuesday_first_closetime: string;
  tuesday_second_opentime: string;
  tuesday_second_closetime: string;
  wednesday_first_opentime: string;
  wednesday_first_closetime: string;
  wednesday_second_opentime: string;
  wednesday_second_closetime: string;
  thursday_first_opentime: string;
  thursday_first_closetime: string;
  thursday_second_opentime: string;
  thursday_second_closetime: string;
  friday_first_opentime: string;
  friday_first_closetime: string;
  friday_second_opentime: string;
  friday_second_closetime: string;
  saturday_first_opentime: string;
  saturday_first_closetime: string;
  saturday_second_opentime: string;
  saturday_second_closetime: string;
  sunday_first_opentime: string;
  sunday_first_closetime: string;
  sunday_second_opentime: string;
  sunday_second_closetime: string;

  //Day Status
  monday_status: string;
  tuesday_status: string;
  wednesday_status: string;
  thursday_status: string;
  friday_status: string;
  saturday_status: string;
  sunday_status: string;

  pickup_estimate_time: string;
  restaurant_cuisine: string;
  restaurant_visibility: string;
  restaurant_dispatch: string;
  online_order: string;
  restaurant_pickup: string;
  restaurant_delivery: string;
  restaurant_booktable: string;
  booking_status: string;
  image_type: string;
  restaurant_about: string;
  username: string;

  //Delivery Info
  estimate_time: string;
  minimum_order: string;
  paypal_minimum_order: number;
  card_minimum_order: number;
  free_delivery: string;
  booking_start: number;
  booking_end: number;
  map_mode: string;

  //Order Info
  email_order: string;
  order_email: string;
  sms_option: string;
  sms_phonenumber: string;

  //Commission
  restaurant_commission: number;
  cash_commission: number;
  card_commission: number;
  paypal_commission: number;

  logo_name: string;
  is_logged: string;
  reward_option: string;
  status: string;
  response_status: string;
  delete_status: string;

  service_charge: number;
  service_charge_type: number;
  servicecharge_delivery: boolean;
  servicecharge_picked: boolean;
  driver_tip: boolean;
  is_featured: string;
  new: string;

  meta_title: string;
  meta_keyword: string;
  meta_description: string;

  no_of_guest: number;
  booking_payment: string;
  booking_amount: number;
  is_allergy: boolean;
  allergy_message: string;

  payment_methods: RestaurantPaymentMethod[];
  site_setting: SiteSetting;
  meta_tag: Metatag;
  restaurant_timing: RestaurantTiming
  business: any
  surcharges: Surcharge[];

  average_rating: number;
  total_reviews: number;
  restaurant_status: string;
  delivery_setting: any
  cuisine_names: any[];
  promotions: any[];
  web_theme: string;

  // New Variables
  image_url: string;
  restaurant_id: string;
  is_favourite: boolean;
  city_name: string;

  // New Functions
  static toFavouriteFormData(restaurant: Restaurant) {
    const formData = new FormData();

    if (restaurant.restaurant_id) formData.append('restaurant_id', restaurant.restaurant_id);
    if (restaurant.user_id) formData.append('customer_id', restaurant.user_id);
    formData.append('is_favourite', restaurant.is_favourite ? '1' : '0');

    return formData;
  }
}
