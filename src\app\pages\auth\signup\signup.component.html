<form nz-form class="login-form mx-auto mb-3" #signupForm="ngForm"
  (ngSubmit)="signupForm.form.valid && signup(signupForm)">
  <h5 class="text-center">Signup</h5>
  <div class="col-md-12 alert alert-danger alert-dismissible fade show" role="alert" *ngIf="errorMessage">
    {{ errorMessage }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
  </div>
  <div class="col-md-12 col-xs-12 mb-1 d-md-flex">
    <div class="col-md-5 col-xs-12">
      <label for="first_name" class="form-label">First Name</label>
      <input name="first_name" type="text" (keydown.space)="$event.preventDefault()" class="form-control"
        id="first_name" placeholder="Enter First Name" [(ngModel)]="user.first_name" required #first_name="ngModel"
        [ngClass]="{ 'is-invalid': signupForm.submitted && first_name.invalid }" />
      <div class="invalid-feedback" *ngIf="signupForm.submitted && first_name.invalid">
        <span *ngIf="first_name.errors.required">First Name is required</span>
      </div>
    </div>
    <div class="col-md-1"></div>
    <div class="col-md-5 col-xs-12">
      <label for="last_name" class="form-label">Last Name</label>
      <input name="last_name" type="text" (keydown.space)="$event.preventDefault()" class="form-control" id="last_name"
        placeholder="Enter Last Name" [(ngModel)]="user.last_name" required #last_name="ngModel"
        [ngClass]="{ 'is-invalid': signupForm.submitted && last_name.invalid }" />
      <div class="invalid-feedback" *ngIf="signupForm.submitted && last_name.invalid">
        <span *ngIf="last_name.errors.required">Last Name is required</span>
      </div>
    </div>
  </div>
  <div class="col-md-12 col-xs-12 mb-1 d-md-flex">
    <div class="col-md-5 col-xs-12">
      <label for="phone_number" class="form-label">Phone Number</label>
      <input name="phone_number" (keypress)="keyPress($event)" (keydown.space)="$event.preventDefault()"
        (blur)="onlyNumeric()" minlength="10" maxlength="11" type="text" class="form-control" id="phone_number"
        placeholder="Enter Phone Number" [(ngModel)]="user.phone_number" required #phone_number="ngModel"
        [ngClass]="{ 'is-invalid': signupForm.submitted && phone_number.invalid }" />
      <div class="invalid-feedback" *ngIf="signupForm.submitted && phone_number.invalid">
        <span *ngIf="phone_number.errors.required">Phone Number is required</span>
        <span *ngIf="phone_number.errors.minlength">Phone Number minimum 10 digit</span>
        <span *ngIf="phone_number.errors.maxlength">Phone Number minimum 11 digit</span>
      </div>
    </div>
    <div class="col-md-1"></div>
    <div class="col-md-5 col-xs-12">
      <label for="username" class="form-label">Email</label>
      <input name="username" type="email" class="form-control" (keydown.space)="$event.preventDefault()" id="username"
        placeholder="Enter Email" [(ngModel)]="user.username" email required #username="ngModel"
        [ngClass]="{ 'is-invalid': signupForm.submitted && username.invalid }" />
      <div class="invalid-feedback" *ngIf="signupForm.submitted && username.invalid">
        <span *ngIf="username.errors.required">Username is required</span>
        <span *ngIf="username.errors.email">Must be a valid email address</span>
      </div>
    </div>
  </div>
  <div class="col-md-12 col-xs-12 mb-1 d-md-flex">
    <div class="col-md-5 col-xs-12">
      <label for="spassword" class="form-label">Password</label>
      <input name="spassword" type="password" class="form-control" (keydown.space)="$event.preventDefault()"
        id="spassword" placeholder="Enter Password" [(ngModel)]="user.password" required #password="ngModel"
        [ngClass]="{ 'is-invalid': signupForm.submitted && password.invalid }" />
      <div class="invalid-feedback" *ngIf="signupForm.submitted && password.invalid">
        <span *ngIf="password.errors.required">Password is required</span>
      </div>
    </div>
    <div class="col-md-1 col-xs-12"></div>
    <div class="col-md-5 col-xs-12">
      <label for="confirmPassword" class="form-label">Confirm Password</label>
      <input name="confirmPassword" type="password" class="form-control" (keydown.space)="$event.preventDefault()"
        id="confirmPassword" placeholder="Enter Confirm Password" [(ngModel)]="user.confirmPassword" required
        #confirmPassword="ngModel" [ngClass]="{ 'is-invalid': signupForm.submitted && confirmPassword.invalid }" />
      <div class="invalid-feedback" *ngIf="signupForm.submitted && confirmPassword.invalid">
        <span *ngIf="confirmPassword.errors.required">Confirm Password is required</span>
      </div>
    </div>
  </div>
  <div class="col-md-12 col-xs-12 d-md-flex" *ngIf="referral.referral_option == 'Yes'">
    <div class="col-md-11 col-xs-11">
      <label for="referred_by" class="form-label">Referral Code (optional)</label>
      <input name="referred_by" type="text" class="form-control" id="referred_by" placeholder="Enter Referral Code"
        [(ngModel)]="user.referred_by" />
    </div>
  </div>
  <div class="d-md-flex justify-content-center mt-3">
    <button type="submit" [disabled]="isLoading" class="text-center btn btn-primary w-50 text-white">
      <div class="ms-2 spinner-border" style="width: 20px;height: 20px;" *ngIf="isLoading" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Signup
    </button>
  </div>
</form>