import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { Component, OnDestroy, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { NgForm } from '@angular/forms';
import { NotificationService } from 'src/app/core/services/notification.service';
import { MessagingService } from 'src/app/core/services/messaging.service';

@Component({
  selector: 'app-privacy',
  templateUrl: './privacy.component.html',
  styleUrls: ['./privacy.component.scss'],
})
export class PrivacyComponent implements OnInit, OnDestroy {
  @Input() hello: string;

  subs = new Subscription();
  isLoading = false;
  isPrivacyLoading = false;
  error = null;

  isModelLoading = false;
  Modelerror = null;

  user: User;
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;
  previousPage: any;
  selectedToogle: string = 'on';

  restaurant: Restaurant = new Restaurant();

  options = { query: null, page: 1, per_page: 10, customer_id: null };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private currencyPipe: CurrencyPipe,
    private restaurantService: RestaurantService,
    private notificationService: NotificationService,
    private messagingService: MessagingService,
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    this.options.customer_id = user?.id;
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    this.modalOptions = {
      backdrop: 'static',
      size: 'lg',
      backdropClass: 'customBackdrop',
    };
    this.fetchRestaurant();

    // Order Place
    if (this.user.order_place == 0) {
      this.user.email_order_place = false;
      this.user.sms_order_place = false;
      this.user.app_order_place = false;
    }
    if (this.user.order_place == 1) {
      this.user.email_order_place = false;
      this.user.sms_order_place = false;
      this.user.app_order_place = true;
    }
    if (this.user.order_place == 2) {
      this.user.email_order_place = false;
      this.user.sms_order_place = true;
      this.user.app_order_place = false;
    }
    if (this.user.order_place == 3) {
      this.user.email_order_place = false;
      this.user.sms_order_place = true;
      this.user.app_order_place = true;
    }
    if (this.user.order_place == 4) {
      this.user.email_order_place = true;
      this.user.sms_order_place = false;
      this.user.app_order_place = false;
    }
    if (this.user.order_place == 5) {
      this.user.email_order_place = true;
      this.user.sms_order_place = false;
      this.user.app_order_place = true;
    }
    if (this.user.order_place == 6) {
      this.user.email_order_place = true;
      this.user.sms_order_place = true;
      this.user.app_order_place = false;
    }
    if (this.user.order_place == 7) {
      this.user.email_order_place = true;
      this.user.sms_order_place = true;
      this.user.app_order_place = true;
    }

    // Order Accept
    if (this.user.order_accept == 0) {
      this.user.email_order_accept = false;
      this.user.sms_order_accept = false;
      this.user.app_order_accept = false;
    }
    if (this.user.order_accept == 1) {
      this.user.email_order_accept = false;
      this.user.sms_order_accept = false;
      this.user.app_order_accept = true;
    }
    if (this.user.order_accept == 2) {
      this.user.email_order_accept = false;
      this.user.sms_order_accept = true;
      this.user.app_order_accept = false;
    }
    if (this.user.order_accept == 3) {
      this.user.email_order_accept = false;
      this.user.sms_order_accept = true;
      this.user.app_order_accept = true;
    }
    if (this.user.order_accept == 4) {
      this.user.email_order_accept = true;
      this.user.sms_order_accept = false;
      this.user.app_order_accept = false;
    }
    if (this.user.order_accept == 5) {
      this.user.email_order_accept = true;
      this.user.sms_order_accept = false;
      this.user.app_order_accept = true;
    }
    if (this.user.order_accept == 6) {
      this.user.email_order_accept = true;
      this.user.sms_order_accept = true;
      this.user.app_order_accept = false;
    }
    if (this.user.order_accept == 7) {
      this.user.email_order_accept = true;
      this.user.sms_order_accept = true;
      this.user.app_order_accept = true;
    }

    // Order Reject
    if (this.user.order_reject == 0) {
      this.user.email_order_reject = false;
      this.user.sms_order_reject = false;
      this.user.app_order_reject = false;
    }
    if (this.user.order_reject == 1) {
      this.user.email_order_reject = false;
      this.user.sms_order_reject = false;
      this.user.app_order_reject = true;
    }
    if (this.user.order_reject == 2) {
      this.user.email_order_reject = false;
      this.user.sms_order_reject = true;
      this.user.app_order_reject = false;
    }
    if (this.user.order_reject == 3) {
      this.user.email_order_reject = false;
      this.user.sms_order_reject = true;
      this.user.app_order_reject = true;
    }
    if (this.user.order_reject == 4) {
      this.user.email_order_reject = true;
      this.user.sms_order_reject = false;
      this.user.app_order_reject = false;
    }
    if (this.user.order_reject == 5) {
      this.user.email_order_reject = true;
      this.user.sms_order_reject = false;
      this.user.app_order_reject = true;
    }
    if (this.user.order_reject == 6) {
      this.user.email_order_reject = true;
      this.user.sms_order_reject = true;
      this.user.app_order_reject = false;
    }
    if (this.user.order_reject == 7) {
      this.user.email_order_reject = true;
      this.user.sms_order_reject = true;
      this.user.app_order_reject = true;
    }

    // Order Delivered
    if (this.user.order_delivered == 0) {
      this.user.email_order_delivered = false;
      this.user.sms_order_delivered = false;
      this.user.app_order_delivered = false;
    }
    if (this.user.order_delivered == 1) {
      this.user.email_order_delivered = false;
      this.user.sms_order_delivered = false;
      this.user.app_order_delivered = true;
    }
    if (this.user.order_delivered == 2) {
      this.user.email_order_delivered = false;
      this.user.sms_order_delivered = true;
      this.user.app_order_delivered = false;
    }
    if (this.user.order_delivered == 3) {
      this.user.email_order_delivered = false;
      this.user.sms_order_delivered = true;
      this.user.app_order_delivered = true;
    }
    if (this.user.order_delivered == 4) {
      this.user.email_order_delivered = true;
      this.user.sms_order_delivered = false;
      this.user.app_order_delivered = false;
    }
    if (this.user.order_delivered == 5) {
      this.user.email_order_delivered = true;
      this.user.sms_order_delivered = false;
      this.user.app_order_delivered = true;
    }
    if (this.user.order_delivered == 6) {
      this.user.email_order_delivered = true;
      this.user.sms_order_delivered = true;
      this.user.app_order_delivered = false;
    }
    if (this.user.order_delivered == 7) {
      this.user.email_order_delivered = true;
      this.user.sms_order_delivered = true;
      this.user.app_order_delivered = true;
    }

    // Book Table
    if (this.user.book_table == 0) {
      this.user.email_book_table = false;
      this.user.sms_book_table = false;
      this.user.app_book_table = false;
    }
    if (this.user.book_table == 1) {
      this.user.email_book_table = false;
      this.user.sms_book_table = false;
      this.user.app_book_table = true;
    }
    if (this.user.book_table == 2) {
      this.user.email_book_table = false;
      this.user.sms_book_table = true;
      this.user.app_book_table = false;
    }
    if (this.user.book_table == 3) {
      this.user.email_book_table = false;
      this.user.sms_book_table = true;
      this.user.app_book_table = true;
    }
    if (this.user.book_table == 4) {
      this.user.email_book_table = true;
      this.user.sms_book_table = false;
      this.user.app_book_table = false;
    }
    if (this.user.book_table == 5) {
      this.user.email_book_table = true;
      this.user.sms_book_table = false;
      this.user.app_book_table = true;
    }
    if (this.user.book_table == 6) {
      this.user.email_book_table = true;
      this.user.sms_book_table = true;
      this.user.app_book_table = false;
    }
    if (this.user.book_table == 7) {
      this.user.email_book_table = true;
      this.user.sms_book_table = true;
      this.user.app_book_table = true;
    }

    // Book Status
    if (this.user.book_status == 0) {
      this.user.email_book_status = false;
      this.user.sms_book_status = false;
      this.user.app_book_status = false;
    }
    if (this.user.book_status == 1) {
      this.user.email_book_status = false;
      this.user.sms_book_status = false;
      this.user.app_book_status = true;
    }
    if (this.user.book_status == 2) {
      this.user.email_book_status = false;
      this.user.sms_book_status = true;
      this.user.app_book_status = false;
    }
    if (this.user.book_status == 3) {
      this.user.email_book_status = false;
      this.user.sms_book_status = true;
      this.user.app_book_status = true;
    }
    if (this.user.book_status == 4) {
      this.user.email_book_status = true;
      this.user.sms_book_status = false;
      this.user.app_book_status = false;
    }
    if (this.user.book_status == 5) {
      this.user.email_book_status = true;
      this.user.sms_book_status = false;
      this.user.app_book_status = true;
    }
    if (this.user.book_status == 6) {
      this.user.email_book_status = true;
      this.user.sms_book_status = true;
      this.user.app_book_status = false;
    }
    if (this.user.book_status == 7) {
      this.user.email_book_status = true;
      this.user.sms_book_status = true;
      this.user.app_book_status = true;
    }

    // Important Update
    if (this.user.important_update == 0) {
      this.user.email_important_update = false;
      this.user.sms_important_update = false;
      this.user.app_important_update = false;
    }
    if (this.user.important_update == 1) {
      this.user.email_important_update = false;
      this.user.sms_important_update = false;
      this.user.app_important_update = true;
    }
    if (this.user.important_update == 2) {
      this.user.email_important_update = false;
      this.user.sms_important_update = true;
      this.user.app_important_update = false;
    }
    if (this.user.important_update == 3) {
      this.user.email_important_update = false;
      this.user.sms_important_update = true;
      this.user.app_important_update = true;
    }
    if (this.user.important_update == 4) {
      this.user.email_important_update = true;
      this.user.sms_important_update = false;
      this.user.app_important_update = false;
    }
    if (this.user.important_update == 5) {
      this.user.email_important_update = true;
      this.user.sms_important_update = false;
      this.user.app_important_update = true;
    }
    if (this.user.important_update == 6) {
      this.user.email_important_update = true;
      this.user.sms_important_update = true;
      this.user.app_important_update = false;
    }
    if (this.user.important_update == 7) {
      this.user.email_important_update = true;
      this.user.sms_important_update = true;
      this.user.app_important_update = true;
    }

    if (this.user.order_place == 0 && this.user.order_accept == 0 && this.user.order_reject == 0 && this.user.order_delivered == 0 && this.user.book_table == 0 && this.user.book_status == 0 && this.user.important_update == 0) {
      this.selectedToogle = 'off'
    }
  }

  buttonStatus(status: string) {
    this.selectedToogle = status;

    if (status == 'on') {
      this.user.email_order_place = true;
      this.user.email_order_accept = true;
      this.user.email_order_reject = true;
      this.user.email_order_delivered = true;
      this.user.email_book_table = true;
      this.user.email_book_status = true;
      this.user.email_important_update = true;
      this.user.sms_order_place = true;
      this.user.sms_order_accept = true;
      this.user.sms_order_reject = true;
      this.user.sms_order_delivered = true;
      this.user.sms_book_table = true;
      this.user.sms_book_status = true;
      this.user.sms_important_update = true;
      this.user.app_order_place = true;
      this.user.app_order_accept = true;
      this.user.app_order_reject = true;
      this.user.app_order_delivered = true;
      this.user.app_book_table = true;
      this.user.app_book_status = true;
      this.user.app_important_update = true;
    }

    if (status == 'off') {
      this.user.email_order_place = false;
      this.user.email_order_accept = false;
      this.user.email_order_reject = false;
      this.user.email_order_delivered = false;
      this.user.email_book_table = false;
      this.user.email_book_status = false;
      this.user.email_important_update = false;
      this.user.sms_order_place = false;
      this.user.sms_order_accept = false;
      this.user.sms_order_reject = false;
      this.user.sms_order_delivered = false;
      this.user.sms_book_table = false;
      this.user.sms_book_status = false;
      this.user.sms_important_update = false;
      this.user.app_order_place = false;
      this.user.app_order_accept = false;
      this.user.app_order_reject = false;
      this.user.app_order_delivered = false;
      this.user.app_book_table = false;
      this.user.app_book_status = false;
      this.user.app_important_update = false;
    }
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
        // this.restaurant.web_theme
      }, err => this.error = err)
    );
  }

  validatePrivacy() {
    this.isPrivacyLoading = true;
    //Order Place
    if (!this.user.email_order_place && !this.user.sms_order_place && !this.user.app_order_place) {
      this.user.order_place = 0;
    }
    if (!this.user.email_order_place && !this.user.sms_order_place && this.user.app_order_place) {
      this.user.order_place = 1;
    }
    if (!this.user.email_order_place && this.user.sms_order_place && !this.user.app_order_place) {
      this.user.order_place = 2;
    }
    if (!this.user.email_order_place && this.user.sms_order_place && this.user.app_order_place) {
      this.user.order_place = 3;
    }
    if (this.user.email_order_place && !this.user.sms_order_place && !this.user.app_order_place) {
      this.user.order_place = 4;
    }
    if (this.user.email_order_place && !this.user.sms_order_place && this.user.app_order_place) {
      this.user.order_place = 5;
    }
    if (this.user.email_order_place && this.user.sms_order_place && !this.user.app_order_place) {
      this.user.order_place = 6;
    }
    if (this.user.email_order_place && this.user.sms_order_place && this.user.app_order_place) {
      this.user.order_place = 7;
    }

    //Order Accept
    if (!this.user.email_order_accept && !this.user.sms_order_accept && !this.user.app_order_accept) {
      this.user.order_accept = 0;
    }
    if (!this.user.email_order_accept && !this.user.sms_order_accept && this.user.app_order_accept) {
      this.user.order_accept = 1;
    }
    if (!this.user.email_order_accept && this.user.sms_order_accept && !this.user.app_order_accept) {
      this.user.order_accept = 2;
    }
    if (!this.user.email_order_accept && this.user.sms_order_accept && this.user.app_order_accept) {
      this.user.order_accept = 3;
    }
    if (this.user.email_order_accept && !this.user.sms_order_accept && !this.user.app_order_accept) {
      this.user.order_accept = 4;
    }
    if (this.user.email_order_accept && !this.user.sms_order_accept && this.user.app_order_accept) {
      this.user.order_accept = 5;
    }
    if (this.user.email_order_accept && this.user.sms_order_accept && !this.user.app_order_accept) {
      this.user.order_accept = 6;
    }
    if (this.user.email_order_accept && this.user.sms_order_accept && this.user.app_order_accept) {
      this.user.order_accept = 7;
    }

    //Order Reject
    if (!this.user.email_order_reject && !this.user.sms_order_reject && !this.user.app_order_reject) {
      this.user.order_reject = 0;
    }
    if (!this.user.email_order_reject && !this.user.sms_order_reject && this.user.app_order_reject) {
      this.user.order_reject = 1;
    }
    if (!this.user.email_order_reject && this.user.sms_order_reject && !this.user.app_order_reject) {
      this.user.order_reject = 2;
    }
    if (!this.user.email_order_reject && this.user.sms_order_reject && this.user.app_order_reject) {
      this.user.order_reject = 3;
    }
    if (this.user.email_order_reject && !this.user.sms_order_reject && !this.user.app_order_reject) {
      this.user.order_reject = 4;
    }
    if (this.user.email_order_reject && !this.user.sms_order_reject && this.user.app_order_reject) {
      this.user.order_reject = 5;
    }
    if (this.user.email_order_reject && this.user.sms_order_reject && !this.user.app_order_reject) {
      this.user.order_reject = 6;
    }
    if (this.user.email_order_reject && this.user.sms_order_reject && this.user.app_order_reject) {
      this.user.order_reject = 7;
    }

    //Order Delivered
    if (!this.user.email_order_delivered && !this.user.sms_order_delivered && !this.user.app_order_delivered) {
      this.user.order_delivered = 0;
    }
    if (!this.user.email_order_delivered && !this.user.sms_order_delivered && this.user.app_order_delivered) {
      this.user.order_delivered = 1;
    }
    if (!this.user.email_order_delivered && this.user.sms_order_delivered && !this.user.app_order_delivered) {
      this.user.order_delivered = 2;
    }
    if (!this.user.email_order_delivered && this.user.sms_order_delivered && this.user.app_order_delivered) {
      this.user.order_delivered = 3;
    }
    if (this.user.email_order_delivered && !this.user.sms_order_delivered && !this.user.app_order_delivered) {
      this.user.order_delivered = 4;
    }
    if (this.user.email_order_delivered && !this.user.sms_order_delivered && this.user.app_order_delivered) {
      this.user.order_delivered = 5;
    }
    if (this.user.email_order_delivered && this.user.sms_order_delivered && !this.user.app_order_delivered) {
      this.user.order_delivered = 6;
    }
    if (this.user.email_order_delivered && this.user.sms_order_delivered && this.user.app_order_delivered) {
      this.user.order_delivered = 7;
    }

    //Book Table
    if (!this.user.email_book_table && !this.user.sms_book_table && !this.user.app_book_table) {
      this.user.book_table = 0;
    }
    if (!this.user.email_book_table && !this.user.sms_book_table && this.user.app_book_table) {
      this.user.book_table = 1;
    }
    if (!this.user.email_book_table && this.user.sms_book_table && !this.user.app_book_table) {
      this.user.book_table = 2;
    }
    if (!this.user.email_book_table && this.user.sms_book_table && this.user.app_book_table) {
      this.user.book_table = 3;
    }
    if (this.user.email_book_table && !this.user.sms_book_table && !this.user.app_book_table) {
      this.user.book_table = 4;
    }
    if (this.user.email_book_table && !this.user.sms_book_table && this.user.app_book_table) {
      this.user.book_table = 5;
    }
    if (this.user.email_book_table && this.user.sms_book_table && !this.user.app_book_table) {
      this.user.book_table = 6;
    }
    if (this.user.email_book_table && this.user.sms_book_table && this.user.app_book_table) {
      this.user.book_table = 7;
    }

    //Book Status
    if (!this.user.email_book_status && !this.user.sms_book_status && !this.user.app_book_status) {
      this.user.book_status = 0;
    }
    if (!this.user.email_book_status && !this.user.sms_book_status && this.user.app_book_status) {
      this.user.book_status = 1;
    }
    if (!this.user.email_book_status && this.user.sms_book_status && !this.user.app_book_status) {
      this.user.book_status = 2;
    }
    if (!this.user.email_book_status && this.user.sms_book_status && this.user.app_book_status) {
      this.user.book_status = 3;
    }
    if (this.user.email_book_status && !this.user.sms_book_status && !this.user.app_book_status) {
      this.user.book_status = 4;
    }
    if (this.user.email_book_status && !this.user.sms_book_status && this.user.app_book_status) {
      this.user.book_status = 5;
    }
    if (this.user.email_book_status && this.user.sms_book_status && !this.user.app_book_status) {
      this.user.book_status = 6;
    }
    if (this.user.email_book_status && this.user.sms_book_status && this.user.app_book_status) {
      this.user.book_status = 7;
    }

    //Important Update
    if (!this.user.email_important_update && !this.user.sms_important_update && !this.user.app_important_update) {
      this.user.important_update = 0;
    }
    if (!this.user.email_important_update && !this.user.sms_important_update && this.user.app_important_update) {
      this.user.important_update = 1;
    }
    if (!this.user.email_important_update && this.user.sms_important_update && !this.user.app_important_update) {
      this.user.important_update = 2;
    }
    if (!this.user.email_important_update && this.user.sms_important_update && this.user.app_important_update) {
      this.user.important_update = 3;
    }
    if (this.user.email_important_update && !this.user.sms_important_update && !this.user.app_important_update) {
      this.user.important_update = 4;
    }
    if (this.user.email_important_update && !this.user.sms_important_update && this.user.app_important_update) {
      this.user.important_update = 5;
    }
    if (this.user.email_important_update && this.user.sms_important_update && !this.user.app_important_update) {
      this.user.important_update = 6;
    }
    if (this.user.email_important_update && this.user.sms_important_update && this.user.app_important_update) {
      this.user.important_update = 7;
    }

    this.error = null;
    this.userService
      .update(this.user)
      .pipe(finalize(() => (this.isPrivacyLoading = false)))
      .subscribe(
        (res) => {
          this.messagingService.success("Privacy setting updated successfully !!");
          this.subs.add(this.userService.me()
            .pipe(finalize(() => this.isPrivacyLoading = false))
            .subscribe(res => {
              this.user = res
              // Order Place
              if (this.user.order_place == 0) {
                this.user.email_order_place = false;
                this.user.sms_order_place = false;
                this.user.app_order_place = false;
              }
              if (this.user.order_place == 1) {
                this.user.email_order_place = false;
                this.user.sms_order_place = false;
                this.user.app_order_place = true;
              }
              if (this.user.order_place == 2) {
                this.user.email_order_place = false;
                this.user.sms_order_place = true;
                this.user.app_order_place = false;
              }
              if (this.user.order_place == 3) {
                this.user.email_order_place = false;
                this.user.sms_order_place = true;
                this.user.app_order_place = true;
              }
              if (this.user.order_place == 4) {
                this.user.email_order_place = true;
                this.user.sms_order_place = false;
                this.user.app_order_place = false;
              }
              if (this.user.order_place == 5) {
                this.user.email_order_place = true;
                this.user.sms_order_place = false;
                this.user.app_order_place = true;
              }
              if (this.user.order_place == 6) {
                this.user.email_order_place = true;
                this.user.sms_order_place = true;
                this.user.app_order_place = false;
              }
              if (this.user.order_place == 7) {
                this.user.email_order_place = true;
                this.user.sms_order_place = true;
                this.user.app_order_place = true;
              }

              // Order Accept
              if (this.user.order_accept == 0) {
                this.user.email_order_accept = false;
                this.user.sms_order_accept = false;
                this.user.app_order_accept = false;
              }
              if (this.user.order_accept == 1) {
                this.user.email_order_accept = false;
                this.user.sms_order_accept = false;
                this.user.app_order_accept = true;
              }
              if (this.user.order_accept == 2) {
                this.user.email_order_accept = false;
                this.user.sms_order_accept = true;
                this.user.app_order_accept = false;
              }
              if (this.user.order_accept == 3) {
                this.user.email_order_accept = false;
                this.user.sms_order_accept = true;
                this.user.app_order_accept = true;
              }
              if (this.user.order_accept == 4) {
                this.user.email_order_accept = true;
                this.user.sms_order_accept = false;
                this.user.app_order_accept = false;
              }
              if (this.user.order_accept == 5) {
                this.user.email_order_accept = true;
                this.user.sms_order_accept = false;
                this.user.app_order_accept = true;
              }
              if (this.user.order_accept == 6) {
                this.user.email_order_accept = true;
                this.user.sms_order_accept = true;
                this.user.app_order_accept = false;
              }
              if (this.user.order_accept == 7) {
                this.user.email_order_accept = true;
                this.user.sms_order_accept = true;
                this.user.app_order_accept = true;
              }

              // Order Reject
              if (this.user.order_reject == 0) {
                this.user.email_order_reject = false;
                this.user.sms_order_reject = false;
                this.user.app_order_reject = false;
              }
              if (this.user.order_reject == 1) {
                this.user.email_order_reject = false;
                this.user.sms_order_reject = false;
                this.user.app_order_reject = true;
              }
              if (this.user.order_reject == 2) {
                this.user.email_order_reject = false;
                this.user.sms_order_reject = true;
                this.user.app_order_reject = false;
              }
              if (this.user.order_reject == 3) {
                this.user.email_order_reject = false;
                this.user.sms_order_reject = true;
                this.user.app_order_reject = true;
              }
              if (this.user.order_reject == 4) {
                this.user.email_order_reject = true;
                this.user.sms_order_reject = false;
                this.user.app_order_reject = false;
              }
              if (this.user.order_reject == 5) {
                this.user.email_order_reject = true;
                this.user.sms_order_reject = false;
                this.user.app_order_reject = true;
              }
              if (this.user.order_reject == 6) {
                this.user.email_order_reject = true;
                this.user.sms_order_reject = true;
                this.user.app_order_reject = false;
              }
              if (this.user.order_reject == 7) {
                this.user.email_order_reject = true;
                this.user.sms_order_reject = true;
                this.user.app_order_reject = true;
              }

              // Order Delivered
              if (this.user.order_delivered == 0) {
                this.user.email_order_delivered = false;
                this.user.sms_order_delivered = false;
                this.user.app_order_delivered = false;
              }
              if (this.user.order_delivered == 1) {
                this.user.email_order_delivered = false;
                this.user.sms_order_delivered = false;
                this.user.app_order_delivered = true;
              }
              if (this.user.order_delivered == 2) {
                this.user.email_order_delivered = false;
                this.user.sms_order_delivered = true;
                this.user.app_order_delivered = false;
              }
              if (this.user.order_delivered == 3) {
                this.user.email_order_delivered = false;
                this.user.sms_order_delivered = true;
                this.user.app_order_delivered = true;
              }
              if (this.user.order_delivered == 4) {
                this.user.email_order_delivered = true;
                this.user.sms_order_delivered = false;
                this.user.app_order_delivered = false;
              }
              if (this.user.order_delivered == 5) {
                this.user.email_order_delivered = true;
                this.user.sms_order_delivered = false;
                this.user.app_order_delivered = true;
              }
              if (this.user.order_delivered == 6) {
                this.user.email_order_delivered = true;
                this.user.sms_order_delivered = true;
                this.user.app_order_delivered = false;
              }
              if (this.user.order_delivered == 7) {
                this.user.email_order_delivered = true;
                this.user.sms_order_delivered = true;
                this.user.app_order_delivered = true;
              }

              // Book Table
              if (this.user.book_table == 0) {
                this.user.email_book_table = false;
                this.user.sms_book_table = false;
                this.user.app_book_table = false;
              }
              if (this.user.book_table == 1) {
                this.user.email_book_table = false;
                this.user.sms_book_table = false;
                this.user.app_book_table = true;
              }
              if (this.user.book_table == 2) {
                this.user.email_book_table = false;
                this.user.sms_book_table = true;
                this.user.app_book_table = false;
              }
              if (this.user.book_table == 3) {
                this.user.email_book_table = false;
                this.user.sms_book_table = true;
                this.user.app_book_table = true;
              }
              if (this.user.book_table == 4) {
                this.user.email_book_table = true;
                this.user.sms_book_table = false;
                this.user.app_book_table = false;
              }
              if (this.user.book_table == 5) {
                this.user.email_book_table = true;
                this.user.sms_book_table = false;
                this.user.app_book_table = true;
              }
              if (this.user.book_table == 6) {
                this.user.email_book_table = true;
                this.user.sms_book_table = true;
                this.user.app_book_table = false;
              }
              if (this.user.book_table == 7) {
                this.user.email_book_table = true;
                this.user.sms_book_table = true;
                this.user.app_book_table = true;
              }

              // Book Status
              if (this.user.book_status == 0) {
                this.user.email_book_status = false;
                this.user.sms_book_status = false;
                this.user.app_book_status = false;
              }
              if (this.user.book_status == 1) {
                this.user.email_book_status = false;
                this.user.sms_book_status = false;
                this.user.app_book_status = true;
              }
              if (this.user.book_status == 2) {
                this.user.email_book_status = false;
                this.user.sms_book_status = true;
                this.user.app_book_status = false;
              }
              if (this.user.book_status == 3) {
                this.user.email_book_status = false;
                this.user.sms_book_status = true;
                this.user.app_book_status = true;
              }
              if (this.user.book_status == 4) {
                this.user.email_book_status = true;
                this.user.sms_book_status = false;
                this.user.app_book_status = false;
              }
              if (this.user.book_status == 5) {
                this.user.email_book_status = true;
                this.user.sms_book_status = false;
                this.user.app_book_status = true;
              }
              if (this.user.book_status == 6) {
                this.user.email_book_status = true;
                this.user.sms_book_status = true;
                this.user.app_book_status = false;
              }
              if (this.user.book_status == 7) {
                this.user.email_book_status = true;
                this.user.sms_book_status = true;
                this.user.app_book_status = true;
              }

              // Important Update
              if (this.user.important_update == 0) {
                this.user.email_important_update = false;
                this.user.sms_important_update = false;
                this.user.app_important_update = false;
              }
              if (this.user.important_update == 1) {
                this.user.email_important_update = false;
                this.user.sms_important_update = false;
                this.user.app_important_update = true;
              }
              if (this.user.important_update == 2) {
                this.user.email_important_update = false;
                this.user.sms_important_update = true;
                this.user.app_important_update = false;
              }
              if (this.user.important_update == 3) {
                this.user.email_important_update = false;
                this.user.sms_important_update = true;
                this.user.app_important_update = true;
              }
              if (this.user.important_update == 4) {
                this.user.email_important_update = true;
                this.user.sms_important_update = false;
                this.user.app_important_update = false;
              }
              if (this.user.important_update == 5) {
                this.user.email_important_update = true;
                this.user.sms_important_update = false;
                this.user.app_important_update = true;
              }
              if (this.user.important_update == 6) {
                this.user.email_important_update = true;
                this.user.sms_important_update = true;
                this.user.app_important_update = false;
              }
              if (this.user.important_update == 7) {
                this.user.email_important_update = true;
                this.user.sms_important_update = true;
                this.user.app_important_update = true;
              }
              this.userService.saveUser(res);
            }, err => this.error = err)
          );
        },
        (err) => {
          this.error = err;
        }
      );

  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
