<ng-container *ngIf="!userService.userThemeLoading; else themeLoading">
  <ng-container [ngSwitch]="menuTheme">
    <app-address1 *ngSwitchCase="'theme1'"></app-address1>
    <app-address2 *ngSwitchCase="'theme2'"></app-address2>
    <app-address2 *ngSwitchCase="'theme3'"></app-address2>

    <!-- Optional: fallback -->
    <div *ngSwitchDefault>
      <app-address1></app-address1>
    </div>
  </ng-container>
</ng-container>

<ng-template #themeLoading>
  <div class="d-flex justify-content-center align-items-center w-100 h-100" style="min-height: 80vh;">
    <div class="cart-loader">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  </div>
</ng-template>