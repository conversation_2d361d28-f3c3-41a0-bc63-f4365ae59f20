<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-12 d-flex pb-3">
      <h4 class="col-6">Address Book</h4>
      <div class="col-6 text-end">
        <div class="add-address-button" (click)="addAddress(addressModal)">
          Add Address
        </div>
      </div>
    </div>

    <div class="d-flex flex-wrap gap-3 mt-0">
      <div class="card shadow-sm border rounded-3" *ngFor="let addressBook of addressBooks; let i = index">
        <div class="card-body text-center d-flex flex-column align-items-center gap-2">
          <!-- Icon -->
          <div class="icon-wrapper bg-light rounded-circle p-3 d-flex align-items-center justify-content-center">
            <img src="./assets/address.png" width="32" height="32" alt="Address Icon">
          </div>
          <!-- Title -->
          <h6 class="mb-1 fw-bold">{{ addressBook.title }}</h6>
          <!-- Address -->
          <p class="text-muted small mb-2">
            {{ addressBook.flat_no }}, {{ addressBook.address }}, {{ addressBook.zipcode }}
          </p>
          <!-- Delete Button -->
          <button class="btn btn-outline-danger btn-sm rounded-pill px-3" (click)="deleteAddress(addressBook)"
            title="Delete Address">
            <i class="fas fa-trash-alt me-1"></i> Delete
          </button>
        </div>
      </div>
    </div>

    <div class="col-md-12 empty-cart-cls text-center" *ngIf="addressBooks.length <= 0">
      <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
      <p><strong>No address(s) Found</strong></p>
    </div>

  </div>
</div>


<ng-template #addressModal let-modal>
  <div class="address-modal p-3">
    <div class="address-modal-header">
      <button class="address-close-btn" (click)="modal.dismiss('Cross click')">
        <i class="fas fa-times"></i>
      </button>
      <h4 class="address-modal-title text-primary fw-bold" id="modal-basic-title">
        Add New Deliver Address
      </h4>
    </div>
    <div class="modal-body">
      <div class="col-sm-12 col-xs-12 form-group">
        <label class="col-md-4">PostCode</label>
        <div class="col-sm-12 col-xs-12 d-flex justify-content-between">
          <input type="text" class="form-control col-md-5 col-xs-12 w-50" id="zipcode" name="zipcode"
            [(ngModel)]="addressBookAdd.zipcode" required />
          <button class="address-modal-close p-0 px-4" [disabled]="isPostcodeLoading"
            (click)="findzipcode(addressBookAdd.zipcode)">
            Find Address
          </button>
        </div>
        <div *ngIf="Postcodeerror">
          <span class="text-danger">{{ Postcodeerror }}</span>
        </div>
      </div>
      <div class="col-sm-12 col-xs-12 form-group">
        <label class="col-md-4">Address Title</label>
        <input type="text" class="form-control col-md-8" id="title" name="title" [(ngModel)]="addressBookAdd.title" />
      </div>
      <div class="col-sm-12 col-xs-12 form-group">
        <label class="col-md-4">Door no / Flat no</label>
        <input type="text" class="form-control col-md-8" id="flat_no" name="flat_no"
          [(ngModel)]="addressBookAdd.flat_no" required />
      </div>
      <div class="col-sm-12 col-xs-12 form-group">
        <label class="col-md-4">Address</label>
        <input type="text" class="form-control col-md-8" id="address" name="address"
          [(ngModel)]="addressBookAdd.address" required />
      </div>
      <input type="hidden" class="form-control col-md-8" id="latitude" name="latitude"
        [(ngModel)]="addressBookAdd.latitude" />
      <input type="hidden" class="form-control col-md-8" id="longitude" name="longitude"
        [(ngModel)]="addressBookAdd.longitude" />
      <div *ngIf="Modelerror">
        <span class="text-danger">{{ Modelerror }}</span>
      </div>
    </div>
    <button class="address-modal-close w-100" [disabled]="isModelLoading" (click)="validateAddress(addressModal)">
      Add Address
    </button>
  </div>
</ng-template>