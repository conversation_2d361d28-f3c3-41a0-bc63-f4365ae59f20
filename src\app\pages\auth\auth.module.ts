import { NgModule } from '@angular/core';
import { LoginComponent } from './login/login.component';
import { SignupComponent } from './signup/signup.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { AuthComponent } from './auth.component';
import { RouterModule, Routes } from '@angular/router';
import { Auth2Component } from './auth2/auth2.component';
import { Login2Component } from './login/login2/login2.component';
import { Signup2Component } from './signup/signup2/signup2.component';

const routes: Routes = [{ path: '', component: AuthComponent }];

@NgModule({
  declarations: [AuthComponent, LoginComponent, SignupComponent, Auth2Component, Login2Component, Signup2Component],
  imports: [RouterModule.forChild(routes), SharedModule],
})
export class AuthModule { }
