<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">
    <div class="col-12" *ngIf="orders.length > 0">
      <h4>Order History</h4>
      <div class="col-12 border rounded body-box-shadow p-3 mb-2" *ngFor=" let order of orders;">
        <div class="col-12 d-flex">
          <div class="col-5 align-item-center">
            <div class="fw-bold">{{order.delivery_date}} {{order.delivery_time}}</div>
            <span class="text-muted">
              #{{order.order_number}} | Total {{convertNumber(order.order_grand_total)}}
            </span>
          </div>
          <div class="col-3 align-item-center py-2 fw-bold">
            {{(order.status == 'Failed' ?'Rejected':order.status)}}
          </div>

          <div class="col-4 text-end">
            <div class="col-3 btn bg-primary text-white w-auto cursor" style="margin-right: 5px;margin-bottom: 5px;"
              (click)="orderView(order.id)">
              View
            </div>
            <div *ngIf="order.status == 'Delivered' && (!order.reviews)" (click)="addReview(reviewModal,order)"
              class="col-3 btn bg-primary text-white w-auto cursor" style="margin-right: 5px;margin-bottom: 5px;">
              Review
            </div>
            <div *ngIf="order.status == 'Delivered'" (click)="reOrderCreate(order.id)"
              class="col-3 btn bg-primary text-white w-auto cursor" style="margin-right: 5px;margin-bottom: 5px;">
              Re-Order
            </div>
            <div *ngIf="order.status == 'Delivered' && false" class="col-3 btn bg-primary text-white w-auto"
              style="margin-right: 5px;margin-bottom: 5px;">
              Complaint
            </div>
          </div>
        </div>
      </div>
      <app-pagination *ngIf="orders.length > 0" [total_items]="totalOrders" class="pt1 mt-1" [per_page]="per_page"
        [(current_page)]="page" (onChange)="loadPage($event)">
      </app-pagination>
    </div>

    <div class="col-md-12 empty-cart-cls text-center py-5" *ngIf="orders.length <= 0">
      <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
      <p><strong>No order(s) Found</strong></p>
    </div>
  </div>
</div>


<ng-template #reviewModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Review
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group d-flex">
      <label class="col-md-4">Rating</label>
      <div class="col-md-8">
        <app-rating [stars]="'5'" [(rating)]="reviewAdd.rating" class="pt-0 mt-0"></app-rating>
      </div>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-4">Message</label>
      <div class="col-sm-8">
        <textarea rows="3" class="form-control" nz-input [(ngModel)]="reviewAdd.message" name="message" id="message"
          placeholder="Enter Your Message"> </textarea>
      </div>
    </div>
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isModelLoading" class="btn btn-outline-dark cursor bg-primary text-white"
      (click)="validateReview(reviewModal)">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Submit
    </button>

  </div>
</ng-template>