<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">
    <div class="col-12 pb-4">
      <div class="referral-hero">
        <div class="referral-icon">
          <i class="fas fa-gift"></i>
        </div>
        <h3 class="referral-title">Share & Earn Rewards</h3>
        <p class="referral-subtitle">Invite friends to TiffinTom and earn {{convertNumber(referral.invite_amount)}} when
          they register. Your friends get {{convertNumber(referral.receive_amount)}} too!</p>
      </div>

      <div class="referral-code-section">
        <label class="code-label">Your Referral Code</label>
        <div class="code-display">
          <span class="code-text">{{user.referral_code}}</span>
          <button class="copy-btn" (click)="copyMessage()">
            <i class="fas fa-copy"></i>
            {{copyTextChange ? 'Copied!' : 'Copy'}}
          </button>
        </div>
      </div>

      <div class="share-section">
        <h5 class="share-title">Share Your Code</h5>
        <div class="share-grid">
          <a [href]="'http://www.facebook.com/sharer.php?u='+this.refferUrl" target="_blank" class="share-btn facebook">
            <i class="fab fa-facebook-f"></i>
            <!-- <span>Facebook</span> -->
          </a>
          <a [href]="'https://twitter.com/share?url='+this.refferUrl" target="_blank" class="share-btn twitter">
            <i class="fab fa-twitter"></i>
            <!-- <span>Twitter</span> -->
          </a>
          <a [href]="'https://api.whatsapp.com/send?text='+this.refferUrl" target="_blank" class="share-btn whatsapp">
            <i class="fab fa-whatsapp"></i>
            <!-- <span>WhatsApp</span> -->
          </a>
          <button class="share-btn sms" (click)="smsReferral(phoneModal)">
            <i class="fas fa-sms"></i>
            <!-- <span>SMS</span> -->
          </button>
          <button class="share-btn email" (click)="emailReferral(emailModal)">
            <i class="fas fa-envelope"></i>
            <!-- <span>Email</span> -->
          </button>
        </div>
      </div>

      <div class="url-share-section">
        <div class="url-input-group">
          <input type="text" class="url-input" [value]="this.refferUrl" readonly />
          <button class="url-copy-btn" (click)="copyMessage()">
            {{copyTextChange ? 'Copied!' : 'Copy Link'}}
          </button>
        </div>
      </div>

      <div class="how-it-works">
        <h5 class="works-title">How It Works</h5>
        <div class="steps-container">
          <div class="step">
            <div class="step-icon">
              <img src="./assets/invite-frd.png" alt="invite">
            </div>
            <div class="step-content">
              <h6>1. Invite Friends</h6>
              <p>Share your referral code with friends</p>
            </div>
          </div>
          <div class="step">
            <div class="step-icon">
              <img src="./assets/sucessful.png" alt="register">
            </div>
            <div class="step-content">
              <h6>2. Friend Registers</h6>
              <p>They sign up using your code</p>
            </div>
          </div>
          <div class="step">
            <div class="step-icon">
              <img src="./assets/gift.png" alt="reward">
            </div>
            <div class="step-content">
              <h6>3. Get Rewarded</h6>
              <p>You both earn {{convertNumber(referral.invite_amount)}}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="referral-list-card">
        <div class="card-header" (click)="tableExpand = !tableExpand">
          <h5 class="card-title">Referral History</h5>
          <i class="fas" [class.fa-chevron-down]="!tableExpand" [class.fa-chevron-up]="tableExpand"></i>
        </div>
        <div class="card-content" [class.expanded]="tableExpand">
          <div class="table-container" *ngIf="referralPendings.length > 0">
            <table class="referral-table">
              <thead>
                <tr>
                  <th>#</th>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Amount</th>
                  <th>Date</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of referralPendings; let i = index">
                  <td>{{i+1}}</td>
                  <td>{{item.first_name}} {{item.last_name}}</td>
                  <td>{{item.username}}</td>
                  <td>{{item.invite_amount}}</td>
                  <td>{{convertToDate(item.created)}}</td>
                  <td>
                    <span class="status-badge" [class.pending]="item.status == 'Eligible'"
                      [class.completed]="item.status != 'Eligible'">
                      {{(item.status == 'Eligible')?'Pending':item.status}}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="empty-state" *ngIf="referralPendings.length <= 0">
            <img src="assets/boxitem.png" alt="No referrals">
            <h6>No Referrals Yet</h6>
            <p>Start sharing your code to see referrals here</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #phoneModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Share On SMS
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Phone Number</label>
      <input type="text" class="form-control col-md-8" (keypress)="keyPress($event)" minlength="10" maxlength="11"
        id="phoneNumber" name="phoneNumber" [(ngModel)]="phoneNumber" />
    </div>
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isModelLoading" class="btn btn-primary text-white cursor"
      (click)="validatePhone(phoneModal)">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Send
    </button>
  </div>
</ng-template>

<ng-template #emailModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Share On Email
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Email</label>
      <input type="email" class="form-control col-md-8" id="emailAddress" name="emailAddress" placeholder="Enter email"
        [(ngModel)]="emailAddress" />
    </div>
    <div *ngIf="ModelEmailerror">
      <span class="text-danger">{{ ModelEmailerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isEmailModelLoading" class="btn btn-primary text-white cursor"
      (click)="validateEmail(emailModal)">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isEmailModelLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Send
    </button>
  </div>
</ng-template>