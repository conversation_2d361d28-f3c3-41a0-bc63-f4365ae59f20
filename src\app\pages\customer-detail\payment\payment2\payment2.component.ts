import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { StripeCustomer } from 'src/app/core/models/stripe-customer';
import { StripeCustomerService } from 'src/app/core/services/stripe-customer.service';
import { NotificationService } from 'src/app/core/services/notification.service';
import { MessagingService } from 'src/app/core/services/messaging.service';
declare var Stripe;


@Component({
  selector: 'app-payment2',
  templateUrl: './payment2.component.html',
  styleUrls: ['./payment2.component.scss']
})
export class Payment2Component implements OnInit {

  subs = new Subscription();
  isLoading = false;
  error = null;

  isModelLoading = false;
  Modelerror = null;

  user: User;
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;
  previousPage: any;

  restaurant: Restaurant = new Restaurant();
  stripeCustomers: StripeCustomer[] = [];
  stripeCustomer: StripeCustomer = new StripeCustomer();
  addStripeCustomer: StripeCustomer;

  totalWalletHistories = 0;
  addAmount: number = 0;
  cardId: string;
  last_page = 0;

  options = { query: null, page: 1, per_page: 10, customer_id: null };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private currencyPipe: CurrencyPipe,
    private restaurantService: RestaurantService,
    private stripeCustomerService: StripeCustomerService,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef,
    private messagingService: MessagingService,
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    this.options.customer_id = user?.id;
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    this.modalOptions = {
      backdrop: 'static',
      size: 'lg',
      backdropClass: 'customBackdrop',
      windowClass: 'rounded-modal'
    };
    this.fetchRestaurant();
    this.fetchCards();
  }

  fetchRestaurant() {
    this.isLoading = true;
    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
        if (this.restaurant?.site_setting?.finance_stripe_mode == 'Test') {
          var publishKey = this.restaurant?.site_setting?.finance_stripe_publishkeyTest
        } else {
          var publishKey = this.restaurant?.site_setting?.finance_stripe_publishkey
        }
        this.stripe = Stripe(publishKey);
        this.cdr.detectChanges();
      }, err => this.error = err)
    );
  }

  fetchCards() {
    this.subs.add(
      this.stripeCustomerService
        .get({ customer_id: this.userId, nopaginate: "1" })//service_type: 'normal',
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.stripeCustomers = res;
            this.cdr.detectChanges();
          },
          (err) => {
            this.stripeCustomers = [];
          }
        )
    );
  }

  addCard(model) {
    this.isModelLoading = true;
    this.addStripeCustomer = new StripeCustomer();
    this.openModal(model);
  }

  initCard() {
    this.cardElement = <HTMLInputElement>(
      document.getElementById('customCardElement')
    );
    var elements = this.stripe.elements();
    this.card = elements.create('card', { hidePostalCode: true });
    this.card.mount(this.cardElement);
    this.isModelLoading = false;
  }

  async handleForm(e) {
    e.preventDefault();
    this.isModelLoading = true;
    let createPaymentMethodPromise = this.stripe
      .createPaymentMethod({
        type: 'card',
        card: this.card,
      })
      .then((result) => {
        // this.createPaymentIntent(result.paymentMethod.id);
        if (!result.error) {
          this.stripeCustomer.customer_id = this.user.id;
          this.stripeCustomer.customer_name = this.user.first_name;
          this.stripeCustomer.stripe_token_id = result.paymentMethod.id;
          this.stripeCustomer.exp_month = result.paymentMethod.card.exp_month;
          this.stripeCustomer.exp_year = result.paymentMethod.card.exp_year;
          this.stripeCustomer.country = result.paymentMethod.card.country;
          this.stripeCustomer.card_brand = result.paymentMethod.card.brand;
          this.stripeCustomer.card_number = result.paymentMethod.card.last4;
          this.stripeCustomer.card_type = result.paymentMethod.card.funding;
          this.stripeCustomer.service_type = 'normal';
          this.subs.add(
            this.stripeCustomerService.create(this.stripeCustomer).
              pipe(finalize(() => this.isModelLoading = false))
              .subscribe(
                (res) => {
                  this.fetchCards();
                  this.messagingService.success("card added successfully !!")
                  this.modalService.dismissAll();
                },
                (err) => {
                  this.Modelerror = err;
                }
              )
          )
        } else {
          this.isModelLoading = false
          this.Modelerror = result.error.message;
        }
      });
  }

  deleteCard(stripeCustomer: StripeCustomer) {
    this.isLoading = true; this.error = null;

    this.subs.add(this.stripeCustomerService.delete(stripeCustomer.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.fetchCards();
        this.messagingService.success("card deleted successfully !!")
      }, err => { this.error = err; })
    );
  }


  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
    this.initCard()
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  getCardBrandClass(brand: string): string {
    switch (brand?.toLowerCase()) {
      case 'visa':
        return 'visa';
      case 'mastercard':
        return 'mastercard';
      case 'amex':
        return 'amex';
      default:
        return 'default-card';
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
