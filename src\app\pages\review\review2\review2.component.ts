import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { Component, On<PERSON>estroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import {
  NgbModal,
  ModalDismissReasons,
  NgbModalOptions,
  NgbActiveModal,
} from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { Review } from 'src/app/core/models/review';
import { ReviewService } from 'src/app/core/services/review.service';
import { Meta, Title } from '@angular/platform-browser';

@Component({
  selector: 'app-review2',
  templateUrl: './review2.component.html',
  styleUrls: ['./review2.component.scss']
})
export class Review2Component implements OnInit {
  subs = new Subscription();

  isLoading = false;
  error = null;
  success = false;

  user: User;
  reviews: Review[] = [];
  originalReviews: Review[] = [];
  restaurant: Restaurant = new Restaurant();
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;
  ratingFinal: number = 0;
  finalReview: string;
  selectedFilter: any = 'all';

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private restaurantService: RestaurantService,
    private currencyPipe: CurrencyPipe,
    private reviewService: ReviewService,
    private metaTagService: Meta,
    private titleService: Title,
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    // if (!this.userId) {
    //   this.router.navigateByUrl('/auth');
    // }
    this.modalOptions = {
      backdrop: 'static',
      size: 'lg',
      backdropClass: 'customBackdrop',
    };
    //Restaurant Find 
    this.fetchRestaurant();
    this.fetchReviews();
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
        if (this.restaurant.meta_tag.review_meta_title) {
          this.titleService.setTitle(this.restaurant.meta_tag.review_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.meta_tag.review_meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.meta_tag.review_meta_description });
        }
      }, err => this.error = err)
    );
  }

  fetchReviews() {
    this.isLoading = true;

    this.subs.add(this.reviewService.get({ restaurant_id: this.restaurant_id, status: 1, nopaginate: 1 })
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        (res) => {
          this.reviews = res;
          this.originalReviews = res;
          this.reviews.forEach(review => {
            this.ratingFinal = review.rating + this.ratingFinal;
          });
          this.finalReview = (this.ratingFinal / this.reviews.length).toFixed(2);
        }, (err) => {
          this.reviews = [];
        }
      )
    )
  }

  reviewFilter(filter) {
    this.selectedFilter = filter;
    if (filter == 'all') {
      this.reviews = Object.assign([], this.originalReviews);
    } else {
      let tempAllItems = Object.assign([], this.originalReviews)
      let data = tempAllItems.filter(item => item.rating === filter);
      this.reviews = Object.assign([], data);
      return;
    }
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd', 'en_US')
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
