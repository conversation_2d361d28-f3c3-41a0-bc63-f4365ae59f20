import { Component, OnInit } from '@angular/core';
import { UserService } from 'src/app/core/services/user.service';

@Component({
  selector: 'app-payment',
  templateUrl: './payment.component.html',
  styleUrls: ['./payment.component.scss'],
})
export class PaymentComponent implements OnInit {
  menuTheme: string;

  constructor(public userService: UserService) { }

  ngOnInit(): void {
    this.userService.getUserTheme().subscribe(res => {
      this.menuTheme = res;
    })
  }
}
