<div class="container mt-2 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-md-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">
    <div class="col-12 input-group py-2 sticky-top" style="z-index: 1050;">
      <input type="text" class="form-control border border-default body-box-shadow rounded"
        placeholder="I'm looking for..." aria-label="Search" (keyup)='search($event.target.value)' />
      <button type="button" class="btn bg-primary text-white body-box-shadow" style="width:120px;">Search</button>
    </div>
    <div class="col-lg-12 d-flex text-center px-2 py-2" *ngIf="mobile">
      <div class="fw-bold col-6 p-2 h-100 cursor rounded-start" (click)="orderType('pickup')"
        [ngClass]="{'bg-primary text-white' : order.order_type == 'pickup','bg-white text-black' : order.order_type != 'pickup','pe-none' : restaurant.restaurant_pickup != 'Yes'}">
        <span>
          Pickup
          <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_pickup != 'No'">Est. {{
            restaurant.pickup_estimate_time }} mins</p>
          <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_pickup != 'Yes'">Unavailable</p>
        </span>
      </div>
      <div class="fw-bold col-6 p-2 h-100 cursor rounded-end" (click)="orderType('delivery')"
        [ngClass]="{'bg-primary text-white' : order.order_type == 'delivery','bg-white text-black' : order.order_type != 'delivery','pe-none' : restaurant.restaurant_delivery != 'Yes'}">
        <span>
          Delivery
          <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_delivery != 'No'">Est. {{
            restaurant.estimate_time }} mins</p>
          <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_delivery != 'Yes'">Unavailable</p>
        </span>
      </div>
    </div>
    <div class="col-lg-3 d-none d-lg-block">
      <div class="categories sticky-top" style="top: 55px;">
        <h5 class="text-center">Categories</h5>
        <ul class="list-group body-box-shadow rounded" style="max-height: calc(110vh - 167px);overflow-y: auto;">
          <!-- [class.active]="expandableCategoryId == category.id" [class.disabled]="category.id == expandableCategoryId" -->
          <li class="list-group-item cursor" *ngFor="let category of categories" (click)="clickOnAccordion(category.id)"
            [class.customActiveMenu]="selectedCategoryId == category.id">
            {{ category?.category_name }}
          </li>
        </ul>
      </div>
      <div class="col-12 empty-cart-cls text-center py-5" *ngIf="categories.length == 0">
        <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
        <p><strong>No category(s) Found</strong></p>
      </div>
    </div>
    <div class="col-md-12 col-lg-5" [ngStyle]="{'margin-bottom': (mobile) ? '85px' : ''}">
      <div class="d-flex justify-content-between">
        <h5 class="text-left">Items</h5>
        <h5 class="text-right cursor" (click)="allergyShow()" *ngIf="restaurant.is_allergy">
          <i class="fa fa-info-circle" aria-hidden="true"></i>
          Allergy
        </h5>
      </div>
      <div class="accordion accordion-flush body-box-shadow" id="accordionExample">
        <div class="accordion-item" *ngFor="let category of categories">
          <h2 class="accordion-header" [id]="'category_' + category.id">
            <!-- [disabled]="category.id == expandableCategoryId"  [class.collapsed]="expandableCategoryId != category.id" -->
            <button (click)="clickOnAccordion(category.id)" class="accordion-button fw-bold cursor" type="button"
              data-bs-toggle="collapse" [data-bs-target]="'#category_body_' + category.id"
              [aria-controls]="'category_body_' + category.id">
              <div class="menu-name fw-bold d-flex-none">
                {{ category.category_name }}
                <p class="fw-light mb-0 pb-0 mt-1">
                  {{ category.description }}
                </p>
              </div>
            </button>
          </h2>
          <!-- [class.collapse]="expandableCategoryId != category.id"  -->
          <div [id]="'category_body_' + category.id" class="accordion-collapse category-section"
            [attr.data-category-id]="category.id" [aria-labelledby]="'category_' + category.id"
            data-bs-parent="#accordionExample">
            <div class="accordion-body p-1">
              <ul class="list-group list-group-flush">
                <li class="list-group-item" *ngFor="let menu of category.menu; trackBy: trackByFn">
                  <div class="menu-item d-flex justify-content-between pt-1 pb-1">
                    <div class="menu-image" *ngIf="restaurant.image_type == 'Yes' && menu?.image_url">
                      <img [src]="menu?.image_url" height="60" [alt]="menu?.menu_name" style="padding-right:10px;">
                    </div>
                    <div class="menu-details">
                      <div class="menu-name fw-bold">
                        {{ menu?.menu_name }}
                      </div>
                      <div class="menu-description" *ngIf="menu?.menu_description">
                        <span class="text-muted">
                          {{ menu?.menu_description }}
                        </span>
                      </div>
                      <div class="d-flex">
                        <span *ngIf="menu?.popular_dish =='Yes'"><img src="./assets/alergy_icon/popular.png"
                            title="Popular Dish" alt="popular dish" /></span>
                        <span class="alergy-icon" *ngIf="menu?.spicy_dish =='mild'"><img
                            src="./assets/alergy_icon/mint.png" title="Mild" alt="mild" /></span>
                        <span class="alergy-icon" *ngIf="menu?.spicy_dish =='medium'"><img
                            src="./assets/alergy_icon/med.png" title="Medium" alt="medium" /></span>
                        <span class="alergy-icon" *ngIf="menu?.spicy_dish =='extra_hot'"><img
                            src="./assets/alergy_icon/hot.png" title="Extra Hot" alt="extra hot" /></span>
                        <span class="alergy-icon" *ngIf="menu?.spicy_dish =='slightly_hot'"><img
                            src="./assets/alergy_icon/slightlyhot.png" title="Slightly Hot" alt="slightly hot" /></span>
                        <span class="alergy-icon" *ngIf="menu?.spicy_dish =='hot'"><img
                            src="./assets/alergy_icon/hot1.png" title="Hot" alt="hot" /></span>
                        <span class="alergy-icon" *ngIf="menu?.vegetarian"><img src="./assets/alergy_icon/vega.png"
                            title="Vegetarian" alt="vegetarian tiffintom" /></span>
                        <span class="alergy-icon" *ngIf="menu?.contains_nuts"><img
                            src="./assets/alergy_icon/Contains-nuts.png" title="Contains Nuts"
                            alt="contains nuts tiffintom" /></span>
                        <span class="alergy-icon" *ngIf="menu?.milk"><img src="./assets/alergy_icon/milk.png"
                            title="Milk" alt="milk tiffintom" /></span>
                        <span class="alergy-icon" *ngIf="menu?.mustard"><img src="./assets/alergy_icon/Mustard.png"
                            title="Mustard" alt="mustard tiffintom" /></span>
                        <span class="alergy-icon" *ngIf="menu?.eggs"><img src="./assets/alergy_icon/eggs.png"
                            title="Eggs" alt="eggs tiffintom" /></span>
                        <span class="alergy-icon" *ngIf="menu?.fish"><img src="./assets/alergy_icon/fish.png"
                            title="Fish" alt="fish tiffintom" /></span>
                        <span class="alergy-icon" *ngIf="menu?.gluten_free"><img
                            src="./assets/alergy_icon/Gluten-Free.png" title="Gluten-Free"
                            alt="gluten free tiffintom" /></span>
                        <span class="alergy-icon" *ngIf="menu?.vegan"><img src="./assets/alergy_icon/vegan.png"
                            title="Vegan" alt="vegan tiffintom" /></span>
                        <span class="alergy-icon" *ngIf="menu?.halal"><img src="./assets/alergy_icon/halal.png"
                            title="Halal" alt="halal tiffintom" /></span>
                        <span class="alergy-icon" *ngIf="menu?.koshar"><img src="./assets/alergy_icon/keshar.png"
                            title="Koshar" alt="koshar tiffintom" /></span>
                      </div>
                      <ng-container *ngIf="menu.variants?.length > 1">
                        <div class="w-100 variants d-flex align-items-center justify-content-between pt-1 pb-1"
                          *ngFor="let variant of menu.variants ; trackBy: trackByFn">
                          <div class="variant-name">
                            {{ variant?.sub_name }}
                          </div>
                          <div class="variant-price d-flex" *ngIf="menu.product_percentage <= 0">
                            {{ variant.orginal_price | currency: "GBP" }}
                            <div class="add-item align-items-center cursor" (click)="
                                addItemToCart(menuModal,menu,menu.id,variant.id)"
                              *ngIf="restaurant.online_order == 'Yes'">
                              <img src="./assets/icons/plus.png" alt="Add Item" class="mx-2"
                                *ngIf="restaurant.online_order == 'Yes'" />
                            </div>
                          </div>
                          <div class="variant-price d-flex" *ngIf="menu.product_percentage > 0">
                            <span class="px-1" style="text-decoration: line-through;">{{ variant.orginal_price |
                              currency: "GBP"
                              }}</span>
                            {{ (variant.orginal_price - (variant.orginal_price * menu.product_percentage /
                            100)) | currency: "GBP" }}
                            <div class="add-item align-items-center cursor" (click)="
                                addItemToCart(menuModal,menu,menu.id,variant.id)"
                              *ngIf="restaurant.online_order == 'Yes'">
                              <img src="./assets/icons/plus.png" alt="Add Item" class="mx-2"
                                *ngIf="restaurant.online_order == 'Yes'" />
                            </div>
                          </div>
                        </div>
                      </ng-container>
                    </div>
                    <div class="menu-price d-flex" *ngIf="menu.variants?.length == 1 && menu.product_percentage <= 0 ">
                      {{ menu.variants[0].orginal_price | currency: "GBP" }}
                      <div class="add-item align-items-center cursor" *ngIf="menu.variants?.length == 1"
                        (click)="addItemToCart(menuModal,menu,menu.id,menu.variants[0].id)">
                        <img src="./assets/icons/plus.png" alt="Add Item" class="mx-2"
                          *ngIf="restaurant.online_order == 'Yes'" />
                      </div>
                    </div>
                    <div class="menu-price d-flex" *ngIf="menu.variants?.length == 1 && menu.product_percentage > 0 ">
                      <span class="px-1" style="text-decoration: line-through;">
                        {{ menu.variants[0].orginal_price | currency: "GBP" }}
                      </span>
                      {{ (menu.variants[0].orginal_price - (menu.variants[0].orginal_price * menu.product_percentage /
                      100)) | currency: "GBP" }}
                      <div class="add-item align-items-center cursor" *ngIf="menu.variants?.length == 1"
                        (click)="addItemToCart(menuModal,menu,menu.id,menu.variants[0].id)">
                        <img src="./assets/icons/plus.png" alt="Add Item" class="mx-2"
                          *ngIf="restaurant.online_order == 'Yes'" />
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12 empty-cart-cls text-center py-5" *ngIf="categories.length == 0">
        <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
        <p><strong>No Product(s) Found</strong></p>
      </div>
    </div>
    <div class="col-lg-4 d-none d-lg-block" *ngIf="restaurant.online_order == 'Yes'">
      <div class="sticky-top" style="top:55px;">
        <h5 class="text-center"> Your {{order.order_type | titlecase}} Order</h5>
        <div class="text-center sticky-top bg-white body-box-shadow rounded p-2">
          <div class="col-md-12 d-flex order-types border border-default rounded">
            <div class="fw-bold col-md-6 p-2 h-100 cursor rounded-start" (click)="orderType('pickup')"
              [ngClass]="{'bg-primary text-white' : order.order_type == 'pickup','pe-none' : restaurant.restaurant_pickup != 'Yes'}">
              <a>
                Pickup
                <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_pickup != 'Yes'">Unavailable</p>
                <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_pickup != 'No'">Est. {{
                  restaurant.estimate_time }} mins</p>
              </a>
            </div>
            <div class="fw-bold col-md-6 p-2 h-100 cursor rounded-end" (click)="orderType('delivery')"
              [ngClass]="{'bg-primary text-white' : order.order_type == 'delivery','pe-none' : restaurant.restaurant_delivery != 'Yes'}">
              <a>
                Delivery
                <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_delivery != 'Yes'">Unavailable</p>
                <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_delivery != 'No'">Est. {{
                  restaurant.pickup_estimate_time }} mins</p>
              </a>
            </div>
          </div>
          <div class="col-md-12 text-primary fw-bold p-2 border-bottom">
            You have {{carts.length > 0 ? carts.length :''}} items
          </div>
          <div class="card-body cart p-0" *ngIf="carts.length != 0"
            style="max-height: calc(100vh - 36vh);overflow-y: auto;">
            <div class="col-sm-12 text-start" *ngIf="carts.length > 0">
              <div class="fw-bold p-1 col-sm-12 border-bottom"
                *ngFor=" let cart of carts; let i=index ; trackBy: trackByFn">
                <div class="col-sm-12 col-md-12 d-flex p-1">
                  <div class="col-sm-1 col-md-1">{{i+1}}</div>
                  <div class="col-sm-11 col-md-11">
                    <div class="w-100">{{cart.menu_name}}</div>
                    <div class="w-100" style="font-size: 12px;color: #999;">{{cart.subaddons_name}}</div>
                  </div>
                </div>
                <div class="col-sm-12 col-md-12 d-flex">
                  <div class="col-sm-1 col-md-1"></div>
                  <a class="col-sm-1 col-md-1 text-center cursor" (click)="updateToCart(cart,'remove')">
                    <img src="./assets/icons/minus.png" alt="item plus icon tiffintom">
                  </a>
                  <span class="col-sm-1 col-md-1 text-center">{{cart.quantity}}</span>
                  <a class="col-sm-1 col-md-1 text-center cursor" (click)="updateToCart(cart,'add')">
                    <img src="./assets/icons/plus.png" alt="item plus icon tiffintom">
                  </a>
                  <div class="col-sm-5 col-md-5"></div>
                  <div class="col-sm-3 col-md-3 d-flex">
                    <span class="col-sm-9 col-md-9 text-center">{{convertNumber(cart.total_price)}}</span>
                    <div class="col-sm-2 col-md-2">
                      <a class="menu-item-qty-icon cursor" (click)="updateToCart(cart,'delete')">
                        <img src="./assets/icons/x-mark.png" alt="clear product from cart tiffintom">
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <span class="col-md-12 fw-bold p-3 text-primary">Did you forget ?</span> -->
              <span class="col-12 fw-bold text-primary px-3 py-1 mb-0" *ngIf="restaurant.suggest_product_count > 0">Did
                you forget ?</span>
              <div class="col-12" *ngFor="let category of categories;let i=index; trackBy: trackByFn">
                <div class="col-12" *ngFor="let menu of category.menu;let j=idx; trackBy: trackByFn">
                  <p class="col-12 fw-bold px-3 py-1 mb-0 text-default"
                    *ngIf="menu.menu_addon == 'No' && menu.price_option == 'single' && menu.is_suggest == '1'">
                    <span class="d-flex justify-content-between">
                      <span class="text-start">{{ menu?.menu_name }}</span>
                      <span class="text-end">
                        <div class="add-item align-items-center cursor" *ngIf="menu.variants?.length == 1" (click)="
                        addItemToCart(menuModal,menu,menu.id,menu.variants[0].id)">
                          <img src="./assets/icons/plus.png" alt="Add Item" class="mx-2" />
                        </div>
                      </span>
                    </span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12"
            *ngIf="restaurant.online_order == 'Yes' && carts.length != 0 && order.order_type == 'delivery'">
            <button class="btn bg-primary text-white w-100 fw-bold p-2" routerLink="/pre-checkout"
              [disabled]="getGrandTotal() < restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes'"
              [ngClass]="{'disabled': getGrandTotal() < restaurant.minimum_order,'disabled': restaurant.restaurant_delivery != 'Yes'}">
              <p class="m-0 d-flex justify-content-between fs-5"><span class="text-start">
                  {{ restaurant.currentStatus == 'Open' ? 'Total' : 'Pre Order'}}
                </span> <span class="spacing text-end">{{convertNumber(getGrandTotal())}}</span></p>
            </button>
          </div>
          <div class="col-md-12"
            *ngIf="restaurant.online_order == 'Yes' && carts.length != 0 && order.order_type == 'pickup'">
            <button class="btn bg-primary text-white w-100 fw-bold p-2" routerLink="/pre-checkout"
              [disabled]="restaurant.restaurant_pickup != 'Yes'"
              [ngClass]="{'disabled': restaurant.restaurant_pickup != 'Yes'}">
              <p class="m-0 d-flex justify-content-between fs-5"><span class="text-start">
                  {{ restaurant.currentStatus == 'Open' ? 'Total' : 'Pre Order' }}
                </span> <span class="spacing text-end">{{convertNumber(getGrandTotal())}}</span></p>
            </button>
          </div>
          <div class="col-md-12 p-1 text-start"
            *ngIf="carts.length != 0 && order.order_type == 'delivery' && restaurant.minimum_order >= getGrandTotal()">
            <span>Minimum Order {{convertNumber(restaurant.minimum_order)}}</span>
          </div>
          <div class="col-sm-12 empty-cart-cls text-center" *ngIf="carts.length == 0">
            <img src="assets/cartitem.png" class="img-fluid m-4">
            <p><strong>No Item(s) Added</strong></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="mobile-nav-button text-end fixed-bottom d-lg-block d-sm-none d-xs-none d-md-none d-xl-none m-0 p-0"
    *ngIf="mobile && restaurant.online_order == 'Yes' && carts.length != 0 && order.order_type == 'delivery'">
    <button class="btn bg-primary text-white w-100 fw-bold p-2" routerLink="/pre-checkout"
      [disabled]="getGrandTotal() <= restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes'"
      [ngClass]="{'disabled': getGrandTotal() < restaurant.minimum_order,'disabled': restaurant.restaurant_delivery != 'Yes'}">
      <p class="m-0 d-flex justify-content-between fs-5">
        <span class="text-start">{{ restaurant.currentStatus == 'Open' ? 'Total' : 'Pre Order'}}</span>
        <span class="spacing text-end">{{convertNumber(getGrandTotal())}}</span>
      </p>
    </button>
  </div>
  <div class="mobile-nav-button text-end fixed-bottom d-lg-block d-sm-none d-xs-none d-md-none d-xl-none m-0 p-0"
    *ngIf="mobile && restaurant.online_order == 'Yes' && carts.length != 0 && order.order_type == 'pickup'">
    <button class="btn bg-primary text-white w-100 fw-bold p-2" routerLink="/pre-checkout"
      [disabled]="restaurant.restaurant_pickup != 'Yes'"
      [ngClass]="{'disabled': restaurant.restaurant_pickup != 'Yes'}">
      <p class="m-0 d-flex justify-content-between fs-5"><span class="text-start">
          {{ restaurant.currentStatus == 'Open' ? 'Total' : 'Pre Order' }}
        </span> <span class="spacing text-end">{{convertNumber(getGrandTotal())}}</span></p>
    </button>
  </div>
  <div class="mobile-nav-button text-end fixed-bottom d-lg-block d-sm-none d-xs-none d-md-none d-xl-none px-3"
    style="bottom: 60px;" *ngIf="mobile">
    <button class="toggler rounded-pill bg-primary text-white p-2 w-25 text-center cursor" (click)="clickcategory()">
      <span class="rounded-pill">Category</span>
    </button>
    <div class="bg-primary footer-category" [ngClass]="{'d-block' : showCategory,'d-none' : !showCategory}">
      <div *ngFor="let category of categories; trackBy: trackByFn" class="border-bottom cursor"
        style="word-break: break-all;">
        <a (click)="clickOnAccordion(category.id)" class="btn fw-bold text-white">
          {{ category.category_name }}
        </a>
      </div>
    </div>
  </div>
</div>

<ng-template #menuModal let-modal>
  <div class="row text-center align-middle justify-content-center" *ngIf="isModelLoading">
    <div class="col-md-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class="modal-header bg-primary text-white" *ngIf="!isModelLoading">
    <h4 class="modal-title" id="modal-basic-title" *ngIf="selectedMenu && selectedMenu.product_percentage <= 0">
      {{ selectedMenu.menu_name }} : {{selectedVariant?.orginal_price | currency: "GBP"}}
    </h4>
    <h4 class="modal-title" id="modal-basic-title" *ngIf="selectedMenu && selectedMenu.product_percentage > 0">
      {{ selectedMenu.menu_name }} :
      <span class="px-1" style="text-decoration: line-through;">{{ selectedVariant?.orginal_price |
        currency: "GBP" }}</span>
      {{ (selectedVariant?.orginal_price - (selectedVariant?.orginal_price * selectedMenu?.product_percentage /
      100)) | currency: "GBP" }}
    </h4>
    <button type="button" class="close bg-primary text-white cursor btn" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body" *ngIf="selectedVariant && !isModelLoading">
    <ul class="list-group border-0">
      <li [class.active]="expandableMainAddonId == mainAddon.id"
        class="cursor bg-light list-group-item border-0 text-dark" style="margin: 0 0 10px 0;"
        *ngFor="let mainAddon of selectedVariant.main_addons; let i = index" (click)="clickOnMainAddon(mainAddon.id)">
        {{ mainAddon?.mainaddons_name }} <small class="text-danger"
          *ngIf="mainAddon.mainaddons_mini_count!= 0">Required</small>
        <div *ngIf="mainAddon?.sub_addons?.length > 0"
          [ngClass]="{'d-block' : expandableMainAddonId == mainAddon.id,'d-none' : expandableMainAddonId != mainAddon.id}">
          <div *ngFor="let subAddon of mainAddon.sub_addons">
            <div class="radio radio-inline" *ngIf="mainAddon.mainaddons_count <= 1"
              style="padding: 0px 10px 0px;margin: 7px 0 2px;">
              <input type="radio" id="subaddon_{{subAddon?.id}}" class="addon_ss subaddon_{{subAddon?.id}}"
                [(ngModel)]="mainAddon.selectedSubAddonId" name="subaddon_radio_{{i}}" [value]="subAddon?.id">
              <label for="subaddon_{{subAddon?.id}}" style="padding-left: 15px;">{{ subAddon?.subaddons_name }}
                ( {{convertNumber(subAddon?.subaddons_price)}} )</label>
            </div>
            <div class="checkbox checkbox-inline" *ngIf="mainAddon.mainaddons_count > 1"
              style="padding: 0px 10px 0px;margin: 7px 0 2px;">
              <input type="checkbox" id="subaddon_{{subAddon?.id}}" class="addon_ss subaddon_{{subAddon?.id}}"
                [(ngModel)]="subAddon.selected" name="subaddon_radio_{{i}}">
              <label for="subaddon_{{subAddon?.id}}" style="padding-left: 15px;">{{ subAddon?.subaddons_name }}
                ( {{convertNumber(subAddon?.subaddons_price)}} )</label>
            </div>
          </div>
          <small class="text-danger" *ngIf="mainAddon.max_error">You should select maximum
            {{mainAddon.mainaddons_count }} addons</small>
          <small class="text-danger" *ngIf="mainAddon.min_error">You should select minimum
            {{mainAddon.mainaddons_mini_count }} addons</small>
        </div>
      </li>
    </ul>
  </div>
  <div class="modal-footer" *ngIf="!isModelLoading">
    <button type="button" class="btn btn-primary text-white cursor" (click)="validate(menuModal)">
      Add To Cart
    </button>
  </div>
</ng-template>

<ng-template #otpModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      <span class="fw-bold"
        *ngIf="(restaurant?.site_setting?.signup_verify_type == 'phone' || restaurant?.site_setting?.signup_verify_type == 'both') && !user.phone_verify">Phone
      </span>
      <span class="fw-bold"
        *ngIf="restaurant?.site_setting?.signup_verify_type == 'both' && !user.phone_verify && !user.email_verify"> &
      </span>
      <span class="fw-bold"
        *ngIf="(restaurant?.site_setting?.signup_verify_type == 'both' || restaurant?.site_setting?.signup_verify_type == 'mail') && !user.email_verify">Email
      </span>
      Verify
    </h4>
    <button type="button" class="close bg-primary text-white border-primary cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <p>Your otp has been sent on this
      <span class="fw-bold"
        *ngIf="(restaurant?.site_setting?.signup_verify_type == 'phone' || restaurant?.site_setting?.signup_verify_type == 'both') && !user.phone_verify">{{user.phone_number}}
      </span>
      <span class="fw-bold"
        *ngIf="restaurant?.site_setting?.signup_verify_type == 'both' && !user.phone_verify && !user.email_verify"> OR
      </span>
      <span class="fw-bold"
        *ngIf="(restaurant?.site_setting?.signup_verify_type == 'both' || restaurant?.site_setting?.signup_verify_type == 'mail') && !user.email_verify">{{user.username}}
      </span>
      <a (click)="profileUpdate(profileModal)" class="u-1 cursor"
        *ngIf="(restaurant?.site_setting?.signup_verify_type == 'phone' || restaurant?.site_setting?.signup_verify_type=='both') && !user.phone_verify">Edit</a>
    </p>
    <div class=" col-sm-12 col-xs-12 form-group"
      *ngIf="(restaurant?.site_setting?.signup_verify_type == 'phone' || restaurant?.site_setting?.signup_verify_type == 'both') && !user.phone_verify">
      <input type="text" class="form-control col-md-8" id="otp" name="otp" [(ngModel)]="verifyOtp"
        placeholder="Please enter phone verification code" />
    </div>
    <div class=" col-sm-12 col-xs-12 form-group mt-2"
      *ngIf="(restaurant?.site_setting?.signup_verify_type == 'both' || restaurant?.site_setting?.signup_verify_type == 'mail') && !user.email_verify">
      <input type="text" class="form-control col-md-8" id="email_otp" name="email_otp" [(ngModel)]="user.email_otp"
        placeholder="Please enter email verification code" />
    </div>
    <div *ngIf="Modelotperror">
      <span class="text-danger">{{ Modelotperror }}</span>
    </div>
  </div>
  <div class="modal-footer justify-content-between">
    <button type="button" [disabled]="isModelOtpLoading"
      class="btn btn-outline-primary bg-primary text-white text-start cursor" (click)="resendOtp()">
      Re-send
    </button>
    <div class="ms-2 spinner-border text-primary" *ngIf="isModelOtpLoading" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <button type="button" [disabled]="isModelOtpLoading" class="cursor btn btn-outline-primary bg-primary text-white"
      (click)="validateOtp()">
      submit
    </button>
  </div>
</ng-template>

<ng-template #profileModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Profile Update
    </h4>
  </div>
  <div class="modal-body">
    <form nz-form class="login-form mx-auto mb-1" #userForm="ngForm"
      (ngSubmit)="userForm.form.valid && updateUser(userForm)">

      <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="false">
        <label class="col-md-4">First Name</label>
        <input type="text" class="form-control col-md-8" id="first_name" name="first_name" [(ngModel)]="user.first_name"
          required #first_name="ngModel" [ngClass]="{ 'is-invalid': userForm.submitted && first_name.invalid }" />
        <div class="invalid-feedback" *ngIf="userForm.submitted && first_name.invalid">
          <span *ngIf="first_name.errors.required">First Name is required</span>
        </div>
      </div>

      <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="false">
        <label class="col-md-4">Last Name</label>
        <input type="text" class="form-control col-md-8" id="last_name" name="last_name" [(ngModel)]="user.last_name"
          required #last_name="ngModel" [ngClass]="{ 'is-invalid': userForm.submitted && last_name.invalid }" />
        <div class="invalid-feedback" *ngIf="userForm.submitted && last_name.invalid">
          <span *ngIf="last_name.errors.required">Last Name is required</span>
        </div>
      </div>

      <div class="col-sm-12 col-xs-12 form-group mb-3">
        <label class="col-md-4">Phone Number</label>
        <input type="text" class="form-control col-md-8" (keypress)="keyPress($event)" minlength="10" maxlength="11"
          id="phone_number" name="phone_number" [(ngModel)]="user.phone_number" required #phone_number="ngModel"
          [ngClass]="{ 'is-invalid': userForm.submitted && phone_number.invalid }" />
        <div class="invalid-feedback" *ngIf="userForm.submitted && phone_number.invalid">
          <span *ngIf="phone_number.errors.required">Phone Number is required</span>
          <span *ngIf="phone_number.errors.minlength">Phone Number minimum 10 digit</span>
          <span *ngIf="phone_number.errors.maxlength">Phone Number minimum 11 digit</span>
        </div>
      </div>

      <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="false">
        <label class="col-md-4">Username</label>
        <input type="text" class="form-control col-md-8" id="username" name="username" [(ngModel)]="user.username"
          readonly />
      </div>

      <div *ngIf="ModelProfileerror">
        <span class="text-danger">{{ ModelProfileerror }}</span>
      </div>

      <div class="col-sm-12 col-xs-12 form-group">
        <button type=" button" [disabled]="isProfileLoading"
          class="cursor btn btn-outline-primary bg-primary text-white">
          <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isProfileLoading"
            role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          Update
        </button>
      </div>

    </form>
  </div>
  <!-- <div class="modal-footer justify-content-between">
    <div class="ms-2 spinner-border text-primary" *ngIf="isModelProfileLoading" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <button type="button" [disabled]="isModelProfileLoading" class="btn btn-outline-dark bg-primary text-white"
      (click)="validateProfile()">
      Save
    </button>
  </div> -->
</ng-template>

<ng-template #allergyModal let-modal>
  <div class="modal-header bg-primary text-white text-center justify-content-center">
    <h4 class="modal-title" id="modal-basic-title">
      Do you have a food allergy?
    </h4>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="restaurant.allergy_message">
      {{restaurant.allergy_message}}
    </div>

    <div class="col-sm-12 col-xs-12 form-group pt-3">
      <button type="button" class="btn btn-outline-primary bg-primary text-white w-100 cursor" aria-label="Close"
        (click)="modal.dismiss('Cross click')" style="border-radius: 1cm;border: none;">
        Close
      </button>
    </div>
  </div>

</ng-template>

<ng-template #itemNotAvailableModal let-modal>
  <div class="modal-header bg-primary text-white text-center justify-content-center">
    <h4 class="modal-title" id="modal-basic-title">
      Not Available
    </h4>
  </div>

  <div class="modal-body">
    <!-- Message for only Order Type mismatch -->
    <div class="col-sm-12 col-xs-12 form-group mb-3 text-center fw-bold pt-2"
      *ngIf="hasOrderTypeMismatch && !hasDayMismatch">
      Some items in your cart are not available for the selected order type.
    </div>

    <!-- Message for only Day mismatch -->
    <div class="col-sm-12 col-xs-12 form-group mb-3 text-center fw-bold pt-2"
      *ngIf="hasDayMismatch && !hasOrderTypeMismatch">
      Some items are not available on the selected day.
    </div>

    <!-- Message for both mismatches -->
    <div class="col-sm-12 col-xs-12 form-group mb-3 text-center fw-bold pt-2"
      *ngIf="hasOrderTypeMismatch && hasDayMismatch">
      Some items are not available for the selected order type or delivery day.
    </div>

    <!-- Buttons -->
    <div class="row pt-3">
      <div class="col-sm-6 col-xs-6 form-group pt-3">
        <button type="button" class="btn btn-primary w-100" (click)="handleItemNotAvailable('no')"
          style="border-radius: 1cm; border: none;">
          No
        </button>
      </div>
      <div class="col-sm-6 col-xs-6 form-group pt-3">
        <button type="button" class="btn btn-primary w-100" (click)="handleItemNotAvailable('yes')"
          style="border-radius: 1cm; border: none;">
          Yes
        </button>
      </div>
    </div>
  </div>
</ng-template>