.main-body {
  background: linear-gradient(to right, var(--primary), var(--primary));
  // background: #ffffff;
  background: rgb(240, 242, 245);
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
  font-family: "Poppins", sans-serif;
  height: 85vh;
  min-height: 35rem;
  // padding: 1rem 0rem;
  margin: 1rem 0rem 1rem 0rem;

  @media screen and (max-width: 768px) {
    min-height: 50rem;
  }
}

h1 {
  font-weight: bold;
  margin: 0;
  font-size: 1.8em;
  margin-bottom: 10px;
}

h1 span {
  font-size: 0.9em;
  margin-left: 5px;
}

p {
  font-size: 14px;
  font-weight: 300;
  line-height: 20px;
  letter-spacing: 0.5px;
  margin: 20px 0 30px;
}

span {
  font-size: 12px;
  margin-bottom: 10px;
}

a {
  color: #333;
  font-size: 14px;
  text-decoration: none;
  margin: 15px 0;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary);
}

.signIn-button {
  border-radius: 20px;
  border: 1px solid var(--primary);
  background-color: var(--primary);
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  padding: 12px 45px;
  letter-spacing: 1px;
  text-transform: uppercase;
  transition: transform 80ms ease-in, background-color 0.3s ease,
    border-color 0.3s ease;
  cursor: pointer;
  margin-top: 10px;
  width: 100%;
}

.signIn-button:active {
  transform: scale(0.95);
}

.signIn-button:focus {
  outline: none;
}

.signIn-button.ghost {
  background-color: transparent;
  border-color: #ffffff;
}

.signIn-button.ghost:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

form {
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 0 50px;
  height: 100%;
  min-height: fit-content;
}

.container {
  background-color: #fff;
  border-radius: 1rem;
  box-shadow: 0 14px 28px rgba(0, 0, 0, 0.2), 0 10px 10px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  width: 65rem;
  // max-width: 100%;
  // min-height: 80vh;
  height: min-content;
  min-height: 35rem;
}

.form-container {
  position: absolute;
  top: 0;
  height: 100%;
  transition: all 0.6s ease-in-out;
}

.sign-in-container {
  left: 0;
  width: 50%;
  z-index: 2;
}

.sign-up-container {
  left: 0;
  width: 50%;
  opacity: 0;
  z-index: 1;
}

.overlay-container {
  position: absolute;
  top: 0;
  left: 50%;
  width: 50%;
  height: 100%;
  overflow: hidden;
  transition: transform 0.6s ease-in-out;
  z-index: 100;
}

.overlay {
  background: var(--primary);
  background: -webkit-linear-gradient(to right, var(--primary), var(--primary));
  background: linear-gradient(to right, var(--primary), var(--primary));
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 0 0;
  color: #ffffff;
  position: relative;
  left: -100%;
  height: 100%;
  width: 200%;
  transform: translateX(0);
  transition: transform 0.6s ease-in-out;
}

.overlay-panel {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 0 40px;
  text-align: center;
  top: 0;
  height: 100%;
  width: 50%;
  transform: translateX(0);
  transition: transform 0.6s ease-in-out;
}

.overlay-left {
  transform: translateX(-20%);
}

.overlay-right {
  right: 0;

  transform: translateX(0);
}

/* --- Animation --- */
/* Move Sign-in to the right */
.container.right-panel-active .sign-in-container {
  transform: translateX(100%);
}

/* Move overlay to the left */
.container.right-panel-active .overlay-container {
  transform: translateX(-100%);
}

/* Bring Sign-up over Sign-in */
.container.right-panel-active .sign-up-container {
  transform: translateX(100%);
  opacity: 1;
  z-index: 5;
  animation: show 0.6s;
}

/* Move overlay back to the right */
.container.right-panel-active .overlay {
  transform: translateX(50%);
}

/* Move overlay panels */
.container.right-panel-active .overlay-left {
  transform: translateX(0);
}

.container.right-panel-active .overlay-right {
  transform: translateX(20%); /* Move slightly off-screen */
}

@keyframes show {
  0%,
  49.99% {
    opacity: 0;
    z-index: 1;
  }

  50%,
  100% {
    opacity: 1;
    z-index: 5;
  }
}

/* Social Icons Styling (Optional) */
.social-container {
  margin: 20px 0;
}

.social-container a {
  border: 1px solid #dddddd;
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin: 0 5px;
  height: 40px;
  width: 40px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.social-container a:hover {
  background-color: #eee;
  color: var(--primary);
  border-color: transparent;
}

/* Copyright Notice */
.copyright {
  position: fixed;
  bottom: 10px;
  right: 15px;
  color: #aaa;
  font-size: 0.8em;
  font-weight: 300;
  z-index: 1001;
}

.display-singup-singin {
  display: none;
}

/* Responsive adjustments (Example) */
@media (max-width: 768px) {
  // ========================
  .overlay-container {
    display: none;
  }

  .sign-in-container {
    width: 100%;
  }

  .sign-up-container {
    width: 100%;
  }

  .signIn-button.ghost {
    background-color: #ffffff;
    color: var(--primary);
    border-color: var(--primary);
  }

  .sign-up-container {
    transform: translateX(-100%);
    left: 0;
    width: 100%;
    opacity: 0;
    z-index: 1;
  }

  .container.right-panel-active .sign-up-container {
    transform: translateX(0%);
    opacity: 1;
    z-index: 5;
    animation: show 0.6s;
  }

  .display-singup-singin {
    display: block;
  }

  .sign-in-container {
    form {
      padding: 0 25px !important;
      // justify-content: flex-start;
    }
  }

  .sign-up-container {
    form {
      padding: 0px !important;
      // justify-content: flex-start;
    }
  }

  // ========================

  .container {
    width: 90%;
    border-top: 3px solid var(--primary);
    border-bottom: 3px solid var(--primary);

    min-height: -webkit-fill-available;
    min-height: -moz-available;
    min-height: stretch;

    &.right-panel-active {
      height: -webkit-fill-available;
      height: -moz-available;
      height: stretch;
      min-height: 100vh;
    }
  }

  .form-container {
    padding: 0 20px;
  }

  h1 {
    font-size: 1.6em;
  }

  .overlay-panel {
    padding: 0 20px;
  }
}

@media (max-width: 576px) {
  .container {
    min-height: -webkit-fill-available;
    min-height: -moz-available;
    min-height: stretch;

    &.right-panel-active {
      min-height: 100vh;
    }
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 1.4em;
  }

  .signIn-button {
    padding: 10px 30px;
  }

  input {
    padding: 10px 12px;
  }

  .social-container a {
    height: 35px;
    width: 35px;
  }

  .container {
    height: calc(100vh - 110px);
  }

  .copyright {
    font-size: 0.7em;
    bottom: 5px;
    right: 10px;
  }
}

.role-btn-group {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  row-gap: 10px;
  column-gap: 10px;
  margin-top: 1rem;

  span {
    cursor: pointer;
    padding: 0.25rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #ccc;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      color: #fdc500;
      border-color: #fdc500;
    }

    &.active {
      background-color: #fdc500;
      color: #ffffff;
      border-color: #fdc500;
    }
  }
}

.img-size {
  width: 10rem;
  height: 3rem;
}
