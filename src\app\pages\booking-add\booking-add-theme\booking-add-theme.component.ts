import { Component, OnInit } from '@angular/core';
import { UserService } from 'src/app/core/services/user.service';

@Component({
  selector: 'app-booking-add-theme',
  templateUrl: './booking-add-theme.component.html',
  styleUrls: ['./booking-add-theme.component.scss']
})
export class BookingAddThemeComponent implements OnInit {
  menuTheme: string;

  constructor(public userService: UserService) { }

  ngOnInit(): void {
    this.userService.getUserTheme().subscribe(res => {
      this.menuTheme = res;
    })
  }
}
