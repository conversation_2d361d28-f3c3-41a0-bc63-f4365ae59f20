import { Component, NgZone } from '@angular/core';
import { CanonicalService } from './shared/canonical.service';
import { LoaderService } from './core/services/loader.service';
import { environment } from 'src/environments/environment';
import { RestaurantService } from './core/services/restaurant.service';
import { NotificationService } from './core/services/notification.service';
import { finalize } from 'rxjs/operators';
import { Restaurant } from './core/models/restaurant';
import { UserService } from './core/services/user.service';
import { MessagingService } from './core/services/messaging.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent {
  title = 'tiffintom-individual';
  loading = false;

  restaurant_id: string;
  restaurant: Restaurant = new Restaurant();

  constructor(
    private zone: NgZone,
    private loaderService: LoaderService,
    private canonicalService: CanonicalService,
    private restaurantService: RestaurantService,
    private notificationService: NotificationService,
    private userService: UserService,
    private messagingService: MessagingService,
  ) { }

  ngOnInit() {
    this.canonicalService.setCanonicalURL();

    this.loaderService.loading$.subscribe(isLoading => {
      this.zone.run(() => {
        this.loading = isLoading;
      });
    });

    this.restaurant_id = environment.googleFirebase;
    this.fetchRestaurant();
  }

  fetchRestaurant() {
    this.userService.setLoader(true);
    this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => { }))
      .subscribe(res => {
        this.restaurant = res;
        this.userService.setUserTheme(this.restaurant.web_theme);
        this.userService.setLoader(false);
      }, err => {
        this.messagingService.error(err);
      })
  }
}
