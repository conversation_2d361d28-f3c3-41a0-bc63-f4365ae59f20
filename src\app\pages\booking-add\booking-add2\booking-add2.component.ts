import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import {
  NgbModal,
  ModalDismissReasons,
  NgbModalOptions,
  NgbActiveModal,
} from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { Booking } from 'src/app/core/models/booking';
import { BookingService } from 'src/app/core/services/booking.service';
import { Title, Meta } from '@angular/platform-browser';
import { NotificationService } from 'src/app/core/services/notification.service';
import { NgForm } from '@angular/forms';
import { StripeCustomer } from 'src/app/core/models/stripe-customer';
import { StripeCustomerService } from 'src/app/core/services/stripe-customer.service';
import { MessagingService } from 'src/app/core/services/messaging.service';
declare var Stripe;

@Component({
  selector: 'app-booking-add2',
  templateUrl: './booking-add2.component.html',
  styleUrls: ['./booking-add2.component.scss']
})
export class BookingAdd2Component implements OnInit {
  @ViewChild('otpModal', { static: true }) otpModal: ElementRef;

  subs = new Subscription();

  user: User;
  restaurant: Restaurant = new Restaurant();
  booking: Booking = new Booking();
  stripeCustomers: StripeCustomer[] = [];
  stripeCustomer: StripeCustomer = new StripeCustomer();
  addStripeCustomer: StripeCustomer;

  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;
  timeSlots = [];
  noOfGuest = [];
  minDate: any;
  maxDate: any;
  bookingDate: any;

  success = false;
  isLoading = false; error = null;
  isBookingLoading = false;
  isModelLoading = false; Modelerror = null;
  isModelOtpLoading = false; Modelotperror = null;
  isModelProfileLoading = true; ModelProfileerror = false;

  phoneTab = false;
  verifyOtp: string;
  bookingComplete: boolean = false;
  bookingPayment: boolean = false;

  publishKey: string;
  isConnect: string;
  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    public userService: UserService,
    private bookingService: BookingService,
    private restaurantService: RestaurantService,
    private stripeCustomerService: StripeCustomerService,
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    private currencyPipe: CurrencyPipe,
    private metaTagService: Meta,
    private titleService: Title,
    private notificationService: NotificationService,
    private messagingService: MessagingService
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    this.modalOptions = {
      backdrop: 'static',
      size: 'md',
      backdropClass: 'customBackdrop',
      windowClass: 'rounded-modal'
    };
    //Restaurant Find 
    this.fetchRestaurant();

    this.minDate = { year: new Date().getFullYear(), month: new Date().getMonth() + 1, day: new Date().getDate() };
    this.maxDate = { year: new Date().getFullYear(), month: new Date().getMonth() + 4, day: new Date(new Date().setDate(new Date().getDate())).getDate() };
    this.bookingDate = { year: new Date().getFullYear(), month: new Date().getMonth() + 1, day: new Date().getDate() };

    this.booking.restaurant_id = this.restaurant_id;
    this.booking.customer_id = this.user.id;
    this.booking.customer_name = this.user.first_name;
    this.booking.booking_email = this.user.username;
    this.booking.booking_phone = this.user.phone_number;

    this.booking.booking_date = this.convertToDate(new Date());
    if (this.booking.guest_count == undefined) {
      this.booking.guest_count = "";
    }
    if (this.booking.booking_time == undefined) {
      this.booking.booking_time = "";
    }
    //Time Slot Date Wise Find
    this.findTimeSlot();
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
        if (this.restaurant.meta_tag.booking_meta_title) {
          this.titleService.setTitle(this.restaurant.meta_tag.booking_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.meta_tag.booking_meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.meta_tag.booking_meta_description });
        }
        for (let i = 1; i <= this.restaurant.no_of_guest; i++) {
          this.noOfGuest.push(i);
        }

        if (this.restaurant.booking_payment != 'none') {
          this.fetchCards();
        }

        if (this.restaurant?.business?.connect_service) {
          this.publishKey = this.restaurant?.business?.connect_stripe_public_key
          this.isConnect = 'connect';
        } else {
          if (this.restaurant?.site_setting?.finance_stripe_mode == 'Test') {
            this.publishKey = this.restaurant?.site_setting?.finance_stripe_publishkeyTest
          } else {
            this.publishKey = this.restaurant?.site_setting?.finance_stripe_publishkey
          }
          this.isConnect = 'normal';
        }
        if (this.publishKey) {
          this.stripe = Stripe(this.publishKey);
        }
      }, err => this.error = err)
    );
  }

  onSelect(evt: any) {
    var deliveryDates = this.convertToDate(new Date(evt.year, evt.month - 1, evt.day));
    this.booking.booking_date = deliveryDates;
    this.findTimeSlot();
  }

  findTimeSlot() {
    this.subs.add(
      this.restaurantService.bookingtimeslot({ restaurant_id: this.restaurant_id, date: this.booking.booking_date }).
        pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            var lastChar = res.booking.slice(-1);
            if (lastChar == ',') {
              res.booking = res.booking.slice(0, -1); // trim last character
            }
            if (res.booking != '') {
              this.timeSlots = res.booking.split(',');
            } else {
              this.timeSlots = [];
              this.booking.booking_time = 'Close';
            }
            // if (res.delivery.today != '') {
            //   this.timeSlots = res.delivery.today.split(',');
            // } else {
            //   var lastChar = res.pickup.today.slice(-1);
            //   if (lastChar == ',') {
            //     res.pickup.today = res.pickup.today.slice(0, -1); // trim last character
            //   }

            //   if (res.pickup.today != '') {
            //     this.timeSlots = res.pickup.today.split(',');
            //   } else {
            //     this.timeSlots = [];
            //     this.booking.booking_time = 'Close';
            //   }
            // }
          },
          (err) => {
          }
        )
    )
  }

  otpSend() {
    this.phoneTab = true;
    if (this.restaurant.site_setting.booking_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.restaurant.site_setting.booking_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.restaurant.site_setting.booking_verify_type;
      }
    }

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.messagingService.success("Otp sent successfully !!");
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  resendOtp() {
    this.phoneTab = true;
    if (this.restaurant.site_setting.booking_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.restaurant.site_setting.booking_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.restaurant.site_setting.booking_verify_type;
      }
    }

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.messagingService.success("otp sent successfully !!");
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  validateOtp() {
    this.isModelOtpLoading = true;
    this.Modelotperror = false;

    if (this.restaurant.site_setting.booking_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.restaurant.site_setting.booking_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.restaurant.site_setting.booking_verify_type;
      }
    }

    if (!this.verifyOtp && (this.user.verify_type == 'both' || this.user.verify_type == 'phone')) {
      this.messagingService.error("Please enter phone verification code !!");
      this.isModelOtpLoading = false;
    } else if (!this.user.email_otp && (this.user.verify_type == 'both' || this.user.verify_type == 'email')) {
      this.messagingService.error("Please enter email verification code !!");
      this.isModelOtpLoading = false;
    } else {
      this.user.otp = this.verifyOtp
      this.subs.add(
        this.userService.varifyBothOtp(this.user).
          pipe(finalize(() => this.isModelOtpLoading = false))
          .subscribe(
            (res) => {
              this.verifyOtp = '';
              this.phoneTab = false;
              this.subs.add(this.userService.me()
                .pipe(finalize(() => this.isModelOtpLoading = false))
                .subscribe(res => {
                  this.user = res
                  this.messagingService.success("otp verify successfully !!");
                  this.validateBook();
                }, err => this.Modelotperror = err)
              );
              this.modalService.dismissAll();
            },
            (err) => {
              this.messagingService.error(err);
            }
          )
      )
    }
  }

  validateBook() {
    this.isBookingLoading = true; this.error = null;
    this.success = false;
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    if (!this.booking.guest_count || this.booking.guest_count == '') {
      this.error = 'Please select guest count';
      this.isBookingLoading = false
    }
    if (!this.booking.booking_time || this.booking.booking_time == 'Close') {
      this.error = 'Please select valid booking time';
      this.isBookingLoading = false
    }
    else if (!re.test(String(this.booking.booking_email).toLowerCase())) {
      this.error = 'Please enter valid email';
      this.isBookingLoading = false
    }
    else if (this.booking.booking_phone.length < 10) {
      this.error = 'Please enter valid phone minimum 10 digit';
      this.isBookingLoading = false
    } else {
      if ((((!this.user.phone_verify && this.restaurant.site_setting.booking_verify_type == 'phone') || !this.user.email_verify && this.restaurant.site_setting.booking_verify_type == 'mail') || (this.restaurant.site_setting.booking_verify_type == 'both' && (!this.user.phone_verify || !this.user.email_verify))) && this.restaurant.site_setting?.booking_verify == '1') {
        this.otpSend();
        this.modalService.open(this.otpModal, { backdrop: 'static', size: 'md', backdropClass: 'customBackdrop', keyboard: false, windowClass: 'rounded-modal' });
        return
      }

      this.booking.type = 'Web';
      if (this.restaurant.booking_payment == 'none') {
        this.booking.status = 'Pending';
      }

      if (this.booking.id) {
        this.subs.add(
          this.bookingService.update(this.booking).
            pipe(finalize(() => this.isBookingLoading = false))
            .subscribe(
              (res) => {
                this.booking = res;
                if (this.restaurant.booking_payment != 'none') {
                  this.bookingPayment = true;
                }
              },
              (err) => {
                this.messagingService.error(err);
              }
            )
        )
      } else {
        this.subs.add(
          this.bookingService.create(this.booking).
            pipe(finalize(() => this.isBookingLoading = false))
            .subscribe(
              (res) => {
                this.booking.id = res.id;
                if (this.restaurant.booking_payment != 'none') {
                  this.bookingPayment = true;
                } else {
                  this.messagingService.success("Your reservation created succefully!!");
                  // this.booking = new Booking();
                  this.booking.id = res.id;
                  var bookingId = btoa(res.id);
                  this.router.navigateByUrl(`/booking-detail/${bookingId}`);
                  this.bookingComplete = true;
                }
              },
              (err) => {
                this.messagingService.error(err);
              }
            )
        )
      }
    }
  }

  fetchCards() {
    this.subs.add(
      this.stripeCustomerService.get({ customer_id: this.user?.id, nopaginate: "1" }) // service_type: this.isConnect, 
        .pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.stripeCustomers = res;
            this.booking.card_id = this.stripeCustomers[0].id;
          },
          (err) => {
            this.stripeCustomers = [];
            this.booking.card_id = '';
          }
        )
    );
  }

  cardPaymentMethod(cardId: string) {
    this.booking.card_id = cardId;
  }

  checkPaymentMethod(payment_method_name: string) {
    this.booking.card_id = '';
  }

  initCard() {
    this.cardElement = <HTMLInputElement>(
      document.getElementById('customCardElement')
    );
    var elements = this.stripe.elements();
    this.card = elements.create('card', { hidePostalCode: true });
    this.card.mount(this.cardElement);
    this.isModelLoading = false;
  }

  addCard(model) {
    this.isModelLoading = true;
    this.addStripeCustomer = new StripeCustomer();
    this.openModal(model);
  }

  async handleForm(e) {
    e.preventDefault();
    this.isModelLoading = true;
    this.Modelerror = false;
    let createPaymentMethodPromise = this.stripe
      .createPaymentMethod({
        type: 'card',
        card: this.card,
      })
      .then((result) => {
        // this.createPaymentIntent(result.paymentMethod.id);
        if (!result.error) {
          this.stripeCustomer.customer_id = this.booking.customer_id;
          this.stripeCustomer.customer_name = this.booking.customer_name;
          this.stripeCustomer.stripe_token_id = result.paymentMethod.id;
          this.stripeCustomer.exp_month = result.paymentMethod.card.exp_month;
          this.stripeCustomer.exp_year = result.paymentMethod.card.exp_year;
          this.stripeCustomer.country = result.paymentMethod.card.country;
          this.stripeCustomer.card_brand = result.paymentMethod.card.brand;
          this.stripeCustomer.card_number = result.paymentMethod.card.last4;
          this.stripeCustomer.card_type = result.paymentMethod.card.funding;
          this.stripeCustomer.service_type = this.isConnect;
          this.subs.add(
            this.stripeCustomerService.create(this.stripeCustomer).
              pipe(finalize(() => this.isModelLoading = false))
              .subscribe(
                (res) => {
                  this.fetchCards();
                  this.messagingService.success("Card added successfully !!");
                  this.modalService.dismissAll();
                },
                (err) => {
                  this.Modelerror = err;
                }
              )
          )
        } else {
          this.isModelLoading = false
          this.Modelerror = result.error.message;
        }
      });
  }

  validateBooking() {
    this.stripeCustomer = this.stripeCustomers.find(card => card.id == this.booking.card_id);
    this.createPaymentIntent(this.stripeCustomer);
  }

  async createPaymentIntent(stripeCustomerNew) {
    this.isBookingLoading = true;

    this.stripeCustomer = stripeCustomerNew;
    this.stripeCustomer.restaurant_id = this.booking.restaurant_id;
    this.stripeCustomer.amount = this.restaurant.booking_payment == 'single' ? this.restaurant.booking_amount : (this.restaurant.booking_amount * parseFloat(this.booking.guest_count));
    this.stripeCustomer.booking_id = this.booking.id;
    this.subs.add(
      this.stripeCustomerService
        .payment_intent(this.stripeCustomer)
        .pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.continueToPayment(res.payment_intent_id);
          },
          (err) => {
            this.error = 'Sorry your Payment Faild! Please try again';
            this.isBookingLoading = false;
          }
        )
    );
  }

  async continueToPayment(paymentIntentId) {
    this.stripe.confirmCardPayment(paymentIntentId).then((result) => {
      if (result.error) {
        this.isBookingLoading = false;
        this.error = 'Something went wrong!';//result.error.message;
      } else {
        if (result.paymentIntent.status === 'succeeded') {

          this.isBookingLoading = true; this.error = null;
          this.booking.booking_amount = this.restaurant.booking_payment == 'single' ? this.restaurant.booking_amount : (this.restaurant.booking_amount * parseFloat(this.booking.guest_count));
          this.booking.txn_id = result.paymentIntent.id;
          this.booking.status = 'Pending';

          this.subs.add(
            this.bookingService.update(this.booking).
              pipe(finalize(() => this.isBookingLoading = false))
              .subscribe(
                (res) => {
                  this.messagingService.success("Your reservation placed successfully !!");
                  this.booking = res;
                  this.bookingPayment = false;
                  this.bookingComplete = true;
                  this.booking = new Booking();
                  this.booking.restaurant_id = this.restaurant_id;
                  this.booking.customer_id = this.user.id;
                  this.booking.customer_name = this.user.first_name;
                  this.booking.booking_email = this.user.username;
                  this.booking.booking_phone = this.user.phone_number;
                  var bookingId = btoa(res.id);
                  this.router.navigateByUrl(`/booking-detail/${bookingId}`);
                },
                (err) => {
                  this.messagingService.error(err);
                }
              )
          )
        }
      }
    });
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
    this.initCard();
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  profileUpdate(model) {
    this.modalService.dismissAll();
    this.openModal(model);
  }

  updateUser(form: NgForm) {
    this.isModelProfileLoading = true;
    this.ModelProfileerror = null;

    if (this.userService.user.phone_number != this.user.phone_number) {
      this.user.phone_verify = false;

      this.userService
        .disabledPhoneVerify(this.user)
        .pipe(finalize(() => (this.isModelProfileLoading = false)))
        .subscribe(
          (res) => { },
          (err) => { }
        );
    }

    this.userService
      .update(this.user)
      .pipe(finalize(() => (this.isModelProfileLoading = false)))
      .subscribe(
        (res) => {
          this.fetchMe();
          this.messagingService.success("Profile updated successfully !!");
          if ((((!this.user.phone_verify && this.restaurant.site_setting.booking_verify_type == 'phone') || !this.user.email_verify && this.restaurant.site_setting.booking_verify_type == 'mail') || (this.restaurant.site_setting.booking_verify_type == 'both' && (!this.user.phone_verify || !this.user.email_verify))) && this.restaurant.site_setting?.signup_verify == '1') {
            this.otpSend();
            this.modalService.dismissAll();
            this.modalService.open(this.otpModal, { backdrop: 'static', size: 'md', backdropClass: 'customBackdrop', keyboard: false, windowClass: 'rounded-modal' });
          }
        },
        (err) => {
          this.ModelProfileerror = err;
        }
      );
  }

  fetchMe() {
    this.subs.add(this.userService.me()
      .pipe(finalize(() => this.isModelOtpLoading = false))
      .subscribe(res => {
        this.userService.saveUser(res);
        this.user = res;
      }, err => this.Modelotperror = err)
    );
  }

  keyPress(event: any) {
    const pattern = /[0-9\+\-\ ]/;

    let inputChar = String.fromCharCode(event.charCode);
    if (event.keyCode != 8 && !pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  onlyNumeric(): boolean {
    if (Number(this.booking.booking_phone)) {
    } else {
      this.booking.booking_phone = '';
    }
    return false
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd', 'en_US')
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
