// @use 'style.scss'; // Path to _variables.scss Notice how we don't include the underscore or file extension

.menu-item {
  .menu-details {
    width: 100%;
  }

  .add-item {
    cursor: pointer;
  }
}

.fees-table {
  background: #fff;
  border-radius: 6px;
  overflow: auto;
  box-shadow: 0px 2px 5px rgba($color: #000000, $alpha: 0.10);
  text-align: center;

  tr td {
    padding: 8px !important;
    font-size: 15px;
    border-bottom: 1px solid #e1e1e1;
  }

  th {
    font-weight: 600;
    padding: 8px !important;
    text-transform: uppercase;
    background: linear-gradient(180deg, #fafafa, #f2f2f2);
  }
}
