::ng-deep .rounded-modal > .modal-dialog > .modal-content {
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.payment-details {
  background: #fff;
  border-radius: 1rem;
  overflow: auto;
  box-shadow: 0px 2px 5px rgba($color: #000000, $alpha: 0.1);
  padding: 1rem;
}

.billing-details {
  background: #fff;
  border-radius: 1rem;
  overflow: auto;
  box-shadow: 0px 2px 5px rgba($color: #000000, $alpha: 0.1);
  padding: 1rem;

  @media screen and (max-width: 576px) {
    padding: 1rem 0.5rem;
  }

  .delivery-pickup-wrapper {
    display: flex;
    align-items: center;
    column-gap: 0.5rem;
    border-radius: 2rem;
    padding: 3px;
    background: #efefef;
    width: fit-content;
    margin: 0 auto;
    margin-bottom: 1rem;

    .delivery-btn,
    .pickup-btn {
      background: transparent;
      color: #000;
      border: none;
      padding: 0.25rem 2rem;
      border-radius: 2rem;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 600;

      transition: all 0.3s ease;

      &.active {
        background: var(--primary);
        color: #fff;
        border: 1px solid #fff;
      }
    }
  }

  .cart-items-list {
    margin-top: 1rem;
  }

  .menu-name-truncate {
    // display: -webkit-box;
    // -webkit-line-clamp: 2;
    // -webkit-box-orient: vertical;
    // overflow: hidden;
    // text-overflow: ellipsis;

    // /* Firefox fallback */
    // display: -moz-box;
    // -moz-box-orient: vertical;
    // -moz-line-clamp: 2;
  }

  .menu-description-truncate {
    // display: -webkit-box;
    // -webkit-line-clamp: 2;
    // -webkit-box-orient: vertical;
    // overflow: hidden;
    // text-overflow: ellipsis;

    // /* Firefox fallback */
    // display: -moz-box;
    // -moz-box-orient: vertical;
    // -moz-line-clamp: 2;
  }

  .voucher-wrapper {
    position: relative;
    width: 100%;
  }

  .voucher-input {
    border-radius: 2rem !important;
    padding: 10px 100px 10px 10px;
  }

  .submit-btn {
    position: absolute;
    top: 50%;
    right: 6px;
    transform: translateY(-50%);
    background: var(--primary);
    color: white;
    border: none;
    padding: 6px 16px;
    border-radius: 2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
  }

  .submit-btn:hover {
    transform: translateY(-52%);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  }

  .empty-cart-cls {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem 2rem;
    color: #6c757d;

    img {
      width: 60px;
      height: 45px;
      object-fit: contain;
    }

    .item-name {
      font-size: 1.2rem;
      font-weight: 600;
    }
  }
}

.blink_me {
  animation: blinker 1s linear infinite;
}

@keyframes blinker {
  50% {
    opacity: 0;
  }
}

.driver-tip-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: 6px 16px;
  border-radius: 2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;

  &:hover {
    transform: translateY(-5%);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  }
}

.wallet-details {
  background-color: #f8f8f8;
  border-radius: 1rem;
  box-shadow: 0px 2px 5px rgba($color: #000000, $alpha: 0.1);
  padding: 1rem;
}

.payment-methods {
  background-color: #fff;
  border-radius: 1rem;
  // box-shadow: 0px 2px 5px rgba($color: #000000, $alpha: 0.1);
  padding: 1rem;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: wrap;
  column-gap: 0.5rem;
  row-gap: 0.5rem;

  .payment-method-card {
    background: #f8f8f8;
    border-radius: 1rem;
    overflow: auto;
    box-shadow: 0px 2px 5px rgba($color: #000000, $alpha: 0.1);
    padding: 0.5rem;
    width: 100%;
    // max-width: 10rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    column-gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e7e7e7;

    &:hover {
      transform: translateY(-5%);
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    }

    &.active {
      border: 2px solid var(--primary);
    }

    img {
      width: 30px;
      height: 30px;
      object-fit: contain;
    }

    span {
      font-weight: 600;
      margin-left: 0.5rem;
    }
  }
}

.order-place-btn {
  width: 100%;
  background: var(--primary);
  color: white;
  border: 1px solid #fff;
  padding: 8px 14px;
  border-radius: 2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  .spinner-border {
    width: 1rem;
    height: 1rem;
    color: #fff;
  }

  // hover (only if not disabled)
  &:hover:not(:disabled):not(.disabled) {
    color: var(--primary);
    border: 1px solid var(--primary);
    background-color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);

    .spinner-border {
      color: var(--primary);
    }
  }

  // disabled (both class and attr)
  &:disabled,
  &.disabled {
    background: #878787;
    border: 1px solid #878787;
    cursor: not-allowed;
    color: #fff;
    box-shadow: none;
    transform: none;

    .spinner-border {
      color: #fff;
    }
  }
}

.card-add-btn {
  background: var(--primary);
  color: white;
  border: 1px solid #fff;
  padding: 8px 14px;
  border-radius: 2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    color: var(--primary);
    border: 1px solid var(--primary);
    background-color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  }
}

.card-modal {
  border-radius: 1rem;

  .card-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .card-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .card-modal-title {
      font-size: 1.5rem;
      margin: 0px;
    }
  }

  .card-modal-close {
    background-color: var(--primary);
    color: #fff;
    padding: 0.75rem;
    border: none;
    border-radius: 2rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;

    .spinner-border {
      width: 1rem;
      height: 1rem;
      color: #fff;
    }

    &:hover {
      color: var(--primary);
      border: 1px solid var(--primary);
      background-color: #fff;

      .spinner-border {
        color: var(--primary);
      }
    }
  }
}

.item-not-available-modal {
  border-radius: 1rem;

  .item-not-available-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .item-not-available-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      min-width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .item-not-available-title {
      font-size: 1.5rem;
    }
  }

  .item-not-available-modal-close {
    background-color: var(--primary);
    color: #fff;
    padding: 0.75rem;
    border: none;
    border-radius: 2rem;
    font-size: 1rem;
    font-weight: 500;

    &:hover {
      color: var(--primary);
      border: 1px solid var(--primary);
      background-color: #fff;
    }
  }
}

.multiple-price-offer-modal {
  border-radius: 1rem;

  .multiple-price-offer-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .multiple-price-offer-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      min-width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
    }

    .multiple-price-offer-modal-title {
      font-size: 1.5rem;
    }
  }

  .multiple-price-offer-modal-close {
    background-color: var(--primary);
    color: #fff;
    padding: 0.75rem;
    border: none;
    border-radius: 2rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;

    &:hover {
      color: var(--primary);
      border: 1px solid var(--primary);
      background-color: #fff;
    }
  }
}

.quantity-controls {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;

  button {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    border: 1px solid #ddd;
    color: var(--primary);
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;

    &:hover {
      background: #f5f5f5;
    }

    &.add-btn {
      color: #27ae60;
    }

    &.delete-btn {
      color: #ff0000;
    }
  }

  span {
    min-width: 20px;
    text-align: center;
    font-weight: 600;
  }
}
