.information2-container {
  min-height: 100vh;
  background: #f8f9fa;
  position: relative;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

// Navigation
.info-navigation {
  background: white;
  border-bottom: 1px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .nav-tabs-wrapper {
    display: flex;
    gap: 0;
    overflow-x: auto;
    padding: 0;

    .nav-tab {
      flex: 1;
      min-width: 120px;
      padding: 1rem 1.5rem;
      border: none;
      background: transparent;
      color: #6c757d;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      cursor: pointer;
      transition: all 0.3s ease;
      border-bottom: 3px solid transparent;

      &:hover {
        color: var(--primary, #007bff);
        background: rgba(0, 123, 255, 0.05);
      }

      &.active {
        color: var(--primary, #007bff);
        border-bottom-color: var(--primary, #007bff);
        background: rgba(0, 123, 255, 0.05);
      }

      i {
        font-size: 1.1rem;
      }
    }
  }
}

// Content Sections
.content-sections {
  padding: 2rem 0;

  .content-section {
    animation: fadeIn 0.5s ease-in-out;
  }

  .section-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 2rem;

    .section-header {
      background: var(--primary);
      color: white;
      padding: 1rem 2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;

      h2 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 600;
      }

      .section-icon {
        font-size: 2rem;
        opacity: 0.8;
      }
    }

    .section-content {
      padding: 2rem;
    }
  }
}

// About Section
.about-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #495057;
  margin-bottom: 2rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;

  .feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .feature-icon {
      width: 60px;
      height: 60px;
      background: var(--primary);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1.5rem;
    }

    .feature-content {
      h4 {
        margin: 0 0 0.25rem 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
      }

      p {
        margin: 0;
        color: #6c757d;
        font-size: 0.9rem;
      }
    }
  }
}

// Location Section
.location-info {
  .address-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 0.75rem 2rem;
    background: #f8f9fa;
    border-radius: 12px;
    margin-bottom: 2rem;

    .address-icon {
      width: 60px;
      height: 60px;
      background: var(--primary);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1.5rem;
    }

    .address-details {
      h4 {
        margin: 0 0 0.5rem 0;
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
      }

      p {
        margin: 0;
        color: #6c757d;
        line-height: 1.5;
      }
    }
  }

  .map-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .location-map {
      width: 100%;
      height: 300px;
      border: none;
    }
  }
}

// Hours Section
.hours-grid {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;

  .hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid var(--primary, #007bff);

    .day-name {
      font-weight: 600;
      color: #333;
    }

    .day-hours {
      color: #28a745;
      font-weight: 500;

      &.closed {
        color: #dc3545;
      }
    }
  }
}

.current-status {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px solid #e9ecef;

  .status-indicator {
    .fas {
      font-size: 0.8rem;
      color: #dc3545;
    }

    &.open .fas {
      color: #28a745;
    }
  }

  .status-text {
    font-weight: 600;
    color: #333;
  }
}
