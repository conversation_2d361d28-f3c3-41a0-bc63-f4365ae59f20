import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DOCUMENT, ViewportScroller } from '@angular/common';
import { Component, ElementRef, HostListener, Inject, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { Category } from 'src/app/core/models/category';
import { Menu } from 'src/app/core/models/menu';
import { CategoryService } from 'src/app/core/services/category.service';
import {
  NgbModal,
  ModalDismissReasons,
  NgbModalOptions,
  NgbActiveModal,
} from '@ng-bootstrap/ng-bootstrap';
import { MenuService } from 'src/app/core/services/menu.service';
import { Variant } from 'src/app/core/models/variant';
import { UserService } from 'src/app/core/services/user.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Order } from 'src/app/core/models/order';
import { Cart } from 'src/app/core/models/cart';
import { environment } from 'src/environments/environment';
import { CartService } from 'src/app/core/services/cart.service';
import { Meta, Title } from '@angular/platform-browser';
import { NotificationService } from 'src/app/core/services/notification.service';
import { User } from 'src/app/core/models/user';
import { NgForm } from '@angular/forms';
import { MessagingService } from 'src/app/core/services/messaging.service';
@Component({
  selector: 'app-menu',
  templateUrl: './menu.component.html',
  styleUrls: ['./menu.component.scss'],
})
export class MenuComponent implements OnInit, OnDestroy {
  @ViewChild('otpModal', { static: true }) otpModal: ElementRef;
  @ViewChild('allergyModal', { static: true }) allergyModal: ElementRef;
  @ViewChild('itemNotAvailableModal', { static: true }) itemNotAvailableModal: ElementRef;

  subs = new Subscription();
  isLoading = false;
  isModelLoading = false;
  error = null;
  originalCategories: Category[] = [];
  categories: Category[] = [];
  menus: Menu[] = [];
  carts: Cart[] = [];
  restaurant: Restaurant = new Restaurant();
  order: Order = new Order();
  user: User;
  categoryOptions = { nopaginate: 1, prefilled: 1 };
  expandableCategoryId: string;
  selectedMenu: Menu;
  selectedVariant: Variant;
  modalOptions: NgbModalOptions;
  expandableMainAddonId: string;
  userId: string;
  restaurant_id: string;
  selectedCategoryId: string;
  showCategory: boolean = false;
  mobile: boolean = false;
  isModelOtpLoading = false;
  Modelotperror = null;
  verifyOtp: string;
  phoneTab = false;
  isModelProfileLoading = true;
  ModelProfileerror = false;

  hasOrderTypeMismatch = false;
  hasDayMismatch = false;

  isRestaurantLoading = false;

  constructor(
    private router: Router,
    private categoryService: CategoryService,
    private menuService: MenuService,
    private route: ActivatedRoute,
    @Inject(DOCUMENT) private document: Document,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private restaurantService: RestaurantService,
    private cartService: CartService,
    private currencyPipe: CurrencyPipe,
    private metaTagService: Meta,
    private titleService: Title,
    private notificationService: NotificationService,
    private messagingService: MessagingService,
  ) { }

  ngOnInit(): void {
    if (window.screen.width <= 820) { // 768px portrait
      this.mobile = true;
    }
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.userId = user?.id
    this.order.order_type = this.cartService.getOrderType();
    this.modalOptions = {
      backdrop: 'static',
      backdropClass: 'customBackdrop',
    };
    this.fetchCategories();
    this.fetchRestaurant();
    if (this.userId) {
      this.fetchCarts();
      this.user = user
    }
  }

  @HostListener('window:scroll', ['$event']) onWindowScroll(): void {
    const scrollPosition = window.scrollY;
    const categoryElements = this.document.querySelectorAll('.category-section');
    for (let i = categoryElements.length - 1; i >= 0; i--) {
      const categoryElement = categoryElements[i] as HTMLElement;
      const categoryTop = categoryElement.offsetTop;
      if (scrollPosition >= categoryTop - 250) {
        this.selectedCategoryId = categoryElement.getAttribute('data-category-id')
        break;
      }
    }
  }

  fetchCategories() {
    this.isLoading = true;

    this.subs.add(
      this.categoryService
        .get({ nopaginate: 1, prefilled: 1, restaurant_id: this.restaurant_id, order_type: this.order.order_type })
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.originalCategories = res;
            this.categories = res;
            this.expandableCategoryId = this.categories[0].id;
            for (let cat of this.categories) {
              this.menus.concat(cat.menu);
            }
          },
          (err) => {
            this.categories = [];
          }
        )
    );
  }

  trackByFn(index, item) {
    return item.id;
    // or if you have no unique identifier:
    // return index;
  }

  fetchCarts() {
    this.subs.add(
      this.cartService
        .get({ restaurant_id: this.restaurant_id, nopaginate: "1" })
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.carts = res;
            this.cartService.carts = this.carts;
          },
          (err) => {
            this.carts = [];
          }
        )
    );
  }

  fetchRestaurant() {
    this.isRestaurantLoading = true;
    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => {
        this.isLoading = false;
        this.isRestaurantLoading = false;
      }))
      .subscribe(res => {
        this.restaurant = res;
        if (this.restaurant.meta_title) {
          this.titleService.setTitle(this.restaurant.meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.meta_description });
        } else {
          this.titleService.setTitle(this.restaurant.site_setting.meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.site_setting.meta_keywords });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.site_setting.meta_description });
        }
        if (this.order.order_type == 'delivery' && res.restaurant_delivery == 'No') {
          this.orderType('pickup');
        }
        if (this.order.order_type == 'pickup' && this.restaurant.restaurant_pickup == 'No') {
          this.orderType('delivery');
        }

        if (this.userId) {
          if ((((!this.user.phone_verify && this.restaurant.site_setting.signup_verify_type == 'phone') || !this.user.email_verify && this.restaurant.site_setting.signup_verify_type == 'mail') || (this.restaurant.site_setting.signup_verify_type == 'both' && (!this.user.phone_verify || !this.user.email_verify))) && this.restaurant.site_setting?.signup_verify == '1') {
            this.otpSend();
            this.modalService.open(this.otpModal, { size: 'md', backdropClass: 'customBackdrop', }); //backdrop: 'static', 
          }
        }

      }, err => this.error = err)
    );
  }

  fetchMenuItem(model, menuId, varaintId) {
    this.isModelLoading = true;

    this.subs.add(
      this.menuService
        .show(menuId)
        .pipe(finalize(() => (this.isModelLoading = false)))
        .subscribe(
          (res) => {
            this.selectedMenu = res;
            this.selectedVariant = this.selectedMenu.variants.find(
              (varint) => varint.id == varaintId
            );
            this.openModal(model);
          },
          (err) => { }
        )
    );
  }

  clickOnAccordion(categoryId: any) {
    this.selectedCategoryId = categoryId;
    this.showCategory = false
    if (categoryId == this.expandableCategoryId) {
      categoryId = null;
    }
    this.expandableCategoryId = categoryId;
    if (categoryId != null) {
      this.scroll('category_' + categoryId);
    }
  }

  scroll(id) {
    // this.viewPortScroller.scrollToAnchor(id);
    if (window.innerWidth <= 1199) {
      var top = document.getElementById(id).offsetTop - 50;
    } else {
      var top = document.getElementById(id).offsetTop - 130;
    }
    window.scrollTo(0, top);
  }

  addItemToCart(model, menu, menuId, variantId) {
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    this.selectedMenu = menu;
    if (this.selectedMenu.menu_addon == 'No' && this.selectedMenu.price_option == 'single') {
      let cart = new Cart();
      cart.menu_id = this.selectedMenu.id;
      cart.restaurant_id = this.restaurant_id;
      cart.menu_name = this.selectedMenu.menu_name;
      if (this.selectedMenu.product_percentage > 0) {
        cart.menu_price = this.selectedMenu.variants[0]?.orginal_price - (this.selectedMenu.variants[0]?.orginal_price * this.selectedMenu.product_percentage / 100);
        cart.total_price = this.selectedMenu.variants[0]?.orginal_price - (this.selectedMenu.variants[0]?.orginal_price * this.selectedMenu.product_percentage / 100);
      } else {
        cart.menu_price = this.selectedMenu.variants[0]?.orginal_price;
        cart.total_price = this.selectedMenu.variants[0]?.orginal_price;
      }
      cart.quantity = 1;
      cart.customer_id = this.userId;
      this.uploadCart(cart);
    } else if (this.selectedMenu.menu_addon == 'No' && this.selectedMenu.price_option == 'multiple') {
      let cart = new Cart();
      const selectedVariant = this.selectedMenu.variants.find(
        (varint) => varint.id == variantId
      );
      cart.menu_id = this.selectedMenu.id;
      cart.restaurant_id = this.restaurant_id;
      cart.menu_name = this.selectedMenu.menu_name;
      if (this.selectedMenu.product_percentage > 0) {
        cart.menu_price = selectedVariant?.orginal_price - (selectedVariant?.orginal_price * this.selectedMenu.product_percentage / 100);
        cart.total_price = selectedVariant?.orginal_price - (selectedVariant?.orginal_price * this.selectedMenu.product_percentage / 100);
      } else {
        cart.menu_price = selectedVariant?.orginal_price;
        cart.total_price = selectedVariant?.orginal_price;
      }
      cart.subaddons_name = selectedVariant?.sub_name;
      cart.quantity = 1;
      cart.customer_id = this.userId;
      this.uploadCart(cart);
    }
    else {
      this.fetchMenuItem(model, menuId, variantId);
    }
  }

  uploadCart(cart: Cart) {
    this.subs.add(
      this.cartService.create(cart).
        pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.carts = res;
            this.cartService.carts = this.carts;
          },
          (err) => {
            this.carts = [];
          }
        )
    )
  }

  updateToCart(cart: Cart, event: string) {
    if (event == 'add') {
      cart.quantity = cart.quantity + 1;
      cart.total_price = cart.menu_price * cart.quantity;
      this.subs.add(
        this.cartService.update(cart).
          pipe(finalize(() => { }))
          .subscribe(
            (res) => {
              this.carts = res;
              this.cartService.carts = this.carts;
            },
            (err) => {
              this.carts = [];
            }
          )
      )
    }

    if (event == 'remove') {
      var quantity = cart.quantity - 1;
      cart.total_price = cart.menu_price * cart.quantity;
      if (quantity > 0) {
        cart.quantity = quantity;
        this.subs.add(
          this.cartService.update(cart).
            pipe(finalize(() => { }))
            .subscribe(
              (res) => {
                this.carts = res;
                this.cartService.carts = this.carts;
              },
              (err) => {
                this.carts = [];
              }
            )
        )
      } else {
        this.subs.add(
          this.cartService.delete(cart.id).
            pipe(finalize(() => { }))
            .subscribe(
              (res) => {
                this.carts = res;
                this.cartService.carts = this.carts;
              },
              (err) => {
                this.fetchCarts();
              }
            )
        )
      }
    }

    if (event == 'delete') {
      this.subs.add(
        this.cartService.delete(cart.id).
          pipe(finalize(() => { }))
          .subscribe(
            (res) => {
              this.carts = res;
            },
            (err) => {
              this.fetchCarts();
            }
          )
      )
    }
  }

  clickOnMainAddon(mainAddonId) {
    this.expandableMainAddonId = mainAddonId;

  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(this.selectedVariant);
        let selectedSubAddons = "";
        for (let mainAddon of this.selectedVariant.main_addons) {

          for (let subAddon of mainAddon.sub_addons) {
            if (mainAddon.selectedSubAddonId == subAddon.id) {
              console.log(subAddon.subaddons_name + " is selected")
            }
            if (subAddon.selected) {
              console.log(subAddon.subaddons_name + " is selected")
            }
          }
        }
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  validate(menuModal) {
    if (this.selectedVariant.main_addons) {
      for (let mainAddon of this.selectedVariant.main_addons) {
        mainAddon.max_error = false;
        mainAddon.min_error = false;
        var subAddonCount: number = 0;
        if (mainAddon.mainaddons_count == 1 && mainAddon.selectedSubAddonId != null) {
          subAddonCount = subAddonCount + 1;
        } else {
          for (let subAddon of mainAddon.sub_addons) {
            if (subAddon.selected) {
              subAddonCount = subAddonCount + 1;
            }
          }
        }
        if (subAddonCount < mainAddon.mainaddons_mini_count) {
          mainAddon.min_error = true;
          this.expandableMainAddonId = mainAddon.id;
          return;
        }
        if (subAddonCount > mainAddon.mainaddons_count) {
          mainAddon.max_error = true;
          this.expandableMainAddonId = mainAddon.id;
          return;
        }
      }
    }

    // create cart menu addon string 
    let selectedSubAddonstring = "";
    var selectedSubAddonPrice = 0;
    var mainAddonCount: number = 0;
    if (this.selectedVariant.main_addons) {
      for (let mainAddon of this.selectedVariant.main_addons) {
        var subAddonCount: number = 0;
        if (mainAddonCount <= 0) {
          if (this.selectedVariant.sub_name && this.selectedMenu.variants.length > 1) {
            selectedSubAddonstring += this.selectedVariant.sub_name + ',';
          }
          if (this.selectedMenu.product_percentage > 0) {
            selectedSubAddonPrice = selectedSubAddonPrice + (this.selectedVariant.orginal_price - (this.selectedVariant.orginal_price * this.selectedMenu.product_percentage / 100));
          } else {
            selectedSubAddonPrice = selectedSubAddonPrice + this.selectedVariant.orginal_price;
          }
        }
        for (let subAddon of mainAddon.sub_addons) {
          if (subAddonCount == 0 && (subAddon.selected || mainAddon.selectedSubAddonId == subAddon.id)) {
            selectedSubAddonstring += mainAddon.mainaddons_name;
          }
          if (subAddon.selected || mainAddon.selectedSubAddonId == subAddon.id) {
            selectedSubAddonstring += ' (' + subAddon.subaddons_name + ') +';
            // '(' + this.currencyPipe.transform(subAddon.subaddons_price, 'GBP', 'symbol', '1.2-2') + ')' +
            selectedSubAddonPrice = selectedSubAddonPrice + subAddon.subaddons_price;
            subAddonCount = subAddonCount + 1;
          }
        }
        mainAddonCount = mainAddonCount + 1;
        var lastChar = selectedSubAddonstring.slice(-1);
        if (lastChar == '+') {
          selectedSubAddonstring = selectedSubAddonstring.slice(0, -1); // trim last character
        }
        if ((mainAddonCount) != this.selectedVariant.main_addons.length) {
          for (let subAddon of mainAddon.sub_addons) {
            if (subAddon.selected || mainAddon.selectedSubAddonId == subAddon.id) {
              selectedSubAddonstring = selectedSubAddonstring + ",";
            }
          }
        }
      }
    } else {
      if (this.selectedMenu.product_percentage > 0) {
        selectedSubAddonPrice = this.selectedMenu.variants[0]?.orginal_price - (this.selectedMenu.variants[0]?.orginal_price * this.selectedMenu.product_percentage / 100);
      } else {
        selectedSubAddonPrice = this.selectedMenu.variants[0]?.orginal_price;
      }
    }

    let cart = new Cart();
    cart.menu_id = this.selectedMenu.id;
    cart.restaurant_id = this.restaurant_id;
    cart.menu_name = this.selectedMenu.menu_name;
    cart.subaddons_name = selectedSubAddonstring;
    cart.menu_price = selectedSubAddonPrice;
    cart.total_price = selectedSubAddonPrice;
    cart.quantity = 1;
    cart.customer_id = this.userId;
    this.uploadCart(cart);
    this.modalService.dismissAll();
  }

  getGrandTotal() {
    let grand_total = 0;
    this.carts.forEach(item => {
      grand_total = grand_total + item.total_price;
    });
    return grand_total;
  }

  clickcategory() {
    if (this.showCategory) {
      this.showCategory = false;
    } else {
      this.showCategory = true;
    }
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event > 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      var val1 = 'Free'
    }
    return val1
  }

  handleItemNotAvailable(type: string) {
    if (type === 'yes') {
      const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
      const itemsToRemove = this.carts.filter(cart => {
        const cartOrderType = cart.menu.product_order_type?.toLowerCase();
        const cartDaysRaw = cart.menu.product_day?.toLowerCase() || 'all';
        const cartDays = cartDaysRaw.split(',').map(day => day.trim());
        const orderTypeMismatch = cartOrderType !== 'both' && cartOrderType !== this.order.order_type.toLowerCase();
        const dayMismatch = cartDaysRaw !== 'all' && !cartDays.includes(selectedDay);
        return orderTypeMismatch || dayMismatch;
      });

      const deleteCalls = itemsToRemove.map(item =>
        this.subs.add(
          this.cartService.delete(item.id).
            pipe(finalize(() => { }))
            .subscribe(
              (res) => {
                this.carts = res;
                this.cartService.carts = this.carts;
              },
              (err) => {
                this.fetchCarts();
              }
            )
        )
      );
      this.router.navigate(['/menu']);
    } else {
      const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
      const itemsToRemove = this.carts.filter(cart => {
        const cartOrderType = cart.menu.product_order_type?.toLowerCase();
        const cartDaysRaw = cart.menu.product_day?.toLowerCase() || 'all';
        const cartDays = cartDaysRaw.split(',').map(day => day.trim());
        const orderTypeMismatch = cartOrderType !== 'both' && cartOrderType !== this.order.order_type.toLowerCase();
        const dayMismatch = cartDaysRaw !== 'all' && !cartDays.includes(selectedDay);
        return orderTypeMismatch || dayMismatch;
      });

      if (itemsToRemove.length > 0) {
        if (this.order.order_type == 'delivery') {
          this.order.order_type = 'pickup';
          this.cartService.saveOrderType('pickup');
        } else if (this.order.order_type == 'pickup') {
          this.order.order_type = 'delivery';
          this.cartService.saveOrderType('delivery');
        } else {
          this.order.order_type = 'delivery';
          this.cartService.saveOrderType('delivery');
        }
      }
    }

    this.modalService.dismissAll();
  }

  orderType(order_type: string) {
    let oldOrderType = this.order.order_type;
    this.order.order_type = order_type;
    this.cartService.saveOrderType(order_type);
    // Reset mismatch flags
    this.hasOrderTypeMismatch = false;
    this.hasDayMismatch = false;

    if (this.cartService.carts.length > 0) {
      const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
      this.carts.forEach(item => {
        const menu = item.menu;
        const itemOrderType = menu.product_order_type?.toLowerCase();
        const itemDaysRaw = menu.product_day?.toLowerCase() || 'all';
        const itemDays = itemDaysRaw.split(',').map(day => day.trim());
        if (itemOrderType !== 'both' && itemOrderType !== order_type.toLowerCase()) {
          this.hasOrderTypeMismatch = true;
        }
        if (itemDaysRaw !== 'all' && !itemDays.includes(selectedDay)) {
          this.hasDayMismatch = true;
        }
      });
      if (this.hasOrderTypeMismatch || this.hasDayMismatch) {
        this.modalService.dismissAll();
        this.modalService.open(this.itemNotAvailableModal, { size: 'lg' });
        return order_type;
      }

      // ✅ Save new order type if no mismatch
      if ((oldOrderType !== order_type) && !this.hasOrderTypeMismatch && !this.hasDayMismatch) {
        this.fetchCategories();
      }
    } else {
      if ((oldOrderType !== order_type) && !this.hasOrderTypeMismatch && !this.hasDayMismatch) {
        this.fetchCategories();
      }
    }

    return order_type;
  }

  search(search: string) {
    if (!search) {
      this.categories = this.originalCategories
    } else {
      let tempAllItems = Object.assign([], this.originalCategories)
      let count = 0;
      let filteredArray = []
      tempAllItems.forEach(category => {
        let tempCategory = Object.assign({}, category);
        let searchedMenu = []
        tempCategory.menu.forEach(menu => {
          if (menu?.menu_name?.toLocaleLowerCase().includes(search.toLocaleLowerCase())) {
            searchedMenu.push(Object.assign({}, menu));
          }
        });
        if (searchedMenu.length > 0) {
          tempCategory.menu = searchedMenu;
          if (count <= 0) {
            this.expandableCategoryId = tempCategory?.id;
            count++;
          }
          filteredArray.push(tempCategory);
        }
      });
      this.categories = Object.assign([], filteredArray);
      return;

    }
  }

  otpSend() {
    this.phoneTab = true;
    if (this.restaurant.site_setting.signup_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.restaurant.site_setting.signup_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.restaurant.site_setting.signup_verify_type;
      }
    }

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.verifyOtp = '';
            this.phoneTab = false;
            // this.modalService.dismissAll();
            this.messagingService.success("otp send successfully !!")
          },
          (err) => {
            this.messagingService.error(err)
          }
        )
    )
  }

  resendOtp() {
    if (this.restaurant.site_setting.signup_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.restaurant.site_setting.signup_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.restaurant.site_setting.signup_verify_type;
      }
    }

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.messagingService.success("otp sent successfully !!")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  validateOtp() {
    this.isModelOtpLoading = true;
    this.Modelotperror = false;

    if (this.restaurant.site_setting.signup_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.restaurant.site_setting.signup_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.restaurant.site_setting.signup_verify_type;
      }
    }

    if (!this.verifyOtp && (this.user.verify_type == 'both' || this.user.verify_type == 'phone')) {
      this.messagingService.error("Please enter phone verification code !!")
      this.isModelOtpLoading = false;
    } else if (!this.user.email_otp && (this.user.verify_type == 'both' || this.user.verify_type == 'email')) {
      this.messagingService.error("Please enter email verification code !!")
      this.isModelOtpLoading = false;
    } else {
      this.user.otp = this.verifyOtp
      this.subs.add(
        this.userService.varifyBothOtp(this.user).
          pipe(finalize(() => this.isModelOtpLoading = false))
          .subscribe(
            (res) => {
              this.verifyOtp = '';
              this.phoneTab = false;
              this.fetchMe();
              this.modalService.dismissAll();
              this.messagingService.success("otp verify successfully !!")
            },
            (err) => {
              this.messagingService.error(err)
            }
          )
      )
    }
  }

  fetchMe() {
    this.subs.add(this.userService.me()
      .pipe(finalize(() => this.isModelOtpLoading = false))
      .subscribe(res => {
        this.user = res;
        this.userService.saveUser(res);
      }, err => this.Modelotperror = err)
    );
  }

  profileUpdate(model) {
    this.modalService.dismissAll();
    this.openModal(model);
  }

  allergyShow() {
    this.modalService.dismissAll();
    this.modalService.open(this.allergyModal, { size: 'md', });
  }

  updateUser(form: NgForm) {
    this.isModelProfileLoading = true;
    this.ModelProfileerror = null;

    this.user.password = '';
    this.user.confirmPassword = '';
    this.user.current_password = '';

    if (this.userService.user.phone_number != this.user.phone_number) {
      this.user.phone_verify = false;

      this.userService
        .disabledPhoneVerify(this.user)
        .pipe(finalize(() => (this.isModelProfileLoading = false)))
        .subscribe(
          (res) => { },
          (err) => { }
        );
    }

    this.userService
      .update(this.user)
      .pipe(finalize(() => (this.isModelProfileLoading = false)))
      .subscribe(
        (res) => {
          this.fetchMe();
          this.messagingService.success("profile updated successfully !!")
          if ((((!this.user.phone_verify && this.restaurant.site_setting.signup_verify_type == 'phone') || !this.user.email_verify && this.restaurant.site_setting.signup_verify_type == 'mail') || (this.restaurant.site_setting.signup_verify_type == 'both' && (!this.user.phone_verify || !this.user.email_verify))) && this.restaurant.site_setting?.signup_verify == '1') {
            this.otpSend();
            this.modalService.dismissAll();
            this.modalService.open(this.otpModal, { size: 'md', backdropClass: 'customBackdrop', });
          }
        },
        (err) => {
          this.ModelProfileerror = err;
        }
      );
  }

  keyPress(event: any) {
    const pattern = /[0-9]/;

    let inputChar = String.fromCharCode(event.charCode);
    if (event.keyCode != 8 && !pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
