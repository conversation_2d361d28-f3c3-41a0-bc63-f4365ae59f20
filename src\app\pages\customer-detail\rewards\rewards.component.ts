import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { Reward } from 'src/app/core/models/reward';
import { RewardService } from 'src/app/core/services/reward.service';

@Component({
  selector: 'app-rewards',
  templateUrl: './rewards.component.html',
  styleUrls: ['./rewards.component.scss'],
})
export class RewardsComponent implements OnInit, OnDestroy {

  subs = new Subscription();
  isLoading = false;
  error = null;

  isModelLoading = false;
  Modelerror = null;

  user: User;
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;

  restaurant: Restaurant = new Restaurant();
  rewards: Reward[] = [];
  reward: Reward = new Reward();
  totalRewards = 0;
  last_page = 0;
  previousPage: any;
  rewardValidity: number;

  options = { query: null, page: 1, per_page: 10, customer_id: null };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private currencyPipe: CurrencyPipe,
    private restaurantService: RestaurantService,
    private rewardService: RewardService,
  ) { }

  ngOnInit(): void {
    this.subs.add(this.route.queryParamMap.subscribe(map => {
      this.options.query = map.get('query');
      if (map.has('page')) this.options.page = +map.get('page');
      if (map.has('per_page')) this.options.per_page = +map.get('per_page');
      this.restaurant_id = environment.googleFirebase;
      let user = JSON.parse(this.userService.getUser());
      this.user = user;
      this.userId = user?.id;
      this.options.customer_id = user?.id;
      if (!this.userId) {
        this.router.navigateByUrl('/auth');
      }
      this.modalOptions = {
        backdrop: 'static',
        backdropClass: 'customBackdrop',
      };
      this.fetchRestaurant();
      this.fetchRewards();
    }));
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
      }, err => this.error = err)
    );
  }

  fetchRewards() {
    this.isLoading = true;

    this.subs.add(this.rewardService.get(this.options)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.rewards = res.data;
        this.totalRewards = res.total;
        this.last_page = res.last_page;
        this.rewardValidity = res.data[0].reward_validity;
      }, err => { this.rewards = []; this.totalRewards = 0; this.Modelerror = err })
    );
  }

  loadPage(page: number) {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.fetchRewards();
    }
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
