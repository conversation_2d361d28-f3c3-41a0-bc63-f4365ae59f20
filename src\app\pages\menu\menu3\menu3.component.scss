::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}

.delivery-pick-up-list {
  display: flex;
  padding: 5px;
  background-color: #f4f3f3;
  border-radius: 50px;
}

.delivery-pick-up-list li {
  width: 100%;
  padding: 14px 15px 9px 15px;
  text-align: center;
  border-radius: 50px;
}

.delivery-pick-up-list li p {
  font-family: "Fredoka";
  font-size: 20px;
  color: #000000;
  font-weight: 500;
  line-height: 14px;
  font-weight: 500;
  margin-bottom: 0;
}

.delivery-pick-up-list li span {
  font-family: "Fredoka";
  font-size: 12px;
  font-weight: 500;
  line-height: normal;
  color: #000000;
}

.delivery-pick-up-list li.active {
  background-color: var(--primary);
}

.delivery-pick-up-list li.active p {
  color: #fff;
}

.delivery-pick-up-list li.active span {
  color: #fff;
}

.delivery-pick-up-list li.disabled {
  background-color: #c0c0c0;
}

.delivery-pick-up-list li.disabled span,
.delivery-pick-up-list li.disabled p {
  color: #fff;
}

.order-free-delivery-list {
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.order-free-delivery-list li {
  font-family: "Visby CF";
  font-size: 12px;
  color: #000000;
  font-weight: 700;
  margin: 0 16px;
}

.order-free-delivery-list li img {
  margin-right: 7px;
}

.delivery-pick-up-list li {
  cursor: pointer;
}

.menu-category-list {
  margin-top: 45px;
  padding-bottom: 30px;
  min-height: 165px;
}

.menu-category-list .menu-section {
  padding: 15px 0 8px 0;
  background-color: #fff;
  border-top: 2px solid #f4f3f3;
}

.menu-section .search-for-restaurant input.form-control {
  font-size: 16px;
  width: 355px;
  height: 36px;
  padding-right: 40px;
}

.menu-section .search-for-restaurant .search-close {
  position: absolute;
  right: 15px;
  right: 16px;
  font-size: 18px !important;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

.menu-top-line {
  padding-top: 20px;
  border-top: 2px solid #f4f3f3;
}

.menu-section h5.menu-title {
  font-size: 24px;
  font-family: "Visby CF";
  font-weight: 800;
}

.menu-category-list.fixed .menu-section {
  position: fixed;
  top: 0px;
  left: 0;
  right: 0;
  margin: 0;
  width: 100%;
  padding: 15px 20px 8px 20px;
  z-index: 123;
  background-color: #fff;
  box-shadow: 0 2px 3px 0 #e1e1e1;
}

.menu-category-list.fixed .category-inner-section {
  max-width: 1718px;
  margin: auto;
}

.menu-category-list.fixed .menu-section .row,
.menu-category-list.fixed .menu-section .menu-inner-sec {
  max-width: 1302px;
}

.menu-category-list.fixed .menu-section .menu-top-line {
  display: none;
}

.menu-category-list.fixed .menu-inner-sec .menu-list {
  padding-right: 84px;
}

.menu-inner-sec {
  display: flex;
  justify-content: space-between;
}

.menu-list-arrow {
  padding-top: 3px;
  position: relative;
}

.menu-list-arrow button img {
  width: 26px;
}

.view-menu-btn {
  cursor: pointer;
  position: relative;
  z-index: 99;
  padding: 0;
  border: 0;
  background-color: transparent;
}

.view-menu-dropdown-bg {
  position: absolute;
  z-index: 99;
  left: 55px;
  top: -10px;
}

.view-menu-dropdown-bg.show {
  display: block;
}

.view-menu-dropdown-bg::after {
  position: absolute;
  top: 10px;
  left: -24px;
  content: "";
  width: 34px;
  height: 32px;
  background: url(/assets/images/left-shape.svg) no-repeat top center;
  z-index: 1;
}

.view-menu-dropdown {
  width: 205px;
  background-color: #fff;
  box-shadow: 0px 0px 4px 0px #00000040;
  border-radius: 15px;
  position: relative;
  z-index: 2;
}

.view-menu-title {
  padding: 10px 20px 20px 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.view-menu-title p {
  font-family: "Visby CF";
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 0;
}

.view-menu-title button.close {
  font-family: "Fredoka";
  font-size: 12px;
  color: #000000;
  font-weight: 500;
  line-height: 22px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  text-align: center;
  background-color: #d9d9d9;
  border: 0;
  padding: 0;
}

.view-menu-dropdown .view-menu-list {
  max-height: 420px;
  overflow: hidden;
  overflow-y: auto;
}

.view-menu-dropdown .view-menu-list::-webkit-scrollbar {
  width: 5px;
}

.view-menu-dropdown .view-menu-list::-webkit-scrollbar-track {
  background: #ffe6e7;
  border-radius: 10px;
}

.view-menu-dropdown .view-menu-list::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 10px;
}

.view-menu-dropdown .view-menu-list::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

.view-menu-list ul {
  margin-bottom: 0;
}

.view-menu-list ul li {
  padding-bottom: 16px;
}

.view-menu-list ul li button {
  font-family: "Visby CF";
  font-size: 15px;
  color: #000000;
  cursor: pointer;
  text-decoration: none;
  font-weight: 700;
  padding: 7px 20px 7px 25px;
  display: flex;
  justify-content: space-between;
  width: 100%;
  position: relative;
  background-color: transparent;
  border: 0;
}

.view-menu-list ul li button::before {
  content: "";
  position: absolute;
  width: 7px;
  height: 100%;
  left: 0;
  top: 0;
  bottom: 0;
  background-color: var(--primary);
  border-radius: 0 10px 10px 0;
  display: none;
}

.view-menu-list ul li button:hover::before,
.view-menu-list ul li.active button::before {
  display: block;
}

.view-menu-list ul li.active button {
  color: var(--primary);
}

.menu-inner-sec .menu-list {
  width: 100%;
  padding: 0 50px;
  margin-bottom: 0;
}

::ng-deep.menu-inner-sec .menu-list button.slick-arrow {
  top: 18px;
  line-height: 20px;
  background-color: transparent;
  box-shadow: none;
  margin: 0;
}

::ng-deep.menu-inner-sec .menu-list button.slick-arrow.slick-prev {
  left: -30px;
}

::ng-deep.menu-inner-sec .menu-list button.slick-arrow.slick-next {
  right: -40px;
}

::ng-deep.menu-inner-sec .menu-list button.slick-arrow.slick-disabled {
  display: none !important;
}

::ng-deep.menu-inner-sec .menu-list button.slick-arrow::before {
  opacity: 1;
  color: #000;
}

.menu-inner-sec .menu-list li {
  text-align: center;
  padding: 0 30px;
}

.menu-inner-sec .menu-list li button {
  font-size: 18px;
  font-weight: 700;
  color: #000000;
  cursor: pointer;
  white-space: nowrap;
  line-height: 25px;
  text-align: center;
  display: inline-block;
  text-decoration: none;
  padding-bottom: 20px;
  position: relative;
  background-color: transparent;
  border: 0;
}

.menu-inner-sec .menu-list li button::before {
  width: 100%;
  display: inline-block;
  position: absolute;
  content: "";
  height: 7px;
  bottom: 0;
  border-radius: 8px 8px 0 0;
  background-color: var(--primary);
  display: none;
}

.menu-inner-sec .menu-list li.active button {
  color: var(--primary);
}

.menu-inner-sec .menu-list li button:hover::before,
.menu-inner-sec .menu-list li.active button::before {
  display: block;
}

.menu-inner-sec {
  padding-top: 10px;
  border-bottom: 2px solid #f4f3f3;
}

.menu-inner-sec .left-arrow {
  padding-top: 3px;
  padding-left: 35px;
  cursor: pointer;
  display: none;
}

.menu-inner-sec .left-arrow.slick-disabled {
  display: none;
}

.menu-inner-sec .left-arrow button {
  font-size: 18px;
  color: #000000;
  padding: 0;
  background-color: transparent;
  border: 0;
}

.menu-inner-sec .right-arrow {
  padding-top: 3px;
  padding-left: 15px;
  cursor: pointer;
  display: none;
}

.menu-inner-sec .right-arrow.slick-disabled {
  display: none;
}

.menu-inner-sec .right-arrow button {
  font-size: 18px;
  color: #000000;
  padding: 0;
  background-color: transparent;
  border: 0;
}

.customer-favourites-section .main-heading {
  margin-bottom: 32px;
}

.customer-favourites-section .main-heading h6 {
  font-size: 30px;
  font-family: "Visby CF";
  font-weight: 800;
}

.favourite-box {
  margin-bottom: 30px;
}

.favourite-box .favourite-image {
  position: relative;
}

.favourite-box .favourite-image img {
  width: 100%;
  height: 193px;
  object-fit: cover;
  border-radius: 10px;
}

.horizontal-favourite-box .horizontal-image-box button.add-option,
.favourite-box .favourite-image button.add-option {
  color: var(--primary);
  border: 1px solid var(--primary);
  background-color: #fff;
  font-size: 15px;
  font-weight: 800;
  display: inline-block;
  text-decoration: none;
  line-height: 28px;
  cursor: pointer;
  width: 60px;
  height: 30px;
  line-height: 28px;
  border-radius: 25px;
  text-align: center;
  position: absolute;
  right: 12px;
  bottom: 10px;
  box-shadow: 0px 0px 7px 2px #00000026;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;

  &:hover {
    color: #fff;
    border: 1px solid #fff;
    background-color: var(--primary);
  }
}

.favourite-box .favourite-content {
  padding-top: 8px;
}

.horizontal-favourite-box .horizontal-content-box h6,
.favourite-box .favourite-content h6 {
  font-family: "Visby CF";
  font-size: 25px;
  font-weight: 800;
  line-height: 30px;
  margin-bottom: 10px;
}

.favourite-box .favourite-content h6 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.horizontal-favourite-box .horizontal-content-box span.price,
.favourite-box .favourite-content span.price {
  font-family: "Visby CF";
  font-size: 20px;
  color: #000;
  font-weight: 600;
  padding-bottom: 5px;
  display: flex;
  align-items: center;
}

.horizontal-favourite-box .horizontal-content-box p,
.favourite-box .favourite-content p {
  font-family: "Visby CF";
  font-size: 15px;
  color: #8b8f8f;
  line-height: 24px;
  margin-bottom: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.horizontal-favourite-box {
  position: relative;
  background-color: #fff;
  border-radius: 10px;
  display: flex;
  box-shadow: 0px 0px 4px 4px #0000000d;
  margin-bottom: 35px;
}

.horizontal-favourite-box .horizontal-content-box {
  width: calc(100% - 150px);
  padding: 24px 30px;
}

.horizontal-favourite-box .horizontal-content-box h6 {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  margin-bottom: 6px;
}

.horizontal-favourite-box .horizontal-image-box {
  width: 150px;
  min-width: 150px;
  position: relative;
}

.horizontal-favourite-box .horizontal-image-box img {
  width: 100%;
  height: 100%;
  border-radius: 0 10px 10px 0;
  object-fit: cover;
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
}

.horizontal-favourite-box .horizontal-image-box a.add-option {
  right: 28px;
  bottom: 17px;
}

/*---------------------------------------------
         Product-Multiple-Options-Popup
---------------------------------------------*/
.product-header {
  padding: 15px 25px;
}

.product-close {
  position: initial !important;
  background: none !important;

  padding: 0;
  margin: 0;
  width: 30px;
  height: 30px;
  opacity: 1;
  border-radius: 50%;
  text-align: center;
  background-color: #f4f3f3 !important;
  background-image: none;
  outline: none;
}

.product-body {
  padding: 20px 25px;
}

.product-body h6 {
  text-align: center;
  margin-bottom: 12px;
}

.product-body p {
  font-size: 16px;
  text-align: center;
}

.popup-product-box {
  padding-bottom: 15px;
}

.product-body .product-image {
  text-align: center;
}

.product-body .product-image img {
  width: 90px;
  border-radius: 5px;
}

.product-body .choose-options h6 {
  font-size: 22px;
  margin-bottom: 0;
  text-align: left;
}

.required-select {
  display: flex;
  align-items: center;
}

.required-select .required {
  font-size: 14px;
  color: #ff0000;
  font-weight: 600;
}

.required-select .required i {
  margin-right: 5px;
}

.required-select .fa-circle {
  color: #8f8f8a;
  font-size: 3px;
  margin: 0 6px;
}

.required-select span {
  color: #8f8f8a;
  font-size: 14px;
  font-weight: 700;
  font-family: "Visby CF";
}

.choose-options .choose-list {
  margin-bottom: 0;
}

.choose-options .choose-list li .form-check {
  cursor: pointer;
  position: relative;
  padding: 0px;
  margin-bottom: 0;
  display: flex;
  border-bottom: 2px solid #f8f7f7;
}

.choose-options .choose-list li .form-check label {
  width: 100%;
  padding: 0 45px;
}

.choose-options .choose-list li .form-check .burger-name {
  font-size: 16px;
  color: #202020;
  font-weight: bold;
}

.choose-options .choose-list li .form-check input.form-check-input {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  left: 0;
  margin: 0;
}

.choose-options .choose-list li .form-check .form-check-label {
  padding: 10px 0;
}

.extra-addon-list li {
  font-size: 13px;
  color: #202020;
  display: inline-block;
  font-weight: 500;
  font-family: "Visby CF";
}

.extra-addon-list li .fa-circle {
  font-size: 5px;
  margin: 0 5px 0 3px;
}

.choose-options .choose-list li .form-check .price {
  font-family: "Visby CF";
  font-size: 15px;
  color: #8b8f8f;
  display: block;
  padding-bottom: 5px;
}

.choose-options .choose-list li .form-check .edit-section span {
  font-size: 14px;
  color: #000;
  font-weight: 600;
  padding: 4px 15px 5px 15px;
  background-color: #f9f9f9;
  border: 1px solid #f4f3f3;
  border-radius: 20px;
}

.choose-options .choose-list li .form-check .right-arrow {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: 0;
}

.choose-options .choose-list li .form-check .right-arrow i {
  font-size: 15px;
  color: #202020;
}

.product-footer {
  padding: 0 25px 25px 25px !important;
}

.product-footer button.btn.btn-primary {
  width: 100%;
}

/*----------Addon-Popup----------*/
#addonpopup .modal-header {
  padding: 1rem;
}

#addonpopup .modal-header button.btn-close {
  top: 24px;
  left: 22px;
  outline: none;
  // padding: 1px 5px;
  padding: 5px;
  min-width: 30px;
  min-height: 30px;
}

#addonpopup .modal-header h6 {
  font-size: 24px;
  line-height: normal;
  margin-bottom: 0;
}

#addonpopup .modal-body {
  padding: 20px 25px 25px 25px;
}

#addonpopup .addon-list h6 {
  font-size: 24px;
  margin-bottom: 0;
}

#addonpopup .addon-list ul {
  padding: 10px 0 30px 0;
  margin-bottom: 0;
}

#addonpopup .addon-list ul li {
  border-bottom: 2px solid #f8f7f7;
}

#addonpopup .addon-list ul li .form-check {
  position: relative;
  display: flex;
  padding-left: 0px;
  margin-bottom: 0;
}

#addonpopup .form-check input.form-check-input {
  margin-top: 14px;
}

#addonpopup .addon-list ul li .form-check .form-check-label {
  color: #202020;
  font-size: 16px;
  font-weight: 600;
  width: 100%;
  padding: 11px 0;
}

#addonpopup button.btn {
  width: 100%;
  font-size: 18px;
  padding: 9px 15px;
}

.form-check input.form-check-input {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 2px solid #202020;
  margin-left: 0;
  margin-right: 10px;
}

.form-check input.form-check-input.radio-button {
  border-radius: 50%;
}

.favourite-icon {
  position: absolute;
  top: 32px;
  right: 45px;
  z-index: 2;
}

.favourite-icon button {
  padding: 0;
  border: 0;
  border-radius: 0;
  background-color: transparent;
}

.favourite-icon button img {
  width: 60px;
}

.make-reservation-btn {
  width: 220px;
  height: 50px;
  // line-height: 30px !important;
  color: #000 !important;
  font-family: "Visby CF";
  font-size: 18px;
  font-weight: 700;
  text-align: center;
  position: absolute;
  right: 55px;
  bottom: 32px;
  border-color: #fff !important;
  background-color: #ffffff !important;
  box-shadow: 0px 4px 8px 3px #00000040;
}

.make-reservation-btn:hover {
  color: var(--primary) !important;
  border-color: var(--primary) !important;
  background-color: #ffffff !important;
}

.see-all-review {
  text-align: right;
}

.see-all-review a {
  font-family: "Visby CF";
  font-size: 14px;
  font-weight: 700;
  color: #000000;
  cursor: pointer;
  width: 170px;
  text-align: center;
  background-color: #f4f3f3;
  padding: 7px 15px 7px 15px;
  border-radius: 25px;
  display: inline-block;
  text-decoration: none;
}

.see-all-review a i {
  font-size: 16px;
  margin-left: 20px;
}

.restaurant-status-title-bg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  border-radius: 20px;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
}

.restaurant-status-title-bg h5 {
  color: #fff;
  text-align: center;
  font-family: "Visby CF";
  font-size: 40px;
  font-weight: 700;
  line-height: 20px;
  margin-bottom: 0;
}

.allergy-info {
  text-align: right;
}

.allergy-info button {
  padding: 0;
  font-family: "Visby CF";
  font-weight: 700;
  line-height: 22px;
  font-size: 16px;
  color: #000000;
  text-decoration: none;
  background-color: transparent;
  border: 0;
}

.allergy-info button img {
  width: 18px;
  position: relative;
  margin-right: 8px;
}

#allergy-popup button.modal-black-btn {
  width: 180px;
  margin: auto;
  display: table;
  height: 46px;
  padding: 5px 10px;
}

#allergy-popup .modal-body p {
  font-size: 18px;
  font-weight: 700;
  line-height: 30px;
  margin-bottom: 20px;
}

.product-counter {
  font-size: 13px;
  color: #fff;
  font-weight: 600;
  width: 26px;
  height: 26px;
  line-height: 24px;
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  background-color: #000;
  position: absolute;
  top: -7px;
  right: -7px;
  z-index: 1;
  box-shadow: 0 0 2px 1px #fbfbfb;
}

#items-already-in-cart-popup .modal-body p {
  color: #000000;
}

#items-already-in-cart-popup .modal-body button.btn {
  padding: 10px 20px;
  font-size: 18px;
  margin: 0 15px;
}

.add-items {
  position: fixed;
  left: 0;
  right: 0;
  margin: auto;
  display: table;
  bottom: 10px;
  z-index: 100;
}

.add-items button.btn {
  font-size: 16px;
  line-height: 24px;
  padding: 8px 15px;
  border-radius: 30px;
  box-shadow: 0 0 10px 2px #b9b9b9;
  display: flex;
  justify-content: space-between;
  width: 650px;
}

.add-items button.btn i {
  margin-left: 2px;
}

.add-items button.btn:hover {
  background-color: #fff !important;
}

.addon-items ul {
  margin-bottom: 0;
}

.addon-items ul li {
  font-family: "Visby CF";
  font-size: 15px;
  color: #000000;
  font-weight: 700;
  line-height: 22px;
  display: inline-block;
  margin-right: 5px;
}

.addon-items ul li img {
  width: 17px;
  margin-top: -3px;
}

#opening-times {
  padding-top: 0;
}

#opening-times .nav.nav-tabs {
  margin: 10px 0;
  border: 0;
  flex-wrap: nowrap;
  padding: 5px;
  border-radius: 50px;
  background-color: #f4f3f3;
}

#opening-times .nav.nav-tabs li {
  width: 100%;
  margin: 0;
}

#opening-times .nav.nav-tabs li button {
  font-size: 16px;
  padding: 9px 15px;
  font-weight: 800;
  border: 0;
  border-radius: 25px;
  width: 100%;
  text-align: center;
}

#opening-times .nav.nav-tabs li.active button {
  background-color: var(--primary);
  color: #fff;
}

.edit-addon-btn {
  padding: 0;
  height: 20px;
  width: 28px;
  font-size: 10px;
  line-height: 18px;
  background-color: var(--primary) !important;
  border: 1px solid var(--primary) !important;
  margin-left: 15px;
  color: #fff;
  border-radius: 5px;
  position: relative;
  top: 3px;
  box-shadow: none !important;
  font-weight: 700;
}

.veg-nonveg {
  width: 16px;
  height: 16px;
  position: relative;
  border: 2px solid #3ab54a;
  margin-right: 8px;
}

.veg-nonveg::before {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #3ab54a;
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  top: 50%;
  transform: translate(0, -50%);
  margin: auto;
}

.veg-nonveg.noneveg-bg {
  border-color: #fe0000;
}

.veg-nonveg.noneveg-bg::before {
  background-color: #fe0000;
}

.subaddon-circle {
  margin: 0 2px 0 5px;
  font-size: 5px !important;
  color: #8f8f8a;
  vertical-align: middle;
}

.price-circle {
  font-size: 4px !important;
  color: #fff;
  vertical-align: middle;
}

.price-circle i {
  margin: 0px !important;
  transition: all 0.5s;
}

#addonpopup button.btn:hover .price-circle {
  color: #000;
  opacity: 0.7;
}
.spicy-list-images {
  margin: 10px 0 0 0;
}
.spicy-list-images li {
  display: inline-block;
  margin-right: 10px;
}
.spicy-list-images li img {
  width: 20px;
}
.reviews-rating-detail .reviews-show a {
  color: #111;
}

@media screen and (max-width: 1800px) {
  .menu-category-list.fixed .menu-section {
    padding: 15px 42px 8px;
  }

  .menu-category-list.fixed .menu-section .menu-inner-sec,
  .menu-category-list.fixed .menu-section .row {
    max-width: calc(100% - 416px);
  }
  .menu-inner-sec .menu-list li button {
    font-size: 15px;
  }
}

@media screen and (max-width: 1750px) {
  .delivery-pick-up-list li {
    padding: 12px 10px 5px 10px;
  }

  .delivery-pick-up-list li p {
    font-size: 18px;
  }

  .order-free-delivery-list li {
    font-size: 11px;
    margin: 0 4px;
  }

  .order-free-delivery-list li img {
    margin-right: 5px;
    width: 22px;
  }

  .menu-category-list.fixed .menu-section {
    padding: 15px 30px 8px;
  }

  .menu-category-list.fixed .category-inner-section {
    max-width: 100%;
    width: 100%;
  }

  .menu-category-list.fixed .menu-section .menu-inner-sec,
  .menu-category-list.fixed .menu-section .row {
    max-width: calc(100% - 406px);
  }

  .menu-top-line {
    padding-top: 15px;
  }

  .menu-section .search-for-restaurant input.form-control {
    font-size: 16px;
    width: 305px;
  }

  .favourite-box .favourite-image img {
    height: 163px;
  }

  .favourite-box .favourite-content h6 {
    font-size: 19px;
    margin-bottom: 2px;
  }

  .favourite-box .favourite-content span.price {
    font-size: 16px;
    padding-bottom: 2px;
  }

  .favourite-box .favourite-content p {
    font-size: 14px;
    line-height: 22px;
  }

  .horizontal-favourite-box .horizontal-content-box {
    padding: 20px 25px;
  }

  .horizontal-favourite-box .horizontal-content-box h6 {
    font-size: 20px;
    margin-bottom: 4px;
  }

  .horizontal-favourite-box .horizontal-content-box span.price {
    font-size: 16px;
    padding-bottom: 4px;
  }

  .horizontal-favourite-box .horizontal-content-box p {
    font-size: 14px;
    line-height: 20px;
  }
}

@media screen and (max-width: 1500px) {
  .favourite-icon {
    top: 20px;
    right: 32px;
  }

  .favourite-icon button img {
    width: 50px;
  }

  .see-all-review a {
    font-size: 10px;
    padding: 6px 10px;
    width: 127px;
  }

  .see-all-review a i {
    font-size: 10px;
    position: relative;
    top: 1px;
  }

  .make-reservation-btn {
    font-size: 13px;
    width: 165px;
    height: 37px;
    line-height: 24px;
    right: 40px;
    bottom: 24px;
  }

  .delivery-pick-up-list {
    padding: 3px 4px;
    margin-bottom: 10px;
  }

  .delivery-pick-up-list li {
    padding: 8px 5px 5px;
    line-height: normal;
  }

  .delivery-pick-up-list li p {
    font-size: 15px;
  }

  .order-free-delivery-list li img {
    width: 20px;
    position: relative;
    top: -2px;
  }

  .menu-category-list {
    margin-top: 35px;
    padding-bottom: 25px;
    min-height: 145px;
  }

  .menu-top-line {
    padding-top: 20px;
  }

  .menu-category-list.fixed .menu-section {
    padding: 10px 120px 8px;
  }

  .menu-category-list.fixed .menu-section .menu-inner-sec,
  .menu-category-list.fixed .menu-section .row {
    max-width: calc(100% - 306px);
    padding-top: 5px;
  }

  .menu-section h5.menu-title {
    font-size: 22px;
    margin: 0;
  }

  .menu-section .search-for-restaurant input.form-control {
    font-size: 13px;
    width: 270px;
    height: 30px;
    padding: 0 35px 0 35px;
  }

  .menu-section .search-for-restaurant .search-icon {
    font-size: 13px;
    left: 12px;
  }

  .menu-list-arrow button img {
    width: 20px;
  }

  .menu-inner-sec .menu-list {
    padding: 0 48px 0 50px;
  }

  .menu-inner-sec .menu-list li button {
    font-size: 14px;
    padding-bottom: 12px;
  }

  .menu-inner-sec .menu-list li button:before {
    height: 6px;
  }

  .menu-category-list.fixed
    .menu-section
    .menu-inner-sec
    .menu-list
    li
    a:before {
    height: 5px;
  }

  ::ng-deep.menu-inner-sec .menu-list button.slick-arrow.slick-next {
    right: -30px;
  }

  .customer-favourites-section .main-heading {
    margin-bottom: 20px;
  }

  .customer-favourites-section .main-heading h6 {
    font-size: 22px;
  }

  .allergy-info button {
    font-size: 14px;
  }

  .allergy-info button img {
    margin-right: 8px;
  }

  .favourite-box .favourite-image img {
    height: 135px;
  }

  .favourite-box .favourite-content h6 {
    font-size: 18px;
    line-height: 22px;
  }

  .favourite-box .favourite-content span.price {
    font-size: 15px;
    padding-bottom: 0;
  }

  .favourite-box .favourite-content p {
    font-size: 12px;
  }

  .horizontal-favourite-box {
    margin-bottom: 25px;
  }

  .horizontal-favourite-box .horizontal-image-box {
    width: 112px;
    min-width: 112px;
  }

  .horizontal-favourite-box .horizontal-content-box {
    padding: 16px 20px;
    max-width: calc(100% - 112px);
    width: 100%;
  }

  .horizontal-favourite-box .horizontal-content-box h6 {
    font-size: 18px;
    margin-bottom: 2px;
  }

  .horizontal-favourite-box .horizontal-content-box span.price {
    font-size: 15px;
    padding-bottom: 2px;
  }

  .horizontal-favourite-box .horizontal-content-box .addon-items ul {
    line-height: 20px;
  }

  .favourite-box .favourite-image button.add-option,
  .horizontal-favourite-box .horizontal-image-box button.add-option {
    font-size: 12px;
    width: 45px;
    height: 22px;
    line-height: 20px;
  }

  .favourite-box .favourite-image button.add-option {
    right: 8px;
    bottom: 8px;
  }

  .horizontal-favourite-box .horizontal-image-box button.add-option {
    right: 20px;
    bottom: 10px;
  }

  .addon-items ul li {
    font-size: 12px;
  }

  .addon-items ul li img {
    width: 14px;
    margin-top: 0px;
  }

  .view-menu-dropdown-bg {
    left: 38px;
    top: 1px;
  }

  .view-menu-dropdown-bg:after {
    top: 7px;
    left: -13px;
    width: 16px;
    height: 19px;
    background-size: 100%;
  }

  .view-menu-dropdown {
    width: 165px;
    box-shadow: 0px 0px 3px 0px #00000040;
    border-radius: 8px;
  }

  .view-menu-title {
    padding: 9px 14px 18px 18px;
  }

  .view-menu-title p {
    font-size: 14px;
    line-height: 22px;
  }

  .view-menu-title button.close {
    font-size: 10px;
    line-height: 15px;
    width: 15px;
    height: 15px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .view-menu-list ul li {
    padding-bottom: 12px;
  }

  .view-menu-list ul li {
    padding-bottom: 15px;
  }

  .view-menu-list ul li button {
    font-size: 12px;
    padding: 5px 20px;
  }

  .view-menu-list ul li button:before {
    width: 5px;
  }

  #addonpopup .modal-header {
    padding: 10px 65px;
  }

  #addonpopup .modal-header button.btn-close {
    left: 20px;
  }

  #addonpopup .modal-header h6 {
    font-size: 20px;
  }

  #addonpopup .modal-header span {
    font-size: 14px;
  }

  #addonpopup .modal-body {
    padding: 20px 20px 20px;
  }

  #addonpopup .addon-list h6 {
    font-size: 20px;
  }

  #addonpopup .addon-list h6 span {
    font-size: 13px;
  }

  #addonpopup .addon-list ul {
    padding: 0px 0 20px;
  }

  #addonpopup .addon-list ul li .form-check .form-check-label {
    font-size: 14px;
    padding: 10px 0;
  }

  #addonpopup .form-check input.form-check-input {
    width: 16px;
    height: 16px;
    margin-top: 14px;
  }

  #addonpopup button.btn {
    font-size: 14px;
    padding: 5px 15px;
  }

  .product-header {
    padding: 10px 20px;
  }

  .product-body {
    padding: 20px 20px;
  }

  .product-body h6 {
    font-size: 22px;
    margin-bottom: 5px;
  }

  .product-body .choose-options h6 {
    margin-bottom: 10px;
  }

  .choose-options .choose-list li .form-check .form-check-label {
    padding: 0;
  }

  .choose-options .choose-list li .form-check label {
    padding: 0 30px;
  }

  .choose-options .choose-list li .form-check input.form-check-input {
    width: 16px;
    height: 16px;
  }

  .choose-options .choose-list li .form-check .right-arrow i {
    font-size: 12px;
  }

  #allergy-popup .modal-body p {
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 30px;
  }

  #allergy-popup button.modal-black-btn {
    width: 160px;
    height: 36px;
    font-size: 14px;
  }

  #allergy-popup button.modal-black-btn i {
    font-size: 14px;
  }

  #items-already-in-cart-popup .modal-body p {
    margin-bottom: 15px;
  }

  #items-already-in-cart-popup .modal-body button.btn {
    font-size: 14px;
    margin: 0 5px;
    padding: 5px 20px;
  }

  #opening-times .nav.nav-tabs li button {
    font-size: 14px;
    padding: 7px 10px;
  }

  .view-menu-dropdown .view-menu-list {
    max-height: 345px;
  }

  .veg-nonveg {
    width: 12px;
    height: 12px;
    border: 1px solid #3ab54a;
    margin-right: 5px;
  }

  .veg-nonveg:before {
    width: 6px;
    height: 6px;
  }

  .menu-section .search-for-restaurant .search-close {
    top: 15px;
    font-size: 12px !important;
  }
  .menu-category-list.fixed .menu-inner-sec .menu-list {
    padding-right: 74px;
  }
}

@media screen and (max-width: 1330px) {
  .favourite-box .favourite-content h6 {
    font-size: 16px;
  }
}

@media screen and (max-width: 1300px) {
  .order-free-delivery-list li {
    font-size: 10px;
    margin: 0 2px;
  }

  .order-free-delivery-list li img {
    width: 18px;
    top: 1px;
    margin-right: 2px;
  }

  .favourite-box .favourite-image img {
    height: 120px;
  }
}

@media screen and (max-width: 1199px) {
  .menu-category-list.fixed .menu-section .menu-inner-sec,
  .menu-category-list.fixed .menu-section .row {
    max-width: 100%;
  }

  .menu-category-list.fixed .menu-section {
    padding: 15px 35px 8px 60px;
  }

  .order-free-delivery-list li {
    margin: 0 5px;
  }

  .favourite-icon {
    top: 15px;
    right: 20px;
  }
}

@media screen and (max-width: 991px) {
  .menu-top-line {
    padding-top: 15px;
  }

  .delivery-pick-up-box {
    margin-bottom: 15px;
  }

  .menu-category-list {
    margin-top: 20px;
  }

  .menu-category-list.fixed .menu-section {
    padding: 15px 25px 8px 50px;
  }

  .horizontal-favourite-box .horizontal-content-box {
    padding: 10px 15px;
  }

  .menu-category-list.fixed .menu-inner-sec .menu-list {
    padding-right: 73px;
  }

  .addon-items ul li {
    font-size: 11px;
    margin-right: 3px;
  }

  .addon-items ul li img {
    width: 11px;
    margin-top: -1px;
  }

  .addon-items ul li:last-child {
    margin-right: 0;
  }
}

@media screen and (max-width: 767px) {
  .menu-category-list.fixed .menu-section {
    padding: 15px 7px 8px 30px;
  }

  .menu-category-list.fixed .menu-inner-sec .menu-list {
    padding-right: 70px;
  }

  .add-items {
    width: 100%;
    padding: 0 30px;
  }

  .add-items button.btn {
    width: 100%;
  }

  .view-menu-dropdown .view-menu-list {
    max-height: 300px;
  }

  .delivery-pick-up-list li {
    padding: 9px 5px 7px;
    line-height: 20px;
  }
}

@media screen and (max-width: 575px) {
  .favourite-icon button img {
    width: 40px;
  }

  .make-reservation-btn {
    right: 20px;
    bottom: 20px;
  }

  .menu-inner-sec .menu-list {
    padding: 0 30px 0 40px;
    width: calc(100% - 10px);
  }
  .menu-inner-sec .menu-list li {
    padding: 0 20px;
  }
  .menu-category-list.fixed .menu-inner-sec .menu-list {
    padding-right: 53px;
  }

  .menu-inner-sec .menu-list button.slick-arrow.slick-next {
    right: -20px;
  }

  .restaurant-status-title-bg h5 {
    font-size: 36px;
  }

  .menu-section .search-for-restaurant input.form-control {
    width: 200px;
  }
}

@media screen and (max-width: 480px) {
  .horizontal-favourite-box .horizontal-content-box {
    padding: 12px 15px;
  }

  ::ng-deep.menu-inner-sec .menu-list button.slick-arrow.slick-next {
    right: -25px;
  }

  .menu-inner-sec {
    padding-top: 5px;
  }
  .menu-inner-sec .menu-list li {
    padding: 0 15px;
  }
  .menu-category-list.fixed .menu-section .menu-inner-sec {
    padding-top: 5px;
  }

  .menu-section .search-for-restaurant input.form-control {
    font-size: 12px;
    width: 180px;
  }

  .menu-category-list.fixed
    .menu-section
    .search-for-restaurant
    input.form-control {
    font-size: 12px;
    width: 180px;
  }

  .menu-category-list.fixed .menu-section {
    padding: 15px 0px 8px 22px;
  }

  .restaurant-status-title-bg {
    padding: 0 15px;
    border-radius: 15px;
  }

  .restaurant-status-title-bg h5 {
    font-size: 30px;
    line-height: 36px;
  }

  .make-reservation-btn {
    width: 155px;
    height: 35px;
    right: 15px;
    bottom: 15px;
    line-height: 20px;
  }

  .add-items {
    padding: 0 22px;
  }

  .add-items button.btn {
    font-size: 15px;
    padding: 6px 15px;
  }

  .horizontal-favourite-box .horizontal-content-box .addon-items ul {
    line-height: 16px;
    margin-top: 2px;
  }

  .horizontal-favourite-box .horizontal-content-box .addon-items ul li {
    line-height: 18px;
  }

  .view-menu-dropdown .view-menu-list {
    max-height: 260px;
  }
}

@media screen and (max-width: 400px) {
  .horizontal-favourite-box .horizontal-content-box h6 {
    line-height: 24px;
  }

  .favourite-box {
    margin-bottom: 20px;
  }

  .customer-favourites-section .main-heading {
    margin-bottom: 15px;
  }

  .customer-favourites-section .main-heading h6 {
    font-size: 20px;
  }
  .spicy-list-images {
    margin-top: 5px;
  }
  .spicy-list-images li {
    margin-right: 5px;
  }
  .spicy-list-images li img {
    width: 16px;
  }
}

// From GoGrubz styles.scss=========================================================================================================================================================

$alert-padding-y: 10px;

$alert-padding-x: 10px;
// @import "./assets/css/bootstrap.min.css";
@import "../../../../assets/css/all.min.css";
@import "../../../../assets/css/fonts.css";
/*@import "assets/css/animate.css";*/
@import "../../../../assets/css/slick.css";
// @import "bootstrap";

.cursor {
  cursor: pointer;
}

.spinner-border {
  width: 16px;
  height: 16px;
  border-width: 2px;
  margin-right: 2px;
}

.body-box-shadow {
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%), 0 8px 16px rgb(0 0 0 / 10%);
}

.ant-form-item-explain.ant-form-item-explain-error {
  color: var(--primary) !important;
}

.anticon-close-circle-fill .ng-star-inserted {
  display: none !important;
}

.modal-open {
  overflow: hidden;
}

body.open {
  overflow: hidden;
}

body {
  touch-action: manipulation;
  overscroll-behavior-y: none;
  padding: 20px 40px;
  margin: 0;
  background-color: #fff;
  font-family: "Visby CF";
  overflow-x: hidden;
}

/* ------General-CSS------ */

h2 {
  font-size: 76px;
  font-family: "Fredoka One";
  line-height: 106px;
  color: #202020;
  margin-bottom: 15px;
}

h3 {
  font-size: 52px;
  color: #202020;
  font-family: "Fredoka One";
  line-height: 74px;
}

h4 {
  font-size: 50px;
  color: #202020;
  font-family: "Fredoka One";
  line-height: 65px;
}

h5 {
  font-size: 40px;
  color: #202020;
  font-family: "Fredoka One";
  line-height: 45px;
}

h6 {
  font-size: 25px;
  color: #202020;
  font-family: "Fredoka One";
  line-height: 30px;
}

p {
  font-size: 20px;
  color: #202020;
  line-height: 28px;
}

ul {
  padding: 0;
  margin: 0;
  margin-bottom: 15px;
  list-style: none;
}

.btn {
  font-size: 16px;
  line-height: 20px;
  padding: 8px 15px;
  color: #fff !important;
  background: var(--primary) !important;
  border: 2px solid var(--primary) !important;
  border-radius: 50px;
  transition: all 0.5s;
}

.btn:hover {
  color: var(--primary) !important;
  background: transparent;
}

body.open-slider {
  overflow: hidden;
}

.main-heading {
  margin-bottom: 40px;
}

.main-heading h2 {
  font-size: 55px;
  line-height: 65px;
  margin-bottom: 40px;
}

.enter-postcode-address {
  max-width: 750px;
  width: 100%;
  position: relative;
  margin-bottom: 55px;
}

.enter-postcode-address input.form-control {
  font-size: 21px;
  color: #000 !important;
  font-weight: 600;
  width: 100%;
  height: 84px;
  border: 0;
  border-radius: 50px;
  padding: 0 230px 0 85px;
  background-color: #ffffff !important;
}

.enter-postcode-address input.form-control::-webkit-input-placeholder {
  color: #b3b3b3;
  opacity: 1;
}

.enter-postcode-address input.form-control::-moz-placeholder {
  color: #b3b3b3;
  opacity: 1;
}

.enter-postcode-address input.form-control:-ms-input-placeholder {
  color: #b3b3b3;
  opacity: 1;
}

.enter-postcode-address input.form-control:-moz-placeholder {
  color: #b3b3b3;
  opacity: 1;
}

.enter-postcode-address button.btn {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 21px;
  color: #fff !important;
  border: 0 !important;
  border-radius: 35px;
  font-weight: 500;
  padding: 0;
  text-align: center;
  width: 140px;
  height: 55px;
  background-color: #000 !important;
}

.enter-postcode-address img.send-icon {
  position: absolute;
  left: 25px;
  top: 25px;
  width: 40px;
}

.enter-postcode-address a.close-icon {
  position: absolute;
  right: 180px;
  padding: 0;
  border: 0;
  outline: none;
  box-shadow: none;
  background-color: initial;
  top: 25px;
}

.enter-postcode-address .ant-form-item-explain.ant-form-item-explain-error {
  color: #fff !important;
}

.inner-page {
  padding-top: 142px !important;
}

button.slick-arrow {
  position: absolute;
  top: -55px;
  right: 0;
  font-size: 0;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  text-align: center;
  line-height: 18px;
  box-shadow: 0px 0px 10px 0px #********;
  padding: 0;
  border: 0;
  background-color: #fff;
  transition: all 0.4s;
}

button.slick-arrow:hover {
  background-color: #d3d1d1;
}

button.slick-arrow.slick-prev {
  margin-right: 70px;
  left: auto;
}

button.slick-arrow.slick-prev::before {
  font-size: 16px;
  color: #000;
  opacity: 1;
  position: absolute;
  content: "\f053";
  font-family: "fontawesome";
  left: 0;
  right: 0;
}

button.slick-arrow.slick-next::before {
  font-size: 16px;
  color: #000;
  opacity: 1;
  position: absolute;
  content: "\f054";
  font-family: "fontawesome";
  left: 0;
  right: 0;
}

button.slick-arrow.slick-disabled {
  display: none !important;
}

.location-box {
  padding: 9px 20px 5px 18px;
  border-radius: 35px;
  background-color: #f4f3f3;
  margin-bottom: 45px;
}

.location-box ul {
  display: flex;
  justify-content: space-between;
  margin: 0;
}

.location-box ul li {
  font-size: 15px;
  color: #000000;
  font-weight: 700;
}

.location-box ul li.dot {
  margin: 0 22px;
}

.location-box ul li .fa-location-dot {
  width: 16px;
  color: var(--primary);
  font-size: 24px;
  position: relative;
  top: -2px;
  margin-right: 20px;
}

.location-box ul li .fa-circle {
  width: 5px;
  font-size: 5px;
  position: relative;
  top: -4px;
}

.location-box ul li a {
  color: #8b8f8f;
  text-decoration: underline;
}

.search-filter-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 48px;
}

.search-for-restaurant {
  position: relative;
}

.search-for-restaurant .search-icon {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  left: 16px;
  font-size: 18px;
}

.search-for-restaurant input.form-control {
  font-family: "Visby CF";
  font-size: 15px;
  line-height: 30px;
  padding: 0 20px 0 50px;
  width: 375px;
  height: 40px;
  background-color: #f4f3f3;
  border: 0;
  outline: none;
  box-shadow: none;
  border-radius: 40px;
}

.search-for-restaurant input.form-control::-webkit-input-placeholder {
  color: #8b8f8f;
  opacity: 1;
}

.search-for-restaurant input.form-control::-moz-placeholder {
  color: #8b8f8f;
  opacity: 1;
}

.search-for-restaurant input.form-control:-ms-input-placeholder {
  color: #8b8f8f;
  opacity: 1;
}

.search-for-restaurant input.form-control:-moz-placeholder {
  color: #8b8f8f;
  opacity: 1;
}

.search-for-restaurant button.btn {
  color: #fff !important;
  font-weight: 600;
  background: var(--primary) !important;
  width: 100px;
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: 10px;
}

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary) !important;
}

.product-box {
  padding-bottom: 30px;
}

.product-box .product-image {
  position: relative;
  overflow: hidden;
  min-height: 184px;
  border: 1px solid #f4f3f3;
  border-radius: 10px;
}

.product-box .product-image img {
  width: 100%;
}

.product-box .product-image img.product-large-image {
  width: 100%;
  min-height: 184px;
  max-height: 184px;
  transition: all 0.5s;
  object-fit: cover;
  border-radius: 10px;
}

.product-box .product-image:hover img.product-large-image {
  transform: scale(1.1);
}

.product-box .product-image .product-logo {
  position: absolute;
  z-index: 1;
  top: 15px;
  left: 15px;
  padding: 5px;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #ffffff;
  box-shadow: 0px 4px 15px 0px #00000080;
}

.product-box .product-image .product-logo img {
  width: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.product-box .product-content {
  padding-top: 10px;
}

.product-box .product-content h5 {
  font-family: "Fredoka";
  font-weight: 500;
  font-size: 28px;
  margin-bottom: 2px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.product-box .product-content ul.rating-list {
  display: flex;
  margin-bottom: 5px;
}

.product-box .product-content ul.rating-list li {
  font-size: 16px;
  color: #8f8f8a;
  font-weight: 700;
  position: relative;
  padding-right: 10px;
  margin-right: 10px;
}

.product-box .product-content ul.rating-list li img {
  width: 13px;
  margin-left: 5px;
  margin-right: 5px;
  margin-top: -5px;
  display: inline-block;
}

.product-box .product-content ul.times-list li::before,
.product-box .product-content ul.rating-list li::before {
  position: absolute;
  content: "";
  width: 4px;
  height: 4px;
  border-radius: 50%;
  right: 0;
  top: 50%;
  transform: translate(0, -50%);
  background-color: #8f8f8a;
}

.product-box .product-content ul.rating-list li:last-child:before {
  display: none;
}

.product-box .product-content ul.times-list {
  display: flex;
  margin-bottom: 12px;
}

.product-box .product-content ul.times-list li {
  font-size: 15px;
  color: #8f8f8a;
  font-weight: 700;
  position: relative;
  padding-right: 15px;
  margin-right: 15px;
}

.product-box .product-content ul.times-list li:last-child:before {
  display: none;
}

.modal-dialog {
  max-width: 600px;
}

.modal-content .modal-header {
  padding: 10px 44px;
  border-bottom: 2px solid #f4f3f3;
}

.modal-content .modal-header h5.modal-title {
  font-size: 40px;
  color: #000;
  font-family: "Visby CF";
  font-weight: 800;
}

.modal-header .btn-close {
  padding: 0;
  margin: 0;
  width: 30px;
  height: 30px;
  opacity: 1;
  border-radius: 50%;
  text-align: center;
  background-color: #f4f3f3;
  background-image: none;
  outline: none;
}

.modal-dialog .modal-content {
  border: 0;
  border-radius: 20px;
}

.modal-dialog .modal-content button.btn-close {
  font-size: 20px;
  padding: 0;
  color: #000000;
  background-image: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  background-color: #f4f3f3;
  opacity: 1;
  position: absolute;
  top: 25px;
  left: 25px;
  z-index: 12;
  box-shadow: none;
}
.modal-dialog .modal-content button.btn-close i {
  height: 15px;
  position: relative;
  top: -2px;
}
.modal-footer {
  padding: 0;
  justify-content: center;
  border: 0;
}

.modal-footer button.btn {
  width: 200px;
  height: 50px;
  font-weight: 700;
  margin: 0 30px;
}

.modal-footer button.btn.btn-secondary {
  color: var(--primary) !important;
  border: 2px solid var(--primary) !important;
  background-color: transparent !important;
}

.product-main-box-section {
  // padding-top: 107px;
  padding-bottom: 60px;
  width: 100%;
  display: inline-block;
  background: #fff;
}

.product-details-left {
  max-width: 100%;
  width: 100%;
  padding-top: 40px;
}
.added-menu-item .product-details-left {
  max-width: calc(100% - 400px);
  padding-right: 40px;
}
.product-main-box {
  position: relative;
  border: 1px solid #ccc;
  border-radius: 20px;
}

.product-main-box img {
  width: 100%;
}

.product-main-box img.product-main-image {
  height: 350px;
  object-fit: cover;
  border-radius: 20px;
}

.product-main-box .product-logo {
  padding: 0 10px;
  width: 120px;
  height: 120px;
  line-height: 118px;
  background-color: #fff;
  box-shadow: 0px 0px 25px 0px #********;
  border-radius: 50%;
  text-align: center;
  position: absolute;
  left: 35px;
  bottom: -40px;
}

.product-main-box .product-logo img {
  width: 100%;
  border-radius: 50%;
}

.modal-content .login-body {
  padding: 20px 55px 30px 55px;
}

.login-title {
  font-family: "Fredoka";
  font-size: 30px;
  font-weight: 500;
  line-height: 36px;
  text-align: center;
  margin-bottom: 20px;
}

.dont-have-account-text {
  font-family: "Fredoka";
  font-size: 18px;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
  margin-bottom: 22px;
}

.dont-have-account-text button {
  color: var(--primary) !important;
  cursor: pointer;
  text-decoration: underline !important;
  background-color: transparent;
  border: 0;
  padding: 0;
  font-weight: 500;
}

.form-group {
  position: relative;
  margin-bottom: 20px;
}

.form-group span.icon {
  position: absolute;
  top: 5px;
  right: 14px;
}

.form-control {
  font-family: "Visby CF";
  font-size: 18px;
  font-weight: 700;
  width: 100%;
  height: 40px;
  padding: 4px 15px 7px 15px;
  background-color: #f4f3f3 !important;
  border: 0;
  box-shadow: none !important;
  border-radius: 10px;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.select-box {
  background-color: #f4f3f3 !important;
  border-radius: 10px;
  position: relative;
}

.select-box::before {
  content: "\f078";
  font-family: "fontawesome";
  font-size: 18px;
  color: #8f8f8a;
  position: absolute;
  right: 10px;
  top: 6px;
}

.select-box .form-control {
  background-color: transparent !important;
  position: relative;
  z-index: 1;
}

.modal-content .login-body button.btn {
  font-size: 18px;
  width: 100%;
  padding: 7px 20px;
}

.or-option {
  margin: 18px 0;
  position: relative;
  text-align: center;
}

.or-option::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 1px;
  top: 13px;
  left: 0;
  right: 0;
  background-color: #8f8f8a;
}

.or-option span {
  width: 60px;
  color: #8f8f8a;
  font-family: "Visby CF";
  font-size: 15px;
  font-weight: 800;
  line-height: 24px;
  text-align: center;
  background-color: #fff;
  display: inline-block;
  z-index: 1;
  position: relative;
}

.socials-login-options ul {
  margin-bottom: 0;
}

.socials-login-options ul li {
  width: 100%;
  margin-bottom: 19px;
}

.socials-login-options ul li:last-child {
  margin-bottom: 0;
}

.socials-login-options ul li a {
  font-family: "Visby CF";
  font-size: 18px;
  color: #fff;
  cursor: pointer;
  font-weight: bold;
  line-height: 40px;
  text-align: center;
  width: 100%;
  height: 40px;
  border-radius: 25px;
  display: inline-block;
  text-decoration: none;
}

.socials-login-options ul li a.google-btn {
  background-color: #4285f6;
}

.socials-login-options ul li a.google-btn img {
  margin-right: 18px;
  margin-top: -4px;
}

.socials-login-options ul li a.facebook-btn {
  background-color: #3c5997;
}

.socials-login-options ul li a.facebook-btn img {
  margin-right: 8px;
  margin-top: -5px;
}

.socials-login-options ul li a.apple-btn {
  background-color: #000000;
}

.socials-login-options ul li a.apple-btn img {
  margin-right: 24px;
  margin-top: -5px;
}

.terms-conditions {
  margin: 5px 0 30px 0;
}

.terms-conditions p {
  font-family: "Fredoka";
  font-size: 18px;
  color: #000000;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
}

.terms-conditions p a {
  color: #000000;
}

.add-cart-box {
  min-width: 400px;
  max-width: 400px;
  height: 100%;
  background-color: #ffffff;
  border: 2px solid #f4f3f3;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 125;
  right: -100%;
  visibility: hidden;
  opacity: 0;
  transition: all 0.5s;
}

body.open .add-cart-box {
  right: 0;
  visibility: visible;
  opacity: 1;
}

.add-cart-box .cart-top {
  padding: 20px 25px 15px 25px;
  border-bottom: 2px solid #f4f3f3;
}

.add-cart-box .cart-top button.close {
  width: 35px;
  height: 35px;
  color: #000;
  background-color: #f4f3f3;
  padding: 0;
  font-family: "Fredoka";
  font-size: 20px;
  font-weight: 500;
  line-height: 30px;
  border-radius: 50%;
  border: 0;
  text-align: center;
  margin-bottom: 25px;
}
.add-cart-box .cart-top button.close i {
  width: 10px;
  position: relative;
  top: -2px;
}
.add-cart-box .top-cart-name {
  display: flex;
}

.add-cart-box .top-cart-name .cart-logo {
  width: 52px;
  height: 52px;
  line-height: 48px;
  background-color: #ffffff;
  box-shadow: 0px 0px 10px 2px #0000001a;
  border-radius: 50%;
  text-align: center;
}

.add-cart-box .top-cart-name .cart-logo img {
  width: 40px;
}

.add-cart-box .top-cart-name .cart-name {
  padding-left: 20px;
  margin-bottom: 16px;
}

.add-cart-box .top-cart-name .cart-name h6 {
  font-family: "Visby CF";
  font-size: 25px;
  font-weight: 800;
  margin-bottom: 0px;
}

.add-cart-box .top-cart-name .cart-name h6 img {
  margin-left: 16px;
  margin-top: -5px;
}

.add-cart-box .top-cart-name .cart-name p {
  font-family: "Visby CF";
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 0;
}
.add-cart-box .checkout-btn a,
.add-cart-box .checkout-btn button {
  width: 100%;
  height: 40px;
  line-height: 34px;
  background-color: var(--primary);
  display: flex;
  justify-content: space-between;
  text-decoration: none;
  border-radius: 25px;
  padding: 0 15px 0 20px;
  border: 2px solid var(--primary);
}
.add-cart-box .checkout-btn a:hover,
.add-cart-box .checkout-btn button:hover {
  background-color: transparent;
}
.add-cart-box .checkout-btn a:hover span,
.add-cart-box .checkout-btn button:hover span {
  color: var(--primary);
}
.add-cart-box .checkout-btn a span,
.add-cart-box .checkout-btn button span {
  color: #fff;
  font-family: "Visby CF";
  font-size: 20px;
  font-weight: 700;
  border-radius: 25px;
  display: flex;
  align-items: center;
}
.add-cart-box .checkout-btn a span i,
.add-cart-box .checkout-btn button span i {
  font-size: 25px;
  margin-left: 15px;
}

.add-cart-box .cart-middle {
  max-height: calc(100vh - 216px);
  overflow: hidden;
  overflow-y: auto;
}

.add-cart-box .cart-middle::-webkit-scrollbar {
  width: 5px;
}

.add-cart-box .cart-middle::-webkit-scrollbar-track {
  background: #ffe6e7;
  border-radius: 10px;
}

.add-cart-box .cart-middle::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 10px;
}

.add-cart-box .cart-middle::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

.add-cart-box .cart-item {
  padding: 12px 15px 12px 25px;
  border-bottom: 2px solid #f4f3f3;
  display: flex;
  align-items: center;
}

.add-cart-box .cart-item .cart-image-item {
  padding-right: 15px;
}

.add-cart-box .cart-item .cart-image-item img {
  width: 83px;
  height: 83px;
  border-radius: 10px;
}

.add-cart-box .cart-item .cart-content {
  width: 100%;
  padding-right: 5px;
}

.add-cart-box .cart-item .cart-content h6 {
  font-family: "Visby CF";
  font-size: 18px;
  line-height: 20px;
  font-weight: 800;
  word-break: break-word;
  margin-bottom: 0px;
}

.add-cart-box .cart-item .cart-content ul {
  padding: 0;
  margin-bottom: 2px;
}

.add-cart-box .cart-item .cart-content ul li {
  font-family: "Visby CF";
  font-size: 18px;
  color: #8f8f8a;
  font-weight: 600;
  padding-left: 10px;
  margin-left: 10px;
  display: inline-block;
  position: relative;
}

.add-cart-box .cart-item .cart-content ul li::before {
  position: absolute;
  content: "";
  top: 50%;
  width: 4px;
  height: 4px;
  left: -2px;
  border-radius: 50%;
  background-color: #8f8f8a;
}

.add-cart-box .cart-item .cart-content ul li:first-child {
  padding-left: 0;
  margin-left: 0;
}

.add-cart-box .cart-item .cart-content ul li:first-child:before {
  display: none;
}

.add-cart-box .cart-item .cart-content p {
  font-family: "Visby CF";
  font-size: 18px;
  line-height: 25px;
  font-weight: 600;
  margin-bottom: 0;
}

.add-cart-box .cart-item .cart-add-item {
  height: 100%;
}

.add-cart-box .cart-item .cart-add-item ul {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  background: #f4f3f3;
  border-radius: 25px;
  box-shadow: 0px 0px 5px 0px #********;
}

.add-cart-box .cart-item .cart-add-item ul li {
  font-family: "Fredoka";
  font-size: 15px;
  color: #000000;
  font-weight: 500;
  line-height: 30px;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.add-cart-box .cart-item .cart-add-item ul li span {
  padding: 0 10px;
}

.add-cart-box .cart-item .cart-add-item ul li button {
  font-size: 12px;
  color: #000000;
  width: 28px;
  height: 28px;
  line-height: 30px;
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  cursor: pointer;
  padding: 0;
  border: 0;
  background-color: #ffffff;
}

.product-main-box-section .container {
  max-width: 1742px;
  display: flex;
  padding: 0rem 2rem;

  @media screen and (max-width: 992px) {
    padding: 0rem;
  }
}

.product-rating-section {
  padding-top: 72px;
}

.rating-main-box {
  padding-right: 60px;
}

.product-name h5 {
  font-size: 45px;
  margin-bottom: 14px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  line-height: 58px;
}

.product-rating {
  margin-bottom: 12px;
}

.product-rating ul {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

.product-rating ul li {
  font-size: 15px;
  font-weight: 700;
  color: #8f8f8a;
  font-family: "Visby CF";
  position: relative;
  padding-right: 16px;
  margin-right: 18px;
}

.product-rating ul li::before {
  position: absolute;
  content: "";
  width: 2px;
  height: 28px;
  background-color: #8b8f8f;
  top: 50%;
  transform: translate(0, -50%);
  right: 0;
}

.product-rating ul li:last-child {
  padding-right: 0;
  margin-right: 0;
}

.product-rating ul li:last-child::before {
  display: none;
}

.product-rating ul li .fa-circle {
  font-size: 5px;
  margin: 0 11px;
  position: relative;
  top: -3px;
}

.product-rating ul li span {
  font-size: 25px;
  font-weight: 700;
  color: #000000;
  font-family: "Visby CF";
}

.product-rating ul li .fa-star {
  width: 25px;
  font-size: 25px;
  color: var(--primary);
  position: relative;
  top: -2px;
  margin-right: 5px;
}

.product-rating ul li .fa-location-dot {
  font-size: 19px;
  color: var(--primary);
  margin-right: 8px;
}

.open-close {
  margin-bottom: 45px;
}

.open-close ul {
  display: flex;
  margin-bottom: 0;
}

.open-close ul li {
  font-size: 15px;
  color: #8f8f8a;
  font-family: "Visby CF";
  font-weight: 700;
}

.open-close ul li.red-text {
  color: #ff0000;
}

.open-close ul li.green-text {
  color: #14a411;
}

.open-close ul li.green-text i {
  margin-right: 5px;
  position: relative;
  top: 1px;
}

.open-close ul li .fa-circle {
  font-size: 4px;
  top: -4px;
  position: relative;
  margin-left: 15px;
  margin-right: 15px;
}

.reviews-rating-main h6 {
  color: #000000;
  font-size: 30px;
  font-weight: 800;
  font-family: "Visby CF";
  margin-bottom: 30px;
}

.reviews-rating-box {
  padding: 25px 25px 12px 30px;
  border: 2px solid #f4f3f3;
  border-radius: 20px;
}

.reviews-rating-detail {
  display: flex;
  margin-bottom: 30px;
}

.reviews-show {
  width: 160px;
  text-align: center;
}

.reviews-rating-box a {
  text-decoration: none;
}

.reviews-show h4 {
  font-size: 60px;
  color: #000000;
  font-weight: 800;
  font-family: "Visby CF";
}

.reviews-show .reviews-list {
  display: flex;
  justify-content: center;
  margin-bottom: 5px;
}

.reviews-show .reviews-list li {
  margin: 0 1px;
}

.reviews-show .reviews-list li a {
  font-size: 25px;
  color: var(--primary);
  cursor: pointer;
}

.reviews-show .reviews-list li a i {
  color: var(--primary);
}

.reviews-show .reviews-list span {
  font-size: 15px;
  color: #8f8f8a;
  font-family: "Visby CF";
  font-weight: 700;
}

.rating-show {
  width: 100%;
  padding-left: 70px;
}

.progress-box {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.progress-box span {
  font-family: "Visby CF";
  font-size: 15px;
  font-weight: 700;
  padding-right: 25px;
  color: #000;
}

.progress-box .progress {
  width: 100%;
  border-radius: 10px;
  background-color: #f4f3f3;
}

.progress-box .progress .progress-bar {
  border-radius: 10px;
  background-color: var(--primary);
}

.rating-list .rating-box {
  padding-top: 15px;
  padding-bottom: 15px;
  border-top: 2px solid #f4f3f3;
}

.rating-list .rating-box p {
  font-size: 18px;
  color: #000000;
  font-weight: 600;
  font-family: "Visby CF";
  margin-bottom: 5px;
  word-wrap: break-word;
}

.rating-list .rating-box .rating-details {
  display: flex;
  align-items: center;
}

.rating-list .rating-box .rating-details span {
  color: #8f8f8a;
  font-size: 14px;
  font-weight: 700;
  font-family: "Visby CF";
}

.rating-list .rating-box .rating-details .rating-star {
  display: flex;
  margin-bottom: 0;
  margin-right: 12px;
}

.rating-list .rating-box .rating-details .rating-star li {
  margin-right: 2px;
}

.rating-list .rating-box .rating-details .rating-star li span {
  font-size: 14px;
  color: #000000;
}

.rating-list .rating-box .rating-details .fa-circle {
  font-size: 4px;
  color: #8f8f8a;
  margin-left: 12px;
  margin-right: 12px;
  top: 2px;
  position: relative;
}

.restaurant-location-box {
  border: 2px solid #f4f3f3;
  border-radius: 20px;
  margin-bottom: 15px;
}

.restaurant-location-box .location-map iframe {
  width: 100%;
  height: 280px;
  border-radius: 20px 20px 0 0;
}

.restaurant-location-box .location-details {
  text-align: left;
  padding: 0 23px 10px 23px;
}

.restaurant-location-box .location-details ul {
  margin-bottom: 0;
}

.restaurant-location-box .location-details ul li a {
  font-size: 15px;
  font-weight: 700;
  color: #000000;
  padding: 15px 25px 15px 0;
  position: relative;
  font-family: "Visby CF";
  display: inline-block;
  width: 100%;
  text-decoration: none;
  border-bottom: 2px solid #f4f3f3;
}

.restaurant-location-box .location-details ul li:last-child a {
  border-bottom: 0;
}

.restaurant-location-box .location-details ul li a img {
  margin-right: 16px;
}

.restaurant-location-box .location-details ul li a .arrow-right {
  position: absolute;
  right: 0;
}

.restaurant-location-box .location-details ul li a .arrow-right i {
  font-size: 18px;
  margin-top: 2px;
  transition: all 0.5s;
  transform: rotate(180deg);
}

.restaurant-location-box .location-details ul li a.collapsed .arrow-right i {
  transform: rotate(0deg);
}

.restaurant-location-box .location-details ul li a .arrow-right img {
  margin: 0;
}

.restaurant-location-box .location-details ul li a img.hygiene-rating-img {
  margin-left: 65px;
}

#opening-times {
  padding: 15px 0 0 0;
}

#opening-times ul li {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

#opening-times ul li span {
  font-size: 13px;
  color: #000;
  font-weight: bold;
}

#opening-times ul li span:first-child {
  width: 75px;
}

#opening-times ul li span:nth-child(2) {
  width: 130px;
  text-align: right;
}

.checkout-page {
  padding-top: 107px;
  padding-bottom: 50px;
}

.checkout-page .container {
  max-width: 1555px;
}

.checkout-section .login-box {
  max-width: 883px;
  width: 100%;
  padding-top: 18px;
  padding-right: 35px;
}

.add-to-cart-box-two {
  // position: sticky;
  // top: 127px;
  // height: calc(100vh - 127px);
  position: sticky;
  top: 0px;
  height: 100vh;
}

.add-to-cart-box-two .add-cart-box {
  height: 100%;
  position: inherit;
  visibility: visible;
  opacity: 1;
}

.add-to-cart-box-two .add-cart-box .cart-top {
  padding-top: 17px;
  padding-bottom: 9px;
}

.add-to-cart-box-two .add-cart-box .top-cart-name .cart-name p {
  line-height: 24px;
}

.add-to-cart-box-two .add-cart-box .cart-top .checkout-btn button.btn {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.add-to-cart-box-two .add-cart-box .checkout-btn a {
  width: 100%;
}

.add-to-cart-box-two .add-cart-box .checkout-btn a.disabled {
  background-color: #f4f3f3;
  border-color: #f4f3f3;
}

.add-to-cart-box-two .add-cart-box .checkout-btn a.disabled span {
  color: #8f8f8a;
}

.add-to-cart-box-two .add-cart-box .cart-middle {
  // max-height: calc(100vh - 275px);
  max-height: calc(100vh - 144px);
}

.add-to-cart-box-two .add-cart-box .cart-middle .cart-item:last-child {
  border-bottom: 0;
}

.subtotal-price-box {
  width: 250px;
  border-bottom: 2px solid#F4F3F3;
  border-right: 2px solid#F4F3F3;
}

.subtotal-box {
  padding: 20px 0;
  border-bottom: 2px solid#F4F3F3;
}

.subtotal-box ul {
  margin-bottom: 0;
  padding-left: 15px;
  padding-right: 15px;
}

.subtotal-box ul li {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.subtotal-box ul li span {
  font-family: "Visby CF";
  font-size: 12px;
  color: #000;
  font-weight: 600;
}

.subtotal-box ul li:first-child {
  margin-bottom: 24px;
}

.subtotal-box ul li:first-child span {
  font-family: "Visby CF";
  font-size: 15px;
  color: #000;
  font-weight: 700;
}

.subtotal-box ul li:last-child {
  margin-bottom: 0;
}

.subtotal-box ul.add-promo-code li {
  margin-bottom: 4px;
}

.subtotal-box ul.add-promo-code li:first-child {
  margin-bottom: 4px;
}

.subtotal-box ul.add-promo-code li.green-text span {
  color: #14a411;
}

.promo-code-box {
  padding: 10px 20px 20px 20px;
}

.promo-code-box h6 {
  font-family: "Visby CF";
  font-size: 20px;
  color: #000;
  font-weight: 800;
  margin-bottom: 22px;
}

.promo-code-box .promo-code {
  font-family: "Visby CF";
  font-size: 12px;
  color: #000;
  font-weight: 700;
  margin-top: 15px;
  margin-bottom: 0;
  text-align: center;
}

.promo-code-box .promo-code.successfully-applied {
  color: #14a411;
}

.promo-code-box .promo-code.not-valid-promo-code {
  color: var(--primary);
}

.promo-code-box h6 img {
  margin-left: 15px;
}

.promo-code-box input.form-control {
  font-size: 12px;
  color: #000;
  font-weight: 700;
  height: 40px;
  padding: 0 12px;
  border-radius: 25px;
  background: #f4f3f3;
  margin-bottom: 0px;
}
.promo-code-box
  input.form-control.error-red-placeholder::-webkit-input-placeholder {
  color: var(--primary);
  opacity: 1;
}
.promo-code-box input.form-control.error-red-placeholder::-moz-placeholder {
  color: var(--primary);
  opacity: 1;
}
.promo-code-box input.form-control.error-red-placeholder:-ms-input-placeholder {
  color: var(--primary);
  opacity: 1;
}
.promo-code-box input.form-control.error-red-placeholder:-moz-placeholder {
  color: var(--primary);
  opacity: 1;
}
.promo-code-box
  input.form-control.error-successfully-placeholder::-webkit-input-placeholder {
  color: #14a411;
  opacity: 1;
}
.promo-code-box
  input.form-control.error-successfully-placeholder::-moz-placeholder {
  color: #14a411;
  opacity: 1;
}
.promo-code-box
  input.form-control.error-successfully-placeholder:-ms-input-placeholder {
  color: #14a411;
  opacity: 1;
}
.promo-code-box
  input.form-control.error-successfully-placeholder:-moz-placeholder {
  color: #14a411;
  opacity: 1;
}
.promo-code-box .error-text {
  color: var(--primary) !important;
  font-size: 13px;
}

.promo-code-box button.btn {
  font-size: 10px;
  font-weight: 600;
  padding: 0;
  width: 85px;
  height: 25px;
  line-height: 20px;
  margin: auto;
  margin-top: 20px;
  display: table;
}

.form-group.postcode-group input.form-control {
  padding-right: 100px;
}

.postcode-group div.btn {
  font-size: 12px;
  font-weight: 700;
  width: 75px;
  height: 30px;
  position: absolute;
  top: 10px;
  right: 18px;
  padding: 0;
}

.next-btn button.btn {
  margin: auto;
  max-width: 490px;
  font-family: "Visby CF";
  font-size: 22px;
  line-height: 28px;
  font-weight: 500;
  padding: 8px 20px;
  height: 45px;
}

button.btn:disabled {
  color: #8f8f8a !important;
  border-color: #f4f3f3 !important;
  background-color: #f4f3f3 !important;
  pointer-events: none;
  opacity: 1;
}

.order-place-btn button.btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.order-place-btn button.btn i {
  font-size: 24px;
  margin-left: 5px;
  position: relative;
  top: 2px;
}

.order-place-btn span {
  font-size: 20px;
}

.order-place-btn button:disabled span {
  color: #8f8f8a;
}

.loader-height {
  min-height: calc(100vh - 335px);
}

.add-to-product {
  margin: 0 0 20px 0;
}

.add-to-product ul {
  margin-bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f4f3f3;
  border-radius: 25px;
  box-shadow: 0px 0px 5px 0px #********;
  margin-bottom: 0;
}

.add-to-product ul li {
  font-family: "Fredoka";
  font-size: 15px;
  color: #000000;
  font-weight: 500;
  line-height: 30px;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.add-to-product ul li a {
  font-size: 14px;
  color: #000;
  width: 34px;
  height: 34px;
  line-height: 36px;
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  cursor: pointer;
  background-color: #fff;
}

.add-to-product ul li span {
  font-size: 18px;
  padding: 0 15px;
}

.empty-cart-cls img {
  margin-bottom: 15px;
}

.empty-cart-cls p {
  font-size: 24px;
  margin-bottom: 0;
}

.pagination li.page-item a.page-link {
  color: #202020;
  cursor: pointer;
  font-weight: 500;
}

.pagination li.page-item.active a.page-link {
  color: #fff;
  background-color: var(--primary);
}

.dont-have-order p {
  font-size: 24px;
}

.btn.modal-black-btn,
a.modal-black-btn,
button.modal-black-btn {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}

a.modal-black-btn:hover,
button.modal-black-btn:hover {
  color: var(--primary) !important;
  background-color: transparent !important;
}

button.modal-black-btn i {
  margin-right: 15px;
}

.grubz-loader {
  position: relative !important;
  overflow: hidden !important;
  height: 150px !important;
  width: 150px !important;
  margin: auto !important;
  margin-top: 50px !important;
}

.grubz-loader .circle {
  width: 14px !important;
  height: 14px !important;
  position: absolute !important;
  background: var(--primary) !important;
  border-radius: 50% !important;
  margin: -7px !important;
  -webkit-animation: grubz 3s ease-in-out infinite -1.5s !important;
  animation: grubz 3s ease-in-out infinite -1.5s !important;
}

.grubz-loader > div .circle:last-child {
  -webkit-animation-delay: 0s !important;
  animation-delay: 0s !important;
}

.grubz-loader > div {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
}

.grubz-loader > div:last-child {
  -webkit-transform: rotate(90deg) !important;
  -ms-transform: rotate(90deg) !important;
  transform: rotate(90deg) !important;
}

@-webkit-keyframes grubz {
  0% {
    -webkit-transform-origin: 50% -100%;
    transform-origin: 50% -100%;
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  50% {
    -webkit-transform-origin: 50% -100%;
    transform-origin: 50% -100%;
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }

  50.1% {
    -webkit-transform-origin: 50% 200%;
    transform-origin: 50% 200%;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform-origin: 50% 200%;
    transform-origin: 50% 200%;
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes grubz {
  0% {
    -webkit-transform-origin: 50% -100%;
    transform-origin: 50% -100%;
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  50% {
    -webkit-transform-origin: 50% -100%;
    transform-origin: 50% -100%;
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }

  50.1% {
    -webkit-transform-origin: 50% 200%;
    transform-origin: 50% 200%;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform-origin: 50% 200%;
    transform-origin: 50% 200%;
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes buzz-out-on-hover {
  10% {
    -webkit-transform: translateX(3px) rotate(2deg);
    transform: translateX(3px) rotate(2deg);
  }

  20% {
    -webkit-transform: translateX(-3px) rotate(-2deg);
    transform: translateX(-3px) rotate(-2deg);
  }

  30% {
    -webkit-transform: translateX(3px) rotate(2deg);
    transform: translateX(3px) rotate(2deg);
  }

  40% {
    -webkit-transform: translateX(-3px) rotate(-2deg);
    transform: translateX(-3px) rotate(-2deg);
  }

  50% {
    -webkit-transform: translateX(2px) rotate(1deg);
    transform: translateX(2px) rotate(1deg);
  }

  60% {
    -webkit-transform: translateX(-2px) rotate(-1deg);
    transform: translateX(-2px) rotate(-1deg);
  }

  70% {
    -webkit-transform: translateX(2px) rotate(1deg);
    transform: translateX(2px) rotate(1deg);
  }

  80% {
    -webkit-transform: translateX(-2px) rotate(-1deg);
    transform: translateX(-2px) rotate(-1deg);
  }

  90% {
    -webkit-transform: translateX(1px) rotate(0);
    transform: translateX(1px) rotate(0);
  }

  100% {
    -webkit-transform: translateX(-1px) rotate(0);
    transform: translateX(-1px) rotate(0);
  }
}

.blink-animation {
  font-size: 14px;
  color: var(--primary);
  animation: blinker 6s linear infinite;
  font-family: "Fredoka";
  font-weight: 500;
  text-align: center;
  display: inline-block;
  width: 100%;
}

@keyframes blinker {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.loader-bg {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgb(0, 0, 0, 0.8);
  z-index: 900;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader-bg .grubz-loader {
  position: initial !important;
}

.slick-track {
  margin-left: 0;
  margin-right: 0;
}
.popular-slider .slick-list .slick-track {
  display: flex;
}

body.filter-open {
  overflow: hidden;
}

.filter-slide-close-bg,
.add-cart-box-remove-bg,
.navigation-drawer-close-bg {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  display: none;
  background-color: rgb(0, 0, 0, 0.5);
}

.filter-slide-close-bg {
  z-index: 125;
}

.filter-open .filter-slide-close-bg,
.open-slider .navigation-drawer-close-bg {
  display: block;
}

.add-cart-box-remove-bg {
  z-index: 124;
}

.open .add-cart-box-remove-bg {
  display: block;
}

.location-search-box .ant-form-item-explain.ant-form-item-explain-error > div {
  font-size: 13px;
}

.main-loader-bg {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
  background: var(--primary);
  display: flex;
  justify-content: center;
  align-items: center;
  // animation: hideLoader 15s forwards;
}

.loading {
  width: 140px;
  height: 75px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  justify-content: space-between;
  text-align: center;
}

.loading span.wait-text {
  font-size: 16px;
  color: #fff;
  width: 100%;
  text-align: center;
}

.loading .dot img.grubz-icon {
  width: 20px;
  margin-top: -10px;
}

.loading .dot {
  font-size: 28px;
  font-weight: 800;
  color: #fff;
  animation: load 0.5s alternate infinite;
}

.loading .dot:nth-child(2) {
  animation-delay: 0.16s;
}

.loading .dot:nth-child(3) {
  animation-delay: 0.32s;
}

.loading .dot:nth-child(4) {
  animation-delay: 0.48s;
}

.loading .dot:nth-child(5) {
  animation-delay: 0.64s;
}

.loading .dot:nth-child(6) {
  animation-delay: 0.8s;
}

.loading .dot:nth-child(7) {
  animation-delay: 0.96s;
}

.loading .dot:nth-child(8) {
  animation-delay: 1.1s;
}

@keyframes hideLoader {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    display: none;
  }
}

@keyframes load {
  0% {
    transform: scaleX(1.25);
  }

  100% {
    transform: translateY(-2rem) scaleX(1);
  }
}

.view-menu-close-dropdown-bg,
.location-search-box-close-bg {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
  display: none;
}

.open-dropdown .view-menu-close-dropdown-bg,
.open-searchbar .location-search-box-close-bg {
  display: block;
}

.filter-section
  .location-search-box
  .search-for-restaurant
  .ant-form-item-control
  .ant-form-item-control-input {
  position: relative;
}

.filter-section
  .location-search-box
  .search-for-restaurant
  .ant-form-item-explain.ant-form-item-explain-error {
  width: 100%;
  margin-top: 3px;
  line-height: 14px;
}

#add-new-card-popup .form-group .ElementsApp .InputElement {
  font-family: "Visby CF" !important;
}

.contact-form-box .ant-form-item-explain.ant-form-item-explain-error > div,
.step-body-box .ant-form-item.ant-row span,
.step-body-box .ant-form-item-explain.ant-form-item-explain-error > div,
.modal-content
  .login-body
  .ant-form-item-explain.ant-form-item-explain-error
  > div {
  font-size: 16px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.restaurants-bg .map-container {
  width: 100% !important;
  height: 850px !important;
  border: 0 !important;
  border-radius: 35px;
}

.restaurants-bg .map-container > div {
  border: 0px !important;
}

.toast-top-right {
  top: 15px;
  right: 15px;
}

.toast-container {
  position: fixed;
}

.popular-slider button.slick-arrow {
  top: 50%;
  transform: translate(0, -50%);
  box-shadow: none;
  background-color: transparent;
}

.popular-slider button.slick-arrow.slick-prev {
  margin: 0;
  left: -50px;
}

.popular-slider button.slick-arrow.slick-next {
  right: -25px;
}

.popular-slider button.slick-arrow.slick-next:before,
.popular-slider button.slick-arrow.slick-prev:before {
  font-size: 32px;
  color: #1c1c1c;
}

/*---------Footer-css---------*/
.footer-bg {
  background-color: #fdf6f2;
  padding: 40px 90px 0 90px;
  border-radius: 50px;
}

.footer-box img.footer-logo {
  width: 180px;
}

.footer-box img.footer-logo:hover {
  -webkit-animation-name: buzz-out-on-hover;
  animation-name: buzz-out-on-hover;
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
}

.footer-box ul {
  margin-bottom: 0;
}

.footer-box ul li {
  margin-bottom: 15px;
}

.footer-box ul li:last-child {
  margin-bottom: 0;
}
.footer-box ul li button,
.footer-box ul li a {
  font-size: 15px;
  font-weight: 700;
  cursor: pointer;
  font-family: "Visby CF";
  text-decoration: none;
  color: #000000;
  line-height: 24px;
  letter-spacing: normal;
}
.footer-box ul li button:hover {
  color: var(--primary);
}
.footer-box ul li a img.orange-language-icon {
  display: none;
}

.footer-box ul li a:hover img.orange-language-icon {
  display: inline-block;
}

.footer-box ul li a:hover img.black-language-icon {
  display: none;
}

.footer-box ul li a:hover {
  color: var(--primary) !important;
}

.follow-us {
  text-align: center;
}

.follow-us p {
  font-family: "Visby CF";
  font-size: 12px;
  text-align: center;
  font-weight: 700;
  margin-bottom: 5px;
}

.follow-us ul {
  margin-bottom: 0;
  text-align: center;
}

.follow-us ul li {
  display: inline-block;
  margin: 0 11px;
}

.follow-us ul li a {
  font-size: 24px;
  color: #000000;
  cursor: pointer;
}
.follow-us ul li a i {
  height: 16px;
}
.follow-us ul li a:hover i {
  color: var(--primary);
}

.copy-right {
  padding-bottom: 14px;
}

.copy-right p {
  font-family: "Inter";
  font-size: 16px;
  font-weight: 700;
  line-height: 30px;
  color: #5c6574;
  margin-bottom: 0;
}
.offers-section button.slick-arrow {
  background-color: #eeebeb;
  box-shadow: 0px 0px 4px 1px #00000026;
  top: 50%;
  transform: translate(0, -50%);
  z-index: 10;
  right: 5px;
  width: 40px;
  height: 40px;
}
.offers-section button.slick-arrow.slick-prev {
  left: 5px;
}
.offers-section button.slick-arrow.slick-next:before,
.offers-section button.slick-arrow.slick-prev:before {
  font-size: 18px;
}

.open-slider button.toggle span {
  background-color: transparent !important;
}

.open-slider button.toggle::before {
  transform: rotateZ(45deg) scaleX(1.25) translate(10px, 10px);
}

.open-slider button.toggle::after {
  transform: rotateZ(-45deg) scaleX(1.25) translate(7px, -7px);
}
.ant-form-item-feedback-icon.ant-form-item-feedback-icon-success,
.ant-input-suffix
  .ant-form-item-feedback-icon.ant-form-item-feedback-icon-error {
  display: none;
}
.ant-form-item-explain .ant-form-item-explain-error {
  font-size: 16px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  color: var(--primary);
}
.enter-postcode-address .ant-form-item-explain .ant-form-item-explain-error {
  color: #fff !important;
}
.col-md-6.category-section {
  width: 33.3%;
}
.added-menu-item .col-md-6.category-section {
  width: 50%;
}

/*------RESPONSIVE CSS------*/
@media screen and (min-width: 1200px) {
  .container {
    max-width: 1310px;
  }
}

@media screen and (max-width: 1800px) {
  body {
    padding: 20px 30px;
  }

  .main-heading h2 {
    margin-bottom: 30px;
  }

  h2 {
    font-size: 76px;
    line-height: 96px;
  }

  p {
    font-size: 22px;
    color: #202020;
    line-height: 30px;
  }
}

@media screen and (max-width: 1750px) {
  .container {
    padding-left: 0;
    padding-right: 0;
  }

  .products-list-section {
    padding-left: 90px;
    padding-right: 90px;
  }

  .other-cuisines-box h6 {
    margin-bottom: 38px;
  }

  .search-for-restaurant .search-icon {
    font-size: 20px;
  }

  .search-for-restaurant input.form-control {
    width: 320px;
  }

  .product-main-box-section {
    padding-bottom: 40px;
  }

  .product-box .product-content ul.times-list li::before,
  .product-box .product-content ul.rating-list li::before {
    top: 55%;
    transform: translate(0, -55%);
  }

  .add-to-cart-box-two .add-cart-box .checkout-btn a span {
    font-size: 18px;
  }

  .add-to-cart-box-two .add-cart-box .checkout-btn a span i {
    font-size: 22px;
    margin-left: 10px;
  }

  .add-to-cart-box-two .add-cart-box .cart-top {
    padding-left: 20px;
    padding-right: 20px;
  }

  .add-to-cart-box-two .add-cart-box .cart-item {
    padding-left: 20px;
    padding-right: 10px;
  }

  .add-to-cart-box-two .add-cart-box .cart-item .cart-content ul li {
    font-size: 15px;
  }

  .add-to-cart-box-two .add-cart-box .cart-item .cart-content ul {
    margin-bottom: 0px;
  }

  .restaurant-location-box .location-details ul li a img.hygiene-rating-img {
    width: 50px;
    margin-left: 20px;
    margin-right: 5px;
  }

  .product-details-left {
    padding-top: 30px;
  }

  .product-main-box img.product-main-image {
    height: 320px;
  }

  .product-rating-section {
    padding-top: 65px;
  }

  .product-name h5 {
    font-size: 42px;
  }

  .rating-main-box {
    padding-right: 30px;
  }

  .restaurant-location-box .location-map iframe {
    height: 250px;
  }

  .restaurant-location-box .location-details {
    padding: 0 20px 0 20px;
  }

  .restaurant-location-box .location-details ul li a {
    padding: 12px 20px 12px 0;
  }

  .restaurant-location-box .location-details ul li a .arrow-right {
    top: 50%;
    transform: translate(0, -50%);
  }

  .restaurant-location-box .location-details ul li a img {
    width: 16px;
    margin-right: 5px;
  }

  .restaurant-location-box .location-details ul li a .arrow-right img {
    width: 12px;
  }

  .restaurant-location-box .location-details ul li a .arrow-right i {
    font-size: 16px;
  }

  .rating-show {
    padding-left: 40px;
  }

  .reviews-rating-box {
    padding-right: 25px;
  }

  .reviews-show .reviews-list li a {
    font-size: 22px;
  }

  .progress-box .progress {
    height: 13px;
  }

  .progress-box span {
    font-size: 14px;
    padding-right: 15px;
    color: #000;
  }

  #opening-times ul li {
    flex-wrap: wrap;
  }

  #opening-times ul li span:nth-child(3) {
    width: 100%;
    text-align: right;
  }
}

@media screen and (max-width: 1500px) {
  body {
    padding: 17px 28px;
  }

  h2 {
    font-size: 72px;
    line-height: 88px;
  }

  h4 {
    font-size: 36px;
    line-height: 38px;
  }

  h5 {
    font-size: 30px;
    line-height: 35px;
  }

  p {
    font-size: 16px;
    line-height: 18px;
  }

  .btn {
    font-size: 13px;
    padding: 4px 12px;
  }

  .main-heading h2 {
    margin-bottom: 35px;
  }

  .main-heading {
    margin-bottom: 25px;
  }

  .main-heading h2 {
    font-size: 45px;
    line-height: 55px;
  }

  .enter-postcode-address {
    max-width: 650px;
    margin-bottom: 40px;
  }

  .enter-postcode-address img.send-icon {
    width: 32px;
    top: 17px;
    left: 18px;
  }

  .enter-postcode-address input.form-control {
    font-size: 18px;
    height: 64px;
    padding: 0 180px 0 60px;
  }

  .enter-postcode-address button.btn {
    top: 9px;
    right: 10px;
    font-size: 18px;
    border-radius: 35px;
    width: 115px;
    height: 45px;
  }

  .enter-postcode-address a.close-icon {
    right: 140px;
    top: 18px;
  }

  .enter-postcode-address a.close-icon img {
    width: 28px;
  }

  .product-box {
    padding-bottom: 22px;
  }

  .product-box .product-image {
    min-height: 142px;
  }

  .product-box .product-image img.product-large-image {
    min-height: 142px;
    max-height: 142px;
  }

  .product-box .product-image .product-logo {
    width: 62px;
    height: 62px;
  }

  .product-box .product-content {
    padding-top: 12px;
  }

  .product-box .product-content h5 {
    font-size: 24px;
    line-height: 28px;
    margin-bottom: 8px;
  }

  .product-box .product-content ul.rating-list li {
    font-size: 14px;
    padding-right: 10px;
    margin-right: 10px;
  }

  .product-box .product-content ul.rating-list li img {
    width: 11px;
    margin-left: 2px;
  }

  .product-box .product-content ul.rating-list li:last-child {
    margin-right: 0;
    padding-right: 0;
  }

  .product-box .product-content ul.rating-list li:before,
  .product-box .product-content ul.times-list li:before {
    width: 3px;
    height: 3px;
  }

  .product-box .product-content ul.times-list li {
    font-size: 14px;
    padding-right: 10px;
    margin-right: 10px;
  }

  .product-box .product-content ul.times-list li:last-child {
    padding-right: 0;
    margin-right: 0;
  }

  button.slick-arrow {
    top: -28px;
    width: 22px;
    height: 22px;
    line-height: 14px;
  }

  button.slick-arrow.slick-prev {
    margin-right: 55px;
  }

  button.slick-arrow.slick-next:before,
  button.slick-arrow.slick-prev:before {
    font-size: 14px;
  }

  .inner-page {
    padding-top: 100px !important;
  }

  .modal-dialog {
    max-width: 450px;
  }

  .modal-dialog .modal-content button.btn-close {
    top: 18px;
    left: 18px;
    width: 26px;
    height: 26px;
    font-size: 15px;
  }
  .modal-dialog .modal-content button.btn-close i {
    top: 0px;
  }
  .login-title {
    font-size: 22px;
    line-height: 28px;
    margin-bottom: 15px;
  }

  .dont-have-account-text {
    font-size: 14px;
    margin-bottom: 12px;
  }

  .modal-content .login-body {
    padding: 15px 40px 25px;
  }

  .modal-dialog .modal-content .form-group {
    margin-bottom: 16px;
  }

  .modal-dialog .modal-content .form-group .form-control {
    font-size: 14px;
    padding: 5px 10px 6px;
    height: 30px;
    border-radius: 7px;
  }

  .form-group span.icon {
    top: 1px;
    right: 10px;
  }

  .form-group span.icon img {
    width: 18px;
  }

  .modal-content .login-body button.btn {
    font-size: 13px;
    padding: 2px 15px;
  }

  .or-option {
    margin: 12px 0 15px 0;
  }

  .or-option span {
    font-size: 12px;
  }

  .socials-login-options ul li {
    margin-bottom: 13px;
  }

  .socials-login-options ul li:last-child {
    margin-bottom: 0;
  }

  .socials-login-options ul li a {
    font-size: 13px;
    line-height: 30px;
    height: 30px;
  }

  .socials-login-options ul li a.google-btn img {
    width: 18px;
    margin-right: 15px;
  }

  .socials-login-options ul li a.facebook-btn img {
    width: 17px;
    margin-right: 10px;
  }

  .socials-login-options ul li a.apple-btn img {
    width: 15px;
    margin-right: 20px;
  }

  .terms-conditions {
    margin: 0 0 20px 0;
  }

  .terms-conditions p {
    font-size: 13px;
    line-height: 15px;
  }
  .add-cart-box .cart-top button.close i {
    top: 0;
  }
  .add-cart-box {
    min-width: 300px;
    max-width: 300px;
  }

  .add-cart-box .cart-top {
    padding: 15px 18px 10px;
  }

  .add-cart-box .cart-top button.close {
    width: 26px;
    height: 26px;
    padding: 1px;
    font-size: 15px;
    line-height: 26px;
    margin-bottom: 20px;
  }

  .add-cart-box .top-cart-name .cart-logo {
    width: 40px;
    height: 40px;
    line-height: 35px;
  }

  .add-cart-box .top-cart-name .cart-logo img {
    width: 30px;
  }

  .add-cart-box .top-cart-name .cart-name {
    padding-left: 15px;
  }

  .add-cart-box .top-cart-name .cart-name h6 {
    font-size: 18px;
    line-height: normal;
  }

  .add-cart-box .top-cart-name .cart-name h6 img {
    margin-left: 14px;
  }

  .add-cart-box .top-cart-name .cart-name p {
    font-size: 13px;
  }
  .add-cart-box .checkout-btn a,
  .add-cart-box .checkout-btn button {
    height: 30px;
    line-height: 25px;
    padding: 0 12px 0 12px;
  }
  .add-cart-box .checkout-btn a span,
  .add-cart-box .checkout-btn button span {
    font-size: 15px;
  }
  .add-cart-box .checkout-btn a span i,
  .add-cart-box .checkout-btn button span i {
    font-size: 18px;
    margin-left: 10px;
    margin-top: 1px;
  }

  .add-cart-box .cart-middle {
    max-height: calc(100vh - 166px);
  }

  .add-cart-box .cart-item {
    padding: 7px 18px;
  }

  .add-cart-box .cart-item .cart-image-item {
    padding-right: 12px;
  }

  .add-cart-box .cart-item .cart-image-item img {
    width: 62px;
    height: 62px;
    border-radius: 7px;
  }

  .add-cart-box .cart-item .cart-content h6 {
    font-size: 14px;
    line-height: 18px;
  }

  .add-cart-box .cart-item .cart-content ul {
    margin-bottom: 0;
  }

  .add-cart-box .cart-item .cart-content ul li {
    font-size: 14px;
    line-height: 18px;
  }

  .add-cart-box .cart-item .cart-content p {
    font-size: 14px;
    line-height: 20px;
  }

  .add-cart-box .cart-item .cart-add-item ul li {
    font-size: 12px;
    font-weight: 500;
    line-height: 18px;
  }

  .add-cart-box .cart-item .cart-add-item ul li span {
    padding: 0 7px;
  }

  .add-cart-box .cart-item .cart-add-item ul li button {
    font-size: 9px;
    font-weight: 500;
    width: 21px;
    height: 21px;
    line-height: 21px;
  }

  .add-to-cart-box-two .add-cart-box {
    min-width: 300px;
    max-width: 300px;
  }

  .add-to-cart-box-two .add-cart-box .cart-item {
    padding-left: 18px;
  }

  .add-to-cart-box-two {
    // top: 95px;
    // height: calc(100vh - 95px);
    top: 0px;
    height: 100vh;
  }

  .add-to-cart-box-two .add-cart-box .cart-middle {
    max-height: calc(100vh - 215px);
  }

  .add-to-cart-box-two .add-cart-box .checkout-btn a span {
    font-size: 15px;
  }

  .add-to-cart-box-two .add-cart-box .cart-top {
    padding: 13px 18px 10px 18px;
  }

  .product-main-box-section {
    padding: 0px 92px 40px 92px;
  }

  .product-details-left {
    max-width: 100%;
  }
  .added-menu-item .product-details-left {
    max-width: calc(100% - 300px);
    padding-right: 30px;
  }
  .product-main-box img.product-main-image {
    height: 260px;
  }

  .product-main-box .product-logo {
    padding: 0 5px;
    width: 90px;
    height: 90px;
    line-height: 85px;
    left: 30px;
    bottom: -30px;
  }

  .product-main-box .product-logo img {
    border-radius: 50%;
  }

  .product-rating-section {
    padding-top: 60px;
  }

  .product-name h5 {
    font-size: 34px;
    margin-bottom: 16px;
  }

  .product-rating {
    margin-bottom: 6px;
  }

  .product-rating ul li {
    font-size: 12px;
    padding-right: 10px;
    margin-right: 12px;
  }

  .product-rating ul li:before {
    height: 20px;
  }

  .product-rating ul li span {
    font-size: 19px;
  }

  .product-rating ul li .fa-star {
    font-size: 19px;
    width: 20px;
  }

  .open-close {
    margin-bottom: 32px;
  }

  .open-close ul li {
    font-size: 12px;
  }

  .open-close ul li i {
    position: relative;
    top: 1px;
    margin-right: 3px;
  }

  .open-close ul li .fa-circle {
    top: -2px;
    font-size: 3px;
    margin-left: 12px;
    margin-right: 12px;
  }

  .reviews-rating-main h6 {
    font-size: 22px;
    margin-bottom: 20px;
  }

  .reviews-rating-box {
    padding: 22px 25px 10px 25px;
    border-radius: 15px;
  }

  .reviews-rating-detail {
    margin-bottom: 20px;
  }

  .rating-show {
    padding-left: 45px;
  }

  .reviews-show h4 {
    font-size: 45px;
    line-height: 38px;
    margin-bottom: 10px;
  }

  .reviews-show .reviews-list li a {
    font-size: 18px;
  }

  .reviews-show .reviews-list {
    margin-bottom: 2px;
  }

  .reviews-show span {
    font-size: 12px;
    color: #8f8f8a;
    font-weight: 700;
  }

  .progress-box {
    margin-bottom: 1px;
  }

  .progress-box span {
    font-size: 12px;
    color: #000;
  }

  .progress-box .progress {
    height: 11px;
  }

  .rating-list .rating-box {
    padding-top: 12px;
    padding-bottom: 12px;
  }

  .rating-list .rating-box p {
    font-size: 14px;
  }

  .rating-list .rating-box .rating-details .rating-star {
    line-height: normal;
    margin-right: 8px;
  }

  .rating-list .rating-box .rating-details .rating-star li a {
    font-size: 10px;
  }

  .rating-list .rating-box .rating-details span {
    font-size: 10px;
    padding-top: 5px;
  }

  .rating-list .rating-box .rating-details .fa-circle {
    top: 4px;
    font-size: 3px;
    margin-left: 8px;
    margin-right: 8px;
  }

  .rating-main-box {
    padding-right: 38px;
  }

  .restaurant-location-box {
    border-radius: 15px;
    margin-bottom: 12px;
  }

  .restaurant-location-box .location-map iframe {
    height: 210px;
    border-radius: 15px 15px 0 0;
  }

  .restaurant-location-box .location-details {
    padding: 0 15px;
  }

  .restaurant-location-box .location-details ul li a {
    font-size: 12px;
    padding: 12px 20px 12px 5px;
  }

  .restaurant-location-box .location-details ul li a img {
    width: 15px;
    margin-right: 10px;
  }

  .restaurant-location-box .location-details ul li a .arrow-right {
    top: 40%;
    transform: translateY(-40%);
  }

  .restaurant-location-box .location-details ul li a .arrow-right i {
    font-size: 13px;
  }

  .restaurant-location-box .location-details ul li a .arrow-right img {
    width: 10px;
  }

  .add-to-product {
    margin-bottom: 15px;
  }

  .add-to-product ul li {
    line-height: 24px;
  }

  .add-to-product ul li a {
    font-size: 10px;
    width: 24px;
    height: 24px;
    line-height: 24px;
  }

  .add-to-product ul li span {
    font-size: 14px;
    padding: 0 10px;
  }

  .modal-dialog .modal-content {
    border-radius: 15px;
  }

  .modal-footer button.btn {
    height: 36px;
    margin: 0 15px;
    line-height: 14px;
  }

  .checkout-page {
    padding-top: 78px;
    padding-left: 92px;
    padding-right: 92px;
  }

  .add-to-cart-box-two .add-cart-box .cart-top .checkout-btn button.btn {
    font-size: 14px;
    font-weight: 800;
    padding: 2px 10px;
  }

  .checkout-section {
    justify-content: center;
  }

  .checkout-section .login-box {
    max-width: 750px;
  }

  .subtotal-price-box {
    width: 188px;
  }

  .subtotal-box {
    padding: 13px 0 8px 0;
  }

  .subtotal-box ul {
    padding-left: 10px;
    padding-right: 10px;
  }

  .subtotal-box ul li:first-child span {
    font-size: 12px;
    padding-bottom: 10px;
  }

  .subtotal-box ul li span {
    font-size: 10px;
  }

  .subtotal-box ul.add-promo-code li {
    margin-bottom: 9px;
  }

  .promo-code-box {
    padding: 5px 13px;
  }

  .promo-code-box h6 {
    font-size: 15px;
    margin-bottom: 13px;
  }

  .promo-code-box h6 img {
    width: 15px;
    margin-left: 14px;
  }

  .promo-code-box input.form-control {
    font-size: 9px;
    height: 30px;
    padding: 0 10px;
  }

  .promo-code-box .error-text {
    font-size: 9px;
    font-weight: 700;
  }

  .promo-code-box button.btn {
    font-size: 8px;
    width: 64px;
    height: 19px;
    line-height: 10px;
    margin-top: 18px;
  }

  .checkout-section .login-box {
    padding-top: 12px;
    padding-right: 25px;
  }

  .pagination li.page-item a.page-link {
    font-size: 14px;
  }

  .ant-form-item-explain.ant-form-item-explain-error {
    font-size: 13px;
  }

  .dont-have-order p {
    font-size: 20px;
  }

  .contact-form-box .ant-form-item-explain.ant-form-item-explain-error > div,
  .step-body-box .ant-form-item-explain.ant-form-item-explain-error > div,
  .step-body-box .ant-form-item.ant-row span,
  .modal-content
    .login-body
    .ant-form-item-explain.ant-form-item-explain-error
    > div {
    font-size: 13px;
  }

  .restaurants-bg .map-container {
    height: 615px !important;
    border-radius: 26px;
  }

  .popular-slider button.slick-arrow.slick-next:before,
  .popular-slider button.slick-arrow.slick-prev:before {
    font-size: 26px;
    line-height: 12px;
  }

  .footer-bg {
    padding: 30px 92px 0;
    border-radius: 30px;
  }

  .footer-box img.footer-logo {
    width: 135px;
  }

  .footer-box ul li {
    margin-bottom: 5px;
  }
  .footer-box ul li button,
  .footer-box ul li a {
    font-size: 12px;
  }

  .follow-us p {
    font-size: 9px;
    margin-bottom: 2px;
  }

  .follow-us ul li {
    margin: 0 9px;
  }

  .follow-us ul li a {
    font-size: 18px;
  }

  .copy-right p {
    font-size: 12px;
  }
  .offers-section button.slick-arrow {
    width: 40px;
    height: 40px;
    line-height: 18px;
  }
  .ant-form-item-explain .ant-form-item-explain-error {
    font-size: 13px;
  }
}

@media screen and (max-width: 1399px) {
  .search-for-restaurant input.form-control {
    width: 300px;
  }
}

@media screen and (max-width: 1330px) {
  .open-close {
    margin-bottom: 22px;
  }

  .popular-slider button.slick-arrow.slick-prev {
    left: -30px;
  }

  .popular-slider button.slick-arrow.slick-next {
    right: -30px;
  }
}

@media screen and (max-width: 1300px) {
  .enter-postcode-address {
    max-width: 550px;
  }

  .enter-postcode-address input.form-control {
    font-size: 16px;
    height: 60px;
    padding: 0 165px 0 60px;
  }

  .enter-postcode-address img.send-icon {
    width: 28px;
  }

  .enter-postcode-address a.close-icon {
    right: 130px;
    top: 17px;
  }

  .enter-postcode-address a.close-icon img {
    width: 24px;
  }

  .enter-postcode-address button.btn {
    font-size: 16px;
    width: 105px;
    height: 42px;
  }

  .products-list-section {
    padding-left: 70px;
    padding-right: 70px;
  }

  .popular-cuisines-card .popular-cuisines-content {
    padding: 10px 0 0 25px;
  }

  .popular-cuisines-card .popular-cuisines-content ul li {
    font-size: 12px;
  }

  .popular-cuisines-card .popular-cuisines-content ul li:first-child {
    width: 45%;
  }

  .location-box {
    margin-bottom: 25px;
  }

  .other-cuisines-box h6 {
    margin-bottom: 28px;
  }

  .location-box ul {
    align-items: center;
  }

  .location-box {
    padding: 7px 15px 7px 15px;
  }

  .location-box ul li .fa-location-dot {
    top: 1px;
    font-size: 20px;
    margin-right: 10px;
  }

  .location-box ul li.dot {
    margin: 0 10px;
  }

  .search-for-restaurant .search-icon {
    font-size: 18px;
  }

  .search-for-restaurant button.btn {
    right: 6px;
    width: 95px;
    font-size: 16px;
  }

  .popular-cuisines-card .popular-cuisines-content h5 {
    font-size: 32px;
  }

  .search-filter-box {
    margin-bottom: 30px;
  }

  .popular-cuisines-card .popular-cuisines-image {
    max-width: 236px;
  }

  .popular-cuisines-card .popular-cuisines-image img {
    width: 100%;
  }

  .popular-cuisines-card .popular-cuisines-content {
    padding: 5px 0 0 25px;
  }

  .rating-star-box {
    padding-top: 5px;
  }

  .restaurant-location-box .location-details ul li a img.hygiene-rating-img {
    width: 30px;
    margin-left: 10px;
  }

  .rating-main-box {
    padding-right: 0px;
  }

  .product-box .product-content ul.rating-list li {
    font-size: 13px;
    padding-right: 8px;
    margin-right: 8px;
  }

  .product-box .product-content ul.rating-list li:last-child {
    padding-right: 0;
    margin-right: 0;
  }

  .product-box .product-content ul.times-list li {
    font-size: 13px;
    padding-right: 8px;
    margin-right: 8px;
  }

  .product-box .product-content ul.times-list li:last-child {
    padding-right: 0;
    margin-right: 0;
  }

  #opening-times ul li span {
    font-size: 12px;
  }

  #opening-times ul li span:nth-child(2) {
    width: auto;
  }

  #opening-times ul li span:first-child {
    width: auto;
  }
  .col-md-6.category-section {
    width: 50%;
  }
}

@media screen and (max-width: 1199px) {
  html {
    overflow-x: hidden;
  }
  body {
    padding: 15px 20px;
  }

  h2 {
    font-size: 55px;
    line-height: 65px;
  }

  .main-heading {
    margin-bottom: 30px;
  }

  .products-list-section {
    padding-left: 40px;
    padding-right: 40px;
  }

  body.open {
    overflow: hidden;
  }

  .popular-slider button.slick-arrow.slick-prev {
    left: -40px;
  }

  .other-cuisines-box {
    position: fixed;
    background: #fff;
    z-index: 123;
    min-width: 320px;
    max-width: 320px;
    padding: 20px;
    box-shadow: 0 0 5px 0 #e1e1e1;
    left: -100%;
    transition: all 0.5s;
    top: 0;
    bottom: 0;
    overflow: hidden;
    overflow-y: auto;
    visibility: hidden;
    opacity: 0;
  }

  .other-cuisines-box::-webkit-scrollbar {
    width: 5px;
  }

  .other-cuisines-box::-webkit-scrollbar-track {
    background: #ffe6e7;
  }

  .other-cuisines-box::-webkit-scrollbar-thumb {
    background: var(--primary);
  }

  .other-cuisines-box::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
  }

  .other-cuisines-box.open {
    left: 0;
    visibility: visible;
    opacity: 1;
  }

  .other-cuisines-box button.other-cuisines-btn {
    color: var(--primary);
    font-size: 28px;
    line-height: normal;
    background-color: transparent;
    border: 0;
    outline: none;
    box-shadow: none;
    margin-bottom: 30px;
  }

  .top-shorting-bar .search-filter-box {
    padding-left: 15px;
  }

  .top-shorting-bar .location-box {
    max-width: 250px;
  }

  .top-shorting-bar .filter-box {
    padding-left: 15px;
  }

  .top-shorting-bar .filter-box p {
    font-size: 18px;
  }

  .top-shorting-bar .search-filter-box .search-for-restaurant {
    max-width: 100%;
  }

  .other-cuisines-box .search-for-restaurant input.form-control {
    height: 50px;
    padding-left: 45px;
    padding-right: 105px;
  }

  .other-cuisines-box .search-for-restaurant .search-icon {
    left: 18px;
  }

  .other-cuisines-box .search-for-restaurant button.btn {
    right: 5px;
    width: 90px;
    padding: 8px 15px;
  }

  .inner-page {
    padding-bottom: 30px;
  }

  .product-box .product-content ul.rating-list li {
    font-size: 14px;
  }

  .product-box .product-content ul.times-list li {
    font-size: 13px;
  }

  .add-cart-box .cart-middle {
    max-height: calc(100vh - 168px);
  }

  .header-two .header-right ul li a {
    font-size: 16px;
    padding: 6px 25px;
  }

  .header-middle-box .search-for-restaurant input.form-control {
    width: 200px;
  }

  .rating-main-box {
    padding-right: 0px;
  }

  .checkout-section {
    flex-wrap: wrap;
  }

  .checkout-section .login-box {
    max-width: 100%;
    padding-right: 0;
  }

  .add-to-cart-box-two {
    width: 100%;
    max-height: 500px;
    height: auto;
    overflow: hidden;
  }

  .add-to-cart-box-two > div {
    justify-content: center;
  }

  .product-main-box-section {
    padding: 0px 40px 40px 40px;
  }

  .product-details-left {
    max-width: 100%;
    float: none;
    padding-top: 30px;
    padding-right: 0;
  }

  .add-to-cart-box-two .add-cart-box .cart-middle {
    max-height: 422px;
  }

  .subtotal-price-box {
    border-top: 2px solid #f4f3f3;
  }

  .restaurants-bg .map-container {
    height: 450px !important;
  }

  .checkout-page {
    padding-left: 45px;
    padding-right: 45px;
  }

  .footer-bg {
    padding: 30px 50px 0 50px;
  }

  .footer-box ul li {
    margin-bottom: 12px;
  }

  .follow-us ul li {
    margin: 0 6px;
  }

  .copy-right {
    padding-top: 10px;
  }
  .added-menu-item .product-details-left {
    max-width: 100%;
    padding-right: 0px;
  }
}

@media screen and (max-width: 991px) {
  h2 br {
    display: none;
  }

  p br {
    display: none;
  }

  .popular-slider button.slick-arrow.slick-next {
    right: -40px;
  }

  .enter-postcode-address {
    margin: auto;
  }

  .enter-postcode-address input.form-control {
    height: 56px;
  }

  .enter-postcode-address img.send-icon {
    width: 26px;
    top: 16px;
  }

  .enter-postcode-address input.form-control {
    height: 56px;
    padding: 0 165px 0 60px;
  }

  .enter-postcode-address a.close-icon {
    right: 130px;
    top: 14px;
  }

  .enter-postcode-address button.btn {
    font-size: 16px;
    width: 100px;
    height: 40px;
    top: 8px;
  }

  .products-list-section {
    padding-left: 30px;
    padding-right: 30px;
  }

  .header-middle-box .search-for-restaurant {
    display: none;
  }

  body.filter-open .filter-slide {
    left: 0;
    visibility: visible;
    opacity: 1;
  }

  .modal-content .modal-header {
    padding: 10px 34px;
    position: relative;
  }

  .rating-main-box {
    margin-bottom: 30px;
  }

  .product-main-box-section {
    padding: 0px 30px 30px 30px;
  }

  .restaurants-bg .map-container {
    height: 400px !important;
    border-radius: 30px;
  }

  .footer-bg {
    border-radius: 30px;
  }

  .footer-box img.footer-logo {
    margin-bottom: 30px;
  }
}

@media screen and (max-width: 767px) {
  .popular-slider button.slick-arrow.slick-prev {
    left: -25px;
  }

  .popular-slider button.slick-arrow.slick-next {
    right: -25px;
  }

  .popular-slider button.slick-arrow.slick-next:before,
  .popular-slider button.slick-arrow.slick-prev:before {
    font-size: 20px;
    line-height: 14px;
  }

  .product-main-box-section {
    padding: 0px 10px 10px 10px;
  }

  .inner-page {
    padding-top: 90px !important;
  }

  .checkout-page {
    padding-top: 66px;
    padding-left: 0;
    padding-right: 0;
  }

  .search-filter-box .search-for-restaurant {
    max-width: 100%;
    width: 100%;
  }

  .search-for-restaurant button.btn {
    right: 5px;
  }

  .location-box {
    padding: 14px 24px 14px 24px;
    max-width: 100%;
  }

  .modal-content .modal-header {
    padding: 10px 30px;
  }

  .add-cart-box .cart-middle {
    max-height: calc(100vh - 185px);
  }

  .header-two .header-right ul li a.basket-toggle-btn {
    width: 70px;
    padding: 3px 10px;
  }

  .restaurants-bg .map-container {
    height: 350px !important;
    border-radius: 30px;
  }

  .enter-postcode-address input.form-control::placeholder {
    max-width: 11ch;
  }

  .footer-bg {
    padding: 40px 30px 0 30px;
  }

  .footer-box {
    text-align: center;
    margin-bottom: 30px;
  }

  .footer-box img.footer-logo {
    margin-bottom: 0px;
  }

  .follow-us {
    width: 100%;
    margin-bottom: 15px;
  }

  .copy-right {
    text-align: center;
  }

  .col-md-6.category-section {
    width: 100%;
  }
  .added-menu-item .col-md-6.category-section {
    width: 100%;
  }
}

@media screen and (max-width: 575px) {
  p {
    font-size: 18px;
    line-height: 20px;
  }

  h2 {
    font-size: 52px;
  }

  .main-heading {
    margin-bottom: 25px;
  }

  .main-heading h2 {
    margin-bottom: 20px;
  }

  .add-to-cart-box-two {
    height: auto;
    max-height: 100%;
    position: initial;
  }

  .modal-dialog {
    margin: 20px auto;
  }

  .modal-content .login-body {
    padding: 20px 20px;
  }

  .modal-dialog .modal-content button.btn-close {
    top: 20px;
    left: 20px;
  }

  .product-box {
    padding-bottom: 20px;
  }

  .header-two button.toggle {
    margin-right: 15px;
  }

  .product-box .product-image .product-logo img.peri-peri-logo {
    height: 49px;
  }

  .product-rating-section {
    padding-top: 50px;
  }

  .reviews-rating-detail {
    flex-wrap: wrap;
  }

  .reviews-rating-detail .reviews-show {
    width: 100%;
    margin-bottom: 20px;
  }

  .reviews-rating-detail .rating-show {
    padding: 0;
  }

  .add-to-cart-box-two > div {
    flex-wrap: wrap;
  }

  .add-to-cart-box-two .add-cart-box {
    max-width: 100%;
    width: 100%;
    height: auto;
    margin-bottom: 10px;
    z-index: 100;
  }

  .add-to-cart-box-two .add-cart-box .cart-middle {
    min-height: 200px;
    max-height: 345px;
  }

  .subtotal-price-box {
    width: 100%;
  }

  .add-to-cart-box-two .add-cart-box .cart-top {
    border-right: 2px solid #f4f3f3;
  }

  .promo-code-box,
  .subtotal-box {
    border-left: 2px solid#F4F3F3;
  }

  .product-main-box .product-logo {
    width: 80px;
    height: 80px;
    line-height: 75px;
    left: 20px;
    bottom: -25px;
  }

  .subtotal-box ul li:first-child span {
    font-size: 14px;
    padding-bottom: 10px;
  }

  .subtotal-box ul li span {
    font-size: 12px;
  }

  .subtotal-box ul li .form-check input.form-check-input {
    margin-top: 4px;
  }

  .promo-code-box input.form-control {
    font-size: 12px;
    padding: 0 14px;
  }

  .promo-code-box button.btn {
    font-size: 10px;
    height: 22px;
    line-height: 10px;
  }

  .dont-have-order p {
    font-size: 18px;
  }

  .suggested-items-box {
    border-left: 2px solid #f4f3f3;
    max-height: 380px;
    overflow: hidden;
    overflow-y: auto;
  }

  .suggested-items-box::-webkit-scrollbar {
    width: 5px;
  }

  .suggested-items-box::-webkit-scrollbar-track {
    background: #ffe6e7;
    border-radius: 10px;
  }

  .suggested-items-box::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 10px;
  }

  .suggested-items-box::-webkit-scrollbar-thumb:hover {
    background: var(--primary);
  }

  .suggested-items-box {
    max-height: 380px;
  }
}

@media screen and (max-width: 480px) {
  body {
    padding: 12px;
  }

  h2 {
    font-size: 52px;
    line-height: 48px;
  }

  .main-heading h2 {
    font-size: 42px;
    line-height: 48px;
  }

  h4 {
    font-size: 34px;
    line-height: 45px;
  }

  p {
    font-size: 16px;
  }
  .offers-section button.slick-arrow {
    width: 30px;
    height: 30px;
    line-height: 14px;
    right: 15px;
  }
  .offers-section button.slick-arrow.slick-prev {
    left: 15px;
  }
  .offers-section button.slick-arrow.slick-next:before,
  .offers-section button.slick-arrow.slick-prev:before {
    font-size: 14px;
  }
  .enter-postcode-address input.form-control {
    font-size: 14px;
    height: 45px;
    padding: 0 110px 0 45px;
  }

  .enter-postcode-address img.send-icon {
    width: 20px;
    top: 14px;
    left: 14px;
  }

  .enter-postcode-address button.btn {
    font-size: 14px;
    top: 6px;
    right: 7px;
    width: 75px;
    height: 34px;
  }

  .enter-postcode-address a.close-icon {
    right: 85px;
    top: 9px;
  }

  .enter-postcode-address a.close-icon img {
    width: 18px;
  }

  .view-all-cities-bg .main-heading {
    margin-bottom: 30px;
  }

  .products-list-section {
    padding-left: 20px;
    padding-right: 20px;
  }

  .popular-slider button.slick-arrow.slick-prev {
    left: -22px;
  }

  .popular-slider button.slick-arrow.slick-next {
    right: -22px;
  }

  .popular-slider button.slick-arrow.slick-next:before,
  .popular-slider button.slick-arrow.slick-prev:before {
    font-size: 18px;
  }

  .search-for-restaurant input.form-control {
    padding-left: 44px;
    padding-right: 100px;
  }

  .search-for-restaurant .search-icon {
    font-size: 16px;
    left: 20px;
  }

  .search-for-restaurant button.btn {
    width: 85px;
    padding: 8px 15px;
    font-size: 15px;
  }

  .other-cuisines-box {
    min-width: 280px;
    max-width: 280px;
  }

  .other-cuisines-box button.other-cuisines-btn {
    margin-bottom: 25px;
  }

  .other-cuisines-box h6 {
    margin-bottom: 20px;
  }

  .location-box {
    padding: 13px 20px 13px 20px;
  }

  .search-for-restaurant .search-icon {
    font-size: 15px;
    left: 15px;
  }

  .search-for-restaurant input.form-control {
    font-size: 14px;
    padding-left: 40px;
    padding-right: 85px;
  }

  .search-for-restaurant button.btn {
    width: 70px;
    font-weight: 500;
    padding: 5px 5px;
    font-size: 14px;
  }

  .search-filter-box {
    margin-bottom: 20px;
  }

  .other-cuisines-box .search-for-restaurant .search-icon {
    left: 16px;
  }

  .other-cuisines-box .search-for-restaurant input.form-control {
    font-size: 14px;
    height: 48px;
    padding-left: 40px;
    padding-right: 95px;
  }

  .other-cuisines-box .search-for-restaurant button.btn {
    width: 80px;
    padding: 6px 15px;
  }

  .product-box .product-content {
    padding-top: 5px;
  }

  .product-box .product-content h5 {
    font-size: 24px;
  }

  .modal-content .modal-header {
    padding: 7px 20px;
  }

  .product-name h5 {
    font-size: 32px;
    margin-bottom: 10px;
  }

  .order-place-btn span {
    font-size: 16px;
  }

  .order-place-btn button.btn i {
    font-size: 18px;
  }

  .product-main-box img.product-main-image {
    height: 240px;
    border-radius: 15px;
  }

  .product-main-box .product-logo {
    width: 60px;
    height: 60px;
    line-height: 55px;
    left: 15px;
    bottom: -30px;
  }

  .product-details-left {
    padding-top: 20px;
  }

  .dont-have-order p {
    font-size: 16px;
  }

  .modal-dialog {
    max-width: calc(100% - 20px);
  }

  .ant-form-item-explain.ant-form-item-explain-error {
    font-size: 12px;
    line-height: 16px;
  }

  .restaurants-bg .map-container {
    height: 300px !important;
    border-radius: 20px;
  }

  .footer-bg {
    padding: 35px 20px 0 20px;
    border-radius: 20px;
  }
}

@media screen and (max-width: 400px) {
  .add-cart-box .cart-item .cart-content ul li {
    font-size: 10px;
    margin-left: 5px;
    padding-left: 5px;
  }

  .add-cart-box .cart-item .cart-content ul li::before {
    top: 42%;
    left: -4px;
  }

  .add-to-cart-box-two .add-cart-box {
    min-width: 300px;
  }

  .checkout-page {
    padding-top: 55px;
  }

  .add-to-cart-box-two .add-cart-box .checkout-btn a span {
    font-size: 15px;
  }

  .add-to-cart-box-two .add-cart-box .checkout-btn a span i {
    font-size: 18px;
  }

  .socials-login-options ul li {
    margin-bottom: 10px;
  }

  .socials-login-options ul li a {
    font-size: 14px;
  }

  .socials-login-options ul li a.google-btn img {
    margin-right: 10px;
  }

  .socials-login-options ul li a.facebook-btn img {
    margin-right: 10px;
  }

  .socials-login-options ul li a.apple-btn img {
    margin-right: 10px;
  }

  .form-control {
    font-size: 16px;
  }

  .next-btn button.btn {
    font-size: 22px;
    padding-left: 15px;
    padding-right: 15px;
    height: 42px;
  }

  .order-place-btn span {
    font-size: 14px;
  }

  .order-place-btn button.btn i {
    font-size: 16px;
    margin-left: 2px;
  }

  .inner-page {
    padding-top: 80px !important;
  }

  .product-main-box-section {
    padding-top: 0px;
  }
}

// New CSS
::ng-deep .rounded-modal > .modal-dialog > .modal-content {
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.delete-cart-item-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 1px solid #ddd;
  color: #ff0000;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;

  &:hover {
    background: #f5f5f5;
  }
}
