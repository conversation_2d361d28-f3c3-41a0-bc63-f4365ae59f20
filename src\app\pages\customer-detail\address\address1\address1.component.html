<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-12 d-flex pb-3">
      <h4 class="col-6">Address Book</h4>
      <div class="col-6 text-end">
        <div class="btn bg-primary text-white w-auto cursor" (click)=" addAddress(addressModal)">
          Add Address
        </div>
      </div>
    </div>

    <div class="col-md-6 col-xs-12 mb-2" *ngFor=" let addressBook of addressBooks;let i = index;">
      <div class="col-12 d-flex border border body-box-shadow rounded p-2">
        <img src="./assets/address.png" class="px-1 pt-2" width="24px" height="24px">
        <span class="fw-bold col-8 px-2">
          {{addressBook.title}}
          <p class="text-muted">{{addressBook.flat_no}},{{addressBook.address}},{{addressBook.zipcode}}</p>
        </span>
        <span class="fw-bold col-3 text-end cursor">
          <span (click)="deleteAddress(addressBook)">Delete</span>
        </span>
      </div>
    </div>

    <div class="col-md-12 empty-cart-cls text-center" *ngIf="addressBooks.length <= 0">
      <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
      <p><strong>No address(s) Found</strong></p>
    </div>

  </div>
</div>

<ng-template #addressModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Add New Deliver Address
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">PostCode</label>
      <div class="col-sm-12 col-xs-12 d-flex justify-content-between">
        <input type="text" class="form-control col-md-5 col-xs-12 w-50" id="zipcode" name="zipcode"
          [(ngModel)]="addressBookAdd.zipcode" required />

        <a class="btn bg-primary col-md-5 col-xs-12 text-white align-items-right cursor" [disabled]="isPostcodeLoading"
          style="width:4cm;" (click)="findzipcode(addressBookAdd.zipcode)">
          <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isPostcodeLoading"
            role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          Find Address
        </a>
      </div>
      <div *ngIf="Postcodeerror">
        <span class="text-danger">{{ Postcodeerror }}</span>
      </div>
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Address Title</label>
      <input type="text" class="form-control col-md-8" id="title" name="title" [(ngModel)]="addressBookAdd.title" />
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Door no / Flat no</label>
      <input type="text" class="form-control col-md-8" id="flat_no" name="flat_no" [(ngModel)]="addressBookAdd.flat_no"
        required />
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Address</label>
      <input type="text" class="form-control col-md-8" id="address" name="address" [(ngModel)]="addressBookAdd.address"
        required />
    </div>
    <input type="hidden" class="form-control col-md-8" id="latitude" name="latitude"
      [(ngModel)]="addressBookAdd.latitude" />
    <input type="hidden" class="form-control col-md-8" id="longitude" name="longitude"
      [(ngModel)]="addressBookAdd.longitude" />
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isModelLoading" class="btn btn-outline-dark bg-primary cursor text-white"
      (click)="validateAddress(addressModal)">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Add Address
    </button>
  </div>
</ng-template>