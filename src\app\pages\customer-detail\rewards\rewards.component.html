<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">
    <h4>Reward Points <span class="fs-6">Validity Up to {{rewardValidity}} Days</span></h4>
    <div class="col-sm-12 col-xs-12 mt-2 body-box-shadow p-0" style="overflow-x: auto;" *ngIf="rewards.length > 0">
      <table class="fees-table w-100">
        <tr>
          <th>id</th>
          <th>Order id</th>
          <th>Restaurant Name</th>
          <th>Total</th>
          <th>Point</th>
          <th>Type</th>
          <th>Date</th>
        </tr>
        <tr *ngFor="let item of rewards">
          <td>{{item.id}}</td>
          <td>{{item.order_id}}</td>
          <td>{{item.restaurant_name}}</td>
          <td>{{item.total}}</td>
          <td>
            {{(item.type == 'Spent'?item.reward_totalpoint:item.points)}}
            <span *ngIf="item.reward_offer">({{convertNumber(item.reward_offer)}})</span>
          </td>
          <td>{{item.type}}</td>
          <td>{{convertToDate(item.created)}}</td>
        </tr>
      </table>
    </div>
    <app-pagination *ngIf="rewards.length > 0" [total_items]="totalRewards" class="pt1 mt-1"
      [per_page]="options.per_page" [(current_page)]="options.page" (onChange)="loadPage($event)">
    </app-pagination>
    <div class="col-md-12 empty-cart-cls text-center py-5" *ngIf="rewards.length <= 0">
      <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
      <p><strong>No reward(s) Found</strong></p>
    </div>
  </div>
</div>


<ng-template #bookingModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Booking Details
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-6">Booking ID :</label>
      <label class="col-md-6">{{booking.booking_id}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Restaurant Name :</label>
      <label class="col-sm-6">{{restaurant.restaurant_name}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Guest :</label>
      <label class="col-sm-6">{{booking.guest_count}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Booking Date :</label>
      <label class="col-sm-6">{{booking.booking_date}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Booking Time :</label>
      <label class="col-sm-6">{{booking.booking_time}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2" *ngIf="booking.booking_instruction">
      <label class="col-sm-6">Instruction :</label>
      <label class="col-sm-6">{{booking.booking_instruction}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Place Date :</label>
      <label class="col-sm-6">{{convertToDate(booking.created)}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Booking Status :</label>
      <label class="col-sm-6">{{booking.status | titlecase}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2" *ngIf="booking.cancel_reason">
      <label class="col-sm-6">Cancel Reason :</label>
      <label class="col-sm-6">{{booking.cancel_reason}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Customer Name :</label>
      <label class="col-sm-6">{{booking.customer_name}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Customer Email :</label>
      <label class="col-sm-6">{{booking.booking_email}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Customer Phone Number :</label>
      <label class="col-sm-6">{{booking.booking_phone}}</label>
    </div>
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
</ng-template>