/* You can add global styles to this file, and also import other style files */
$primary: #f57724;
// $primary: #000000;
// $primary: #ad2b30;
// $primary: #fbba1b;
// $primary: #ec5b08;

:root {
  --primary: #f57724;
  // --primary: #000000;
  // --primary: #ad2b30;
  // --primary: #fbba1b;
  // --primary: #ec5b08;
}

$alert-padding-y: 10px;
$alert-padding-x: 10px;
@import "bootstrap";

.cursor {
  cursor: pointer;
}

.body-box-shadow {
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%), 0 8px 16px rgb(0 0 0 / 10%);
}

::ng-deep ng-otp-input input {
  border-radius: 0.5rem !important;
  font-size: 1.25rem !important;
  font-weight: 500 !important;
  width: 3rem !important;
  height: 3rem !important;

  @media (max-width: 576px) {
    border-radius: 0.5rem !important;
    font-size: 1.25rem !important;
    font-weight: 500 !important;
    width: 2.25rem !important;
    height: 2.25rem !important;
  }
}
