<ng-container *ngIf="!userService.userThemeLoading; else themeLoading">
  <ng-container [ngSwitch]="menuTheme">
    <app-profile1 *ngSwitchCase="'theme1'"></app-profile1>
    <app-profile2 *ngSwitchCase="'theme2'"></app-profile2>
    <app-profile2 *ngSwitchCase="'theme3'"></app-profile2>

    <!-- Optional: fallback -->
    <div *ngSwitchDefault>
      <app-profile1></app-profile1>
    </div>
  </ng-container>
</ng-container>

<ng-template #themeLoading>
  <div class="d-flex justify-content-center align-items-center w-100 h-100" style="min-height: 80vh;">
    <div class="cart-loader">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  </div>
</ng-template>