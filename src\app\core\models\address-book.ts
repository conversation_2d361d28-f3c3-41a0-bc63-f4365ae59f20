export class AddressBook {
  id: string;
  user_id: string;
  restaurant_id: string;

  title: string = 'Home';
  flat_no: string;
  address: string;
  latitude: string;
  longitude: string;
  zipcode: string;

  to_distance: string;
  deliveryCharge: number;
  deliveryMin: number;
  delivery_status: string;

  status: boolean = true;

  created: string;
  updated: string;

  static toFormData(address_book: AddressBook) {
    const formData = new FormData();

    if (address_book.id) formData.append('id', address_book.id);
    if (address_book.user_id) formData.append('user_id', address_book.user_id);
    if (address_book.title) formData.append('title', address_book.title);
    if (address_book.flat_no) formData.append('flat_no', address_book.flat_no);
    if (address_book.address) formData.append('address', address_book.address);
    if (address_book.latitude) formData.append('latitude', address_book.latitude);
    if (address_book.longitude) formData.append('longitude', address_book.longitude);
    if (address_book.zipcode) formData.append('zipcode', address_book.zipcode);
    formData.append('status', address_book.status ? '1' : '0');

    return formData;
  }
}
