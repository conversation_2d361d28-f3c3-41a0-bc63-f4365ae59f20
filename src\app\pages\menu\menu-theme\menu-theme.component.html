<ng-container *ngIf="!userService.userThemeLoading; else themeLoading">
  <ng-container [ngSwitch]="menuTheme">
    <app-menu *ngSwitchCase="'theme1'"></app-menu>
    <app-menu2 *ngSwitchCase="'theme2'"></app-menu2>
    <app-menu3 *ngSwitchCase="'theme3'"></app-menu3>
    <!-- Optional: fallback -->
    <div *ngSwitchDefault>
      <app-menu></app-menu>
    </div>
  </ng-container>
</ng-container>

<ng-template #themeLoading>
  <div class="d-flex justify-content-center align-items-center w-100 h-100" style="min-height: 80vh;">
    <div class="cart-loader">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  </div>
</ng-template>