import { Component, OnInit } from '@angular/core';
import { UserService } from 'src/app/core/services/user.service';

@Component({
  selector: 'app-customer-detail',
  templateUrl: './customer-detail.component.html',
  styleUrls: ['./customer-detail.component.scss'],
})
export class CustomerDetailComponent implements OnInit {
  menuTheme: string;

  constructor(public userService: UserService) { }

  ngOnInit(): void {
    this.userService.getUserTheme().subscribe(res => {
      this.menuTheme = res;
    })
  }
}
