<div class="container mt-2 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">
    <!-- Payment Details -->
    <div class="col-12 col-lg-6 mt-2">
      <div class="payment-details">
        <h5 class="border-bottom">Payment Method</h5>

        <ng-container *ngIf="restaurant?.site_setting?.wallet_available == 'Yes' && restaurant.wallet_payment == 'Yes'">
          <div class="wallet-details">
            <h5>
              <img src="./assets/wallet.png" class="pe-2" (error)="handleImageError($event)">My Wallet
            </h5>
            <div class="d-flex align-items-center" style="column-gap: .75rem;">
              <input type="checkbox" (change)="walletAdd($event)" [(ngModel)]="order.payment_wallet"
                [value]="order.payment_wallet" [disabled]="user.wallet_amount <= 0" class="cursor px-1" id="use_wallet"
                style="width: 20px;height: 20px;">
              <span class="text-center">
                Current Balance <strong>{{convertNumber(user.wallet_amount)}}</strong>
              </span>
            </div>
            <span class="w-100 text-center" style="font-size:small" *ngIf="order.wallet_amount >0" class="px-2">
              <strong>Paid From</strong> Wallet <strong>{{convertNumber(order.wallet_amount)}}</strong>
            </span>
          </div>
        </ng-container>

        <div class="d-flex flex-column"
          [class.d-none]="(order.payment_wallet && user.wallet_amount >= order.order_grand_total + order.charity_amount)">
          <div class="payment-methods">
            <ng-container *ngFor="let paymentMethod of restaurant?.payment_methods;">
              <label class="payment-method-card"
                [ngClass]="{'active': order.payment_method == paymentMethod.payment_method_name}"
                for="inlineRadio{{paymentMethod.id}}" *ngIf="paymentMethod.payment_status == 'Y'">
                <img class="px-1" src="./assets/payment-logo/paypal-small-logo.png" alt="paypal tiffintom"
                  *ngIf="paymentMethod.payment_method_name=='Paypal'" (error)="handleImageError($event)">
                <img class="px-1" src="./assets/payment-logo/cod2.png" alt="cod tiffintom"
                  *ngIf="paymentMethod.payment_method_name=='COD' || paymentMethod.payment_method_name=='cod'"
                  (error)="handleImageError($event)">
                <img class="px-1" src="./assets/payment-logo/card.png" alt="credit card tiffintom"
                  *ngIf="paymentMethod.payment_method_name=='Stripe'" (error)="handleImageError($event)">
                <img class="px-1" src="./assets/payment-logo/apple-pay.png" alt="credit card tiffintom"
                  *ngIf="paymentMethod.payment_method_name=='Apple Pay'" (error)="handleImageError($event)">
                <img class="px-1" src="./assets/payment-logo/revoult_pay.png" alt="credit card tiffintom"
                  *ngIf="paymentMethod.payment_method_name=='Revolut Pay'" (error)="handleImageError($event)">

                <div class="w-100 d-flex align-items-center">
                  <input [(ngModel)]="order.payment_method" type="radio" class="form-check-input cursor"
                    name="checkout_address" [id]="'inlineRadio'+paymentMethod.id"
                    [value]="paymentMethod.payment_method_name"
                    (click)="checkPaypalMin(paymentMethod.payment_method_name)"
                    [disabled]="(order.order_sub_total <= restaurant.paypal_minimum_order && (paymentMethod.payment_method_name == 'Paypal') || (order.order_sub_total <= restaurant.card_minimum_order && paymentMethod.payment_method_name == 'Stripe'))? '' : null">
                  <span>
                    {{ (paymentMethod.payment_method_name!='Stripe') ? paymentMethod.payment_method_name:'Credit Card'}}
                  </span>
                </div>
              </label>

              <ng-container
                *ngIf="paymentMethod.payment_method_name=='Stripe' && paymentMethod.payment_status == 'Y' && order.payment_method=='Stripe'">
                <div class="w-100 d-flex align-items-center justify-content-between ms-4">
                  <div class="d-flex align-items-center">
                    <img src="./assets/payment-logo//american-express-logo.png" alt="credit card tiffintom" class="px-1"
                      (error)="handleImageError($event)">
                    <img src="./assets/payment-logo//visa-logo.png" alt="credit card tiffintom" class="px-1"
                      (error)="handleImageError($event)">
                    <img src="./assets/payment-logo//master-card-logo.png" alt="credit card tiffintom" class="px-1"
                      (error)="handleImageError($event)">
                    <img src="./assets/payment-logo//master.png" alt="credit card tiffintom" class="px-1"
                      (error)="handleImageError($event)">
                  </div>
                  <button class="card-add-btn" data-target="#cardModal" (click)="addCard(cardModal)"
                    *ngIf="stripeCustomers.length < 3">
                    Add Card
                  </button>
                </div>

                <div *ngFor=" let stripeCustomer of stripeCustomers;let i = index;">
                  <label class="form-check-label fw-bold col-12 p-1 pb-0 text-truncate text-dark ms-4"
                    for="ccard_select_{{stripeCustomer.id}}">
                    <input [(ngModel)]="order.card_id" type="radio" class="form-check-input" name="credit_card_choose"
                      [id]="'ccard_select_'+stripeCustomer.id" [value]="stripeCustomer.id" [checked]="i==0">
                    <img src="./assets/payment-logo/visa-logo.png" class="px-1"
                      *ngIf="stripeCustomer.card_brand == 'visa'" (error)="handleImageError($event)">
                    <img src="./assets/payment-logo/master-card-logo.png" class="px-1"
                      *ngIf="stripeCustomer.card_brand == 'MasterCard'" (error)="handleImageError($event)">
                    <img src="./assets/payment-logo/american-express-logo.png" class="px-1"
                      *ngIf="stripeCustomer.card_brand == 'American Express'" (error)="handleImageError($event)">
                    <img src="./assets/payment-logo/master.png" class="px-1" (error)="handleImageError($event)"
                      *ngIf="stripeCustomer.card_brand != 'visa' && stripeCustomer.card_brand != 'MasterCard' && stripeCustomer.card_brand != 'American Express'">
                    {{ stripeCustomer.payment_method_name}}
                    XXXX-XXXXXXXX-{{stripeCustomer.card_number}} Valid till
                    {{stripeCustomer.exp_month}}/{{stripeCustomer.exp_year}}
                  </label>
                </div>

              </ng-container>
            </ng-container>
          </div>
          <ng-container *ngIf="errorPaymentMethod">
            <span class="text-danger fw-bold">{{ errorPaymentMethod }}</span>
          </ng-container>
        </div>

        <ng-container *ngIf="order.order_type == 'delivery'">
          <h5 class="border-bottom mt-4">Your Delivery Address</h5>
          <div class="d-flex align-items-center">
            <i class="fas fa-map-marker-alt me-2"></i>
            <span>{{order.address}}</span>
          </div>
          <ng-container *ngIf="restaurant.driver_tip">
            <div class="d-flex flex-column w-100 mt-4">
              <h6><i>Driver Tip</i></h6>
              <div class="d-flex flex-wrap align-items-center w-100" style="column-gap: 1rem;">
                <span class="driver-tip-btn" (click)="driverTip(1)">
                  + {{convertNumber(1)}}
                </span>
                <span class="driver-tip-btn" (click)="driverTip(2)">
                  + {{convertNumber(2)}}
                </span>
                <span class="driver-tip-btn" (click)="driverTip(5)">
                  + {{convertNumber(5)}}
                </span>
              </div>
              <div class="d-flex flex-wrap align-items-center w-100 mt-2" style="column-gap: 1rem;">
                <span class="text-start fw-bold">Tip: {{convertNumber(order.driver_tip)}}</span>
                <i class="fas fa-times-circle cursor" (click)="driverTip(0)"></i>
              </div>
            </div>
          </ng-container>
        </ng-container>
      </div>
    </div>
    <!-- Payment Details -->

    <!-- Billing Details -->
    <div class="col-12 col-lg-6 mt-2">
      <div class="billing-details">
        <div class="delivery-pickup-wrapper" *ngIf="false">
          <!-- Delivery -->
          <button (click)="orderType('delivery')" class="pickup-btn"
            [ngClass]="{'active': order.order_type == 'delivery'}">
            <i class="fas fa-car me-1"></i>Delivery
            <br>
            <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_delivery != 'Yes'">Unavailable</p>
          </button>
          <!-- Pickup -->
          <button (click)="orderType('pickup')" class="delivery-btn"
            [ngClass]="{'active': order.order_type == 'pickup'}">
            <i class="fas fa-shopping-bag me-1"></i>Pickup
            <br>
            <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_pickup != 'Yes'">Unavailable</p>
          </button>
        </div>

        <h5 class=" text-center">Your {{order.order_type | titlecase}} Order</h5>
        <div class="text-primary text-center fw-bold pb-2 border-bottom">
          You have a {{order?.carts?.length > 0 ? order?.carts?.length :''}} items
        </div>

        <ng-container *ngIf="order?.carts?.length != 0;else noItem">
          <div class="cart-items-list">
            <div class="card shadow-sm rounded-4 pb-2">
              <!-- Header -->
              <div class="row fw-bold border-bottom p-2 pb-2 mb-2 mx-0 bg-light" style="border-radius: 1rem 1rem 0 0;">
                <div class="col-6 col-md-7">Item Name</div>
                <div class="col-2 col-md-2 text-center">Qty</div>
                <div class="col-4 col-md-3 text-end">Total</div>
              </div>

              <!-- Cart Items -->
              <ng-container *ngFor="let cart of order.carts; let i = index; let isLast = last">
                <div class="row align-items-center px-0 py-2 px-md-2 mx-0" [ngClass]="{ 'border-bottom': !isLast }">
                  <div class="col-6 col-md-7">
                    <div class="w-100 ml-1 fw-bold text-primary menu-name-truncate">{{ cart?.menu_name }}</div>
                    <div class="w-100 menu-description-truncate" style="font-size: 12px; color: #999;">
                      {{ cart?.subaddons_name }}
                    </div>
                  </div>
                  <div class="col-2 col-md-2 text-center fw-bold text-primary">{{ cart.quantity }}</div>
                  <div class="col-4 col-md-3 text-end fw-bold text-primary">
                    {{ convertNumber(cart.total_price) }}
                  </div>
                </div>
              </ng-container>

              <!-- Offer Items -->
              <ng-container *ngIf="order?.applied_offers?.length > 0">
                <div class="row fw-bold border-bottom p-2 pb-2 mb-2 mx-0 text-center text-primary bg-light">
                  <div class="col-12">Offer Items</div>
                </div>
                <ng-container *ngFor="let appliedOffer of order.applied_offers; let i=index;let isLast = last">
                  <div class="row align-items-center px-0 py-2 px-md-2 mx-0" [ngClass]="{ 'border-bottom': !isLast }">
                    <div class="col-6 col-md-7">
                      <div class="w-100 ml-1 fw-bold text-primary menu-name-truncate">{{ appliedOffer?.menu_name }}
                      </div>
                      <div class="w-100 menu-description-truncate" style="font-size: 12px; color: #999;">
                        {{ appliedOffer?.subaddons_name }}
                      </div>
                    </div>
                    <div class="col-2 col-md-2 text-center fw-bold text-primary">{{ appliedOffer?.quantity }}</div>
                    <div class="col-4 col-md-3 text-end fw-bold text-primary">
                      {{appliedOffer?.total_price==0?'Free':convertNumber(appliedOffer?.total_price)}}
                    </div>
                  </div>
                </ng-container>
              </ng-container>
            </div>

            <!-- Sub Total -->
            <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2 pt-3">
              <div class="col-8 text-start">Sub Total:</div>
              <div class="col-4 text-end">{{convertNumber(order.order_sub_total)}}</div>
            </div>

            <!-- Surcharge -->
            <ng-container *ngIf="surcharges.length > 0">
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                <ng-container *ngFor="let surcharge of surcharges">
                  <div class="col-8 text-start">{{ surcharge.surcharge_name }}</div>
                  <div class="col-4 text-end">{{ convertNumber(surcharge.surcharge_amount) }}</div>
                </ng-container>
              </div>
            </ng-container>

            <!-- Service Charge -->
            <ng-container *ngIf="order.service_charge > 0">
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                <div class="col-8 text-start">Service Charge:</div>
                <div class="col-4 text-end">{{ convertNumber(order.service_charge) }}</div>
              </div>
            </ng-container>

            <!-- Delivery Charge -->
            <ng-container *ngIf="order.order_type == 'delivery' && order.delivery_charge > 0">
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                <div class="col-8 text-start">Delivery Charge:</div>
                <div class="col-4 text-end">{{ convertNumber(order.delivery_charge) }}</div>
              </div>
            </ng-container>

            <!-- Total Amount -->
            <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
              <div class="col-8 text-start">Total Amount:</div>
              <div class="col-4 text-end">
                {{convertNumber(order.order_sub_total + order.delivery_charge + surchargeAmount +
                order.service_charge)}}
              </div>
            </div>

            <!-- Voucher -->
            <ng-container *ngIf="!order.voucher_amount">
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                <div class="col-12">
                  <div class="voucher-wrapper">
                    <input type="text" class="form-control voucher-input" id="voucher_code" name="voucher_code"
                      placeholder="Apply Coupon Code" [(ngModel)]="order.voucher_code" />
                    <span class="submit-btn" (click)="voucherCheck()">Submit</span>
                  </div>
                </div>
              </div>
            </ng-container>
            <ng-container *ngIf="order.voucher_code && order.voucher_amount > 0">
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                <span class="col-9 text-start">Voucher ({{order.voucher_code}})</span>
                <span class="col-3 text-end cursor">
                  (-) {{convertNumber(order.voucher_amount)}}
                  <i class="fas fa-times cursor" (click)="voucherRemove()"></i>
                </span>
              </div>
            </ng-container>
            <ng-container *ngIf="errorVoucher">
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                <span class="col-12 text-center text-danger fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                  {{ errorVoucher }}
                </span>
              </div>
            </ng-container>

            <!-- Charity Amount -->
            <ng-container
              *ngIf="restaurant?.site_setting?.charity_message != '' && restaurant?.site_setting?.charity_amount > 0">
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                <div class="col-8 text-start">
                  <label class="col-12 cursor" for="use_charity">
                    <input type="checkbox" (change)="charityAdd($event)" class="col-2 px-1" id="use_charity"
                      style="width: 15px;height: 15px;">
                    {{restaurant?.site_setting?.charity_message}}
                  </label>
                </div>
                <div class="col-4 text-end">{{convertNumber(restaurant.site_setting.charity_amount)}}</div>
              </div>
            </ng-container>

            <!-- Reward Amount -->
            <ng-container *ngIf="order.rewardPoint || order.rewardPercentage > 0">
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                <div class="col-8 text-start">
                  <label class="col-12 cursor" for="use_redeem">
                    <input type="checkbox" (change)="redeemAdd($event)" class="col-2 px-1" id="use_redeem"
                      style="width: 15px;height: 15px;">
                    Redeem Amount ({{order.rewardPercentage}} %)
                  </label>
                </div>
                <div class="col-4 text-end">(-) {{convertNumber(order.rewardPoint)}}</div>
              </div>
            </ng-container>

            <!-- Offer Amount -->
            <ng-container *ngIf="order.offer_amount > 0">
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                <div class="col-8 text-start">
                  Offer {{order.offer_percentage?'('+order.offer_percentage+'%)':''}}
                </div>
                <div class="col-4 text-end">(-) {{convertNumber(order.offer_amount)}}</div>
              </div>
            </ng-container>

            <!-- Wallet Amount -->
            <ng-container *ngIf="order.wallet_amount > 0">
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                <div class="col-8 text-start">
                  <img src="./assets/wallet.png" class="px-2" (error)="handleImageError($event)">
                  My Wallet
                </div>
                <div class="col-4 text-end">(-) {{convertNumber(order.wallet_amount)}}</div>
              </div>
            </ng-container>

            <!-- Payable Amount -->
            <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
              <div class="col-8 text-start">Payable Amount:</div>
              <div class="col-4 text-end">{{convertNumber(getGrandTotal())}}</div>
            </div>

            <!-- Error -->
            <ng-container *ngIf="error">
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                <div class="col-12">
                  <span class="text-danger fw-bold">{{ error }}</span>
                </div>
              </div>
            </ng-container>

            <!-- Paypal Delivery -->
            <ng-container *ngIf="isPaypalDeliveryAvailable">
              <div class="col-md-12 pt-2">
                <ngx-paypal [config]="payPalConfig" class="pt-2"></ngx-paypal>
              </div>
            </ng-container>

            <!-- Paypal Pickup -->
            <ng-container *ngIf="isPaypalPickupAvailable">
              <div class="col-md-12 pt-2">
                <ngx-paypal [config]="payPalConfig" class="pt-2"></ngx-paypal>
              </div>
            </ng-container>

            <ng-container *ngIf="isDeliveryWithStripeOrCOD">
              <div class="col-md-12 pt-2">
                <button class="order-place-btn"
                  [disabled]="isCheckoutLoading || order.order_sub_total <= restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes'"
                  (click)="validateCheckout()"
                  [ngClass]="{'disabled': order.order_sub_total < restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes' || isCheckoutLoading}">
                  <p class="m-0 d-flex align-items-center justify-content-between fs-5"><span class="text-start">
                      {{ restaurant.currentStatus == 'Open' ? 'Total' : 'Pre Order'}}
                    </span>
                    <span class="spinner-border text-center" *ngIf="isCheckoutLoading" role=" status">
                      <span class="visually-hidden">Loading...</span>
                    </span>
                    <span class="spacing text-end">
                      {{convertNumber(getGrandTotal())}}
                    </span>
                  </p>
                </button>
              </div>
            </ng-container>

            <ng-container *ngIf="isPickupWithStripeOrCOD">
              <div class="col-md-12 pt-2">
                <button class="order-place-btn" [disabled]="isCheckoutLoading || restaurant.restaurant_pickup != 'Yes'"
                  (click)="validateCheckout()"
                  [ngClass]="{'disabled': restaurant.restaurant_pickup != 'Yes' || isCheckoutLoading}">
                  <p class="m-0 d-flex justify-content-between fs-5"><span class="text-start">
                      {{ restaurant.currentStatus == 'Open' ? 'Total' : 'Pre Order' }}
                    </span>
                    <span class="spinner-border text-center" *ngIf="isCheckoutLoading" role=" status">
                      <span class="visually-hidden">Loading...</span>
                    </span>
                    <span class="spacing text-end">{{convertNumber(getGrandTotal())}}</span>
                  </p>
                </button>
              </div>
            </ng-container>

            <!-- Reward Points -->
            <ng-container *ngIf="order.order_point > 0">
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                <div class="blink_me text-center text-primary col-md-12" style="text-align: initial;">
                  <img src="assets/trophy.png" class="img-fluid px-2" (error)="handleImageError($event)">
                  <span class="fw-bold">You will earn {{order.order_point}} Points</span>
                </div>
              </div>
            </ng-container>

          </div>
        </ng-container>
        <ng-template #noItem>
          <div class="empty-cart-cls">
            <img src="assets/cartitem.png" class="img-fluid m-4" (error)="handleImageError($event)">
            <span class="item-name">No Item(s) Added</span>
          </div>
        </ng-template>
      </div>
    </div>
    <!-- End Billing Details -->
  </div>
</div>

<!-- Modal Templates -->
<ng-template #cardModal let-modal>
  <div class="card-modal p-3">
    <div class="card-modal-header">
      <button class="card-close-btn" (click)="modal.dismiss('Cross click')">
        <i class="fas fa-times"></i>
      </button>
      <h4 class="card-modal-title text-primary fw-bold" id="modal-basic-title">
        Add Your New Card
      </h4>
    </div>
    <div class="modal-body">
      <form (submit)="handleForm($event)">
        <div #cardElement id="customCardElement" class="body-box-shadow py-3"
          style='height: 2.6em !important; padding: .8em !important;'>
          <!-- A Stripe Element will be inserted here. -->
        </div>

        <div *ngIf="Modelerror">
          <span class="text-danger">{{ Modelerror }}</span>
        </div>

        <button type="submit" class="card-modal-close w-100 mt-4" [disabled]="isModelLoading">
          <div class="spinner-border" *ngIf="isModelLoading" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          Add New Card
        </button>
      </form>
    </div>
  </div>
</ng-template>

<ng-template #multiplePriceOfferModal let-modal>
  <div class="multiple-price-offer-modal p-3">
    <div class="multiple-price-offer-modal-header">
      <h4 class="multiple-price-offer-modal-title text-primary fw-bold" id="modal-basic-title">
        You qualify for free below products, please select {{eligibleQty}}
      </h4>
    </div>
    <div class="modal-body my-2">
      <ul class="list-group border-0">
        <li class="bg-light list-group-item border-0 text-dark" style="margin: 0 0 10px 0;"
          *ngFor="let eligibleOffer of order.eligible_offers; let i = index">
          <div class="row">
            <div class="checkbox checkbox-inline col-9" style="padding: 0px 10px 0px;margin: 7px 0 2px;">
              <input type="checkbox" id="offer_{{i}}" (ngModelChange)="selectCheckBox(eligibleOffer,$event,i)"
                class="offer_{{i}}" [(ngModel)]="eligibleOffer.selected" name="offer_radio_{{i}}">
              <label for="offer_{{i}}" style="padding-left: 15px;">
                {{eligibleOffer?.menu_name }}
              </label>
            </div>
            <div class="col-3 pt-1 text-center d-flex">
              <div class="quantity-controls">
                <button class="delete-btn" (click)="updateToOffer(eligibleOffer,'update',i)">
                  <i class="fas fa-minus"></i>
                </button>
                <span>{{ eligibleOffer.quantity }}</span>
                <button class="add-btn" (click)="updateToOffer(eligibleOffer,'add',i)">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
            </div>
          </div>
        </li>
      </ul>
      <div *ngIf="OfferModelerror">
        <span class="text-danger">{{ OfferModelerror }}</span>
      </div>
    </div>
    <button class="multiple-price-offer-modal-close w-100" [disabled]="isOfferModelLoading || !isEligible"
      (click)="validateOffer()">
      <div class="ms-2 spinner-border text-white" *ngIf="isOfferModelLoading" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Add Product
    </button>
  </div>
</ng-template>

<ng-template #otpModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      <span class="fw-bold"
        *ngIf="(restaurant?.site_setting?.order_verify_type == 'phone' || restaurant?.site_setting?.order_verify_type == 'both') && !user.phone_verify">Phone
      </span>
      <span class="fw-bold"
        *ngIf="restaurant?.site_setting?.order_verify_type == 'both' && !user.phone_verify && !user.email_verify"> &
      </span>
      <span class="fw-bold"
        *ngIf="(restaurant?.site_setting?.order_verify_type == 'both' || restaurant?.site_setting?.order_verify_type == 'mail') && !user.email_verify">Email
      </span>
      Verify
    </h4>
  </div>
  <div class="modal-body">
    <p>Your otp has been sent on this
      <span class="fw-bold"
        *ngIf="(restaurant?.site_setting?.order_verify_type == 'phone' || restaurant?.site_setting?.order_verify_type == 'both') && !user.phone_verify">{{user.phone_number}}
      </span>
      <span class="fw-bold"
        *ngIf="restaurant?.site_setting?.order_verify_type == 'both' && !user.phone_verify && !user.email_verify"> OR
      </span>
      <span class="fw-bold"
        *ngIf="(restaurant?.site_setting?.order_verify_type == 'both' || restaurant?.site_setting?.order_verify_type == 'mail') && !user.email_verify">{{user.username}}
      </span>
      <a (click)="profileUpdate(profileModal)" class="u-1 cursor"
        *ngIf="(restaurant?.site_setting?.signup_verify_type == 'phone' || restaurant?.site_setting?.signup_verify_type=='both') && !user.phone_verify">Edit</a>
    </p>
    <div class=" col-sm-12 col-xs-12 form-group"
      *ngIf="(restaurant?.site_setting?.order_verify_type == 'phone' || restaurant?.site_setting?.order_verify_type == 'both') && !user.phone_verify">
      <input type="text" class="form-control col-md-8" id="otp" name="otp" [(ngModel)]="verifyOtp"
        placeholder="Please enter phone verification code" />
    </div>
    <div class=" col-sm-12 col-xs-12 form-group mt-2"
      *ngIf="(restaurant?.site_setting?.order_verify_type == 'both' || restaurant?.site_setting?.order_verify_type == 'mail') && !user.email_verify">
      <input type="text" class="form-control col-md-8" id="email_otp" name="email_otp" [(ngModel)]="user.email_otp"
        placeholder="Please enter email verification code" />
    </div>
    <div *ngIf="Modelotperror">
      <span class="text-danger">{{ Modelotperror }}</span>
    </div>
  </div>
  <div class="modal-footer justify-content-between">
    <button type="button" [disabled]="isModelOtpLoading"
      class="btn btn-outline-primary bg-primary text-white text-start cursor" (click)="resendOtp()">
      Re-send
    </button>
    <div class="ms-2 spinner-border text-primary" *ngIf="isModelOtpLoading" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <button type="button" [disabled]="isModelOtpLoading" class="btn btn-outline-primary bg-primary text-white cursor"
      (click)="validateOtp()">
      submit
    </button>
  </div>
</ng-template>

<ng-template #profileModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Profile Update
    </h4>
  </div>
  <div class="modal-body">
    <form nz-form class="login-form mx-auto mb-1" #userForm="ngForm"
      (ngSubmit)="userForm.form.valid && updateUser(userForm)">

      <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="false">
        <label class="col-md-4">First Name</label>
        <input type="text" class="form-control col-md-8" id="first_name" name="first_name" [(ngModel)]="user.first_name"
          required #first_name="ngModel" [ngClass]="{ 'is-invalid': userForm.submitted && first_name.invalid }" />
        <div class="invalid-feedback" *ngIf="userForm.submitted && first_name.invalid">
          <span *ngIf="first_name.errors.required">First Name is required</span>
        </div>
      </div>

      <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="false">
        <label class="col-md-4">Last Name</label>
        <input type="text" class="form-control col-md-8" id="last_name" name="last_name" [(ngModel)]="user.last_name"
          required #last_name="ngModel" [ngClass]="{ 'is-invalid': userForm.submitted && last_name.invalid }" />
        <div class="invalid-feedback" *ngIf="userForm.submitted && last_name.invalid">
          <span *ngIf="last_name.errors.required">Last Name is required</span>
        </div>
      </div>

      <div class="col-sm-12 col-xs-12 form-group mb-3">
        <label class="col-md-4">Phone Number</label>
        <input type="text" class="form-control col-md-8" (keypress)="keyPress($event)" minlength="10" maxlength="11"
          id="phone_number" name="phone_number" [(ngModel)]="user.phone_number" required #phone_number="ngModel"
          [ngClass]="{ 'is-invalid': userForm.submitted && phone_number.invalid }" />
        <div class="invalid-feedback" *ngIf="userForm.submitted && phone_number.invalid">
          <span *ngIf="phone_number.errors.required">Phone Number is required</span>
          <span *ngIf="phone_number.errors.minlength">Phone Number minimum 10 digit</span>
          <span *ngIf="phone_number.errors.maxlength">Phone Number minimum 11 digit</span>
        </div>
      </div>

      <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="false">
        <label class="col-md-4">Username</label>
        <input type="text" class="form-control col-md-8" id="username" name="username" [(ngModel)]="user.username"
          readonly />
      </div>

      <div *ngIf="ModelProfileerror">
        <span class="text-danger">{{ ModelProfileerror }}</span>
      </div>

      <div class="col-sm-12 col-xs-12 form-group">
        <button type=" button" [disabled]="isProfileLoading"
          class="btn btn-outline-primary bg-primary text-white cursor">
          <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isProfileLoading"
            role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          Update
        </button>
      </div>

    </form>
  </div>
  <!-- <div class="modal-footer justify-content-between">
    <div class="ms-2 spinner-border text-primary" *ngIf="isModelProfileLoading" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <button type="button" [disabled]="isModelProfileLoading" class="btn btn-outline-dark bg-primary text-white"
      (click)="validateProfile()">
      Save
    </button>
  </div> -->
</ng-template>

<ng-template #placeModal let-modal>
  <div class="modal-body">
    <ng-lottie height="340px" [options]="optionPlace" loop autoplay containerClass="moving-box another-class">
    </ng-lottie>
  </div>
</ng-template>

<ng-template #itemNotAvailableModal let-modal>
  <div class="item-not-available-modal p-3">
    <div class="item-not-available-modal-header">
      <button class="item-not-available-close-btn" (click)="handleItemNotAvailable('no')">
        <i class="fas fa-times"></i>
      </button>
      <h4 class="item-not-available-title text-primary fw-bold" id="modal-basic-title">
        Not Available !
      </h4>
    </div>
    <div class="modal-body">
      <!-- Message for only Order Type mismatch -->
      <div class="col-sm-12 col-xs-12 form-group mb-3 text-center fw-bold pt-2"
        *ngIf="hasOrderTypeMismatch && !hasDayMismatch">
        Some items in your cart are not available for the selected order type.
      </div>
      <!-- Message for only Day mismatch -->
      <div class="col-sm-12 col-xs-12 form-group mb-3 text-center fw-bold pt-2"
        *ngIf="hasDayMismatch && !hasOrderTypeMismatch">
        Some items are not available on the selected day.
      </div>
      <!-- Message for both mismatches -->
      <div class="col-sm-12 col-xs-12 form-group mb-3 text-center fw-bold pt-2"
        *ngIf="hasOrderTypeMismatch && hasDayMismatch">
        Some items are not available for the selected order type or delivery day.
      </div>
    </div>
    <!-- Buttons -->
    <div class="row pt-3 g-3">
      <div class="col-sm-6 col-xs-6">
        <button class="item-not-available-modal-close w-100" (click)="handleItemNotAvailable('no')">
          No
        </button>
      </div>
      <div class="col-sm-6 col-xs-6">
        <button class="item-not-available-modal-close w-100" (click)="handleItemNotAvailable('yes')">
          Yes
        </button>
      </div>
    </div>
  </div>
</ng-template>