<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class="row" *ngIf="!isLoading">
    <div class="col-12 bg-white d-md-flex w-100 body-box-shadow rounded" style="min-height: 500px;">

      <div class="col-md-2 col-sm-1 p-2">
        <ul class="list-group">
          <a routerLink="dashboard">
            <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">
              Dashboard
            </li>
          </a>
          <a routerLink="orders">
            <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">My Order
            </li>
          </a>
          <a routerLink="bookings">
            <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">My Booking
            </li>
          </a>
          <a routerLink="addresses">
            <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">Address Book
            </li>
          </a>
          <a routerLink="profile">
            <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">Profile
            </li>
          </a>
          <a routerLink="payments">
            <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">Payments
            </li>
          </a>
          <a routerLink="wallet"
            *ngIf="restaurant?.site_setting?.wallet_available == 'Yes' && restaurant.wallet_payment == 'Yes'">
            <li class=" list-group-item" nzMatchRouter [routerLinkActive]="['active']">My Wallet
            </li>
          </a>
          <a routerLink="rewards" *ngIf="reward?.reward_option == 'Yes' && restaurant.reward_option == 'Yes'">
            <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">Reward Point
            </li>
          </a>
          <a routerLink="privacy-settings">
            <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">Privacy Setting
            </li>
          </a>
          <a routerLink="referral" *ngIf="referral.referral_option == 'Yes'">
            <li class="list-group-item" nzMatchRouter [routerLinkActive]="['active']">Referral Friend
            </li>
          </a>
        </ul>
      </div>

      <div class="col-md-10">

        <router-outlet></router-outlet>

      </div>

    </div>


    <div class="col-12 bg-white d-md-flex w-100 body-box-shadow rounded p-2" *ngIf="false">
      <ul ngbNav #nav="ngbNav" class="list-group pr-1 pb-1" orientation="vertical">
        <!-- <li ngbNavItem="dashboard" class="list-group-item p-0 bg-dark border-bottom w-100">
          <a ngbNavLink class="text-white">Dashboard</a>
          <ng-template ngbNavContent class="list-group-item text-center">
            <div class="p-3 text-center w-100 align-self-center">
              Dashboard.
            </div>
          </ng-template>
        </li> -->
        <li ngbNavItem="order" class="list-group-item p-0 bg-dark border-bottom">
          <a ngbNavLink class="text-white">My Order</a>
          <ng-template ngbNavContent>
            <app-orders></app-orders>
          </ng-template>
        </li>
        <li ngbNavItem="booking" class="list-group-item p-0 bg-dark border-bottom">
          <a ngbNavLink class="text-white">My Booking</a>
          <ng-template ngbNavContent>
            <div class="p-3">Booking.</div>
          </ng-template>
        </li>
        <li ngbNavItem="reward" class="list-group-item p-0 bg-dark border-bottom">
          <a ngbNavLink class="text-white">Reward Point</a>
          <ng-template ngbNavContent>
            <div class="p-3">Reward.</div>
          </ng-template>
        </li>
        <li ngbNavItem="wallet" class="list-group-item p-0 bg-dark border-bottom"
          *ngIf="restaurant.wallet_payment == 'Yes'">
          <a ngbNavLink class="text-white">My Wallet</a>
          <ng-template ngbNavContent>
            <div class="p-3">Wallet.</div>
          </ng-template>
        </li>
        <li ngbNavItem="profile" class="list-group-item p-0 bg-dark border-bottom">
          <a ngbNavLink class="text-white">Profile</a>
          <ng-template ngbNavContent>
            <div class="p-3">Profile.</div>
          </ng-template>
        </li>
        <li ngbNavItem="settings" class="list-group-item p-0 bg-dark border-bottom">
          <a ngbNavLink class="text-white">Settings</a>
          <ng-template ngbNavContent>
            <div class="p-3">Settings.</div>
          </ng-template>
        </li>
        <li ngbNavItem="payments" class="list-group-item p-0 bg-dark border-bottom">
          <a ngbNavLink class="text-white">Payments</a>
          <ng-template ngbNavContent>
            <div class="p-3">Payments.</div>
          </ng-template>
        </li>
        <li ngbNavItem="address" class="list-group-item p-0 bg-dark border-bottom">
          <a ngbNavLink class="text-white">Address Book</a>
          <ng-template ngbNavContent>
            <div class="p-3">Address Book.</div>
          </ng-template>
        </li>
        <li ngbNavItem="privacy" class="list-group-item p-0 bg-dark border-bottom">
          <a ngbNavLink class="text-white">Privacy Setting</a>
          <ng-template ngbNavContent>
            <div class="p-3">Privacy Setting.</div>
          </ng-template>
        </li>
        <li ngbNavItem="referral" class="list-group-item p-0 bg-dark border-bottom">
          <a ngbNavLink class="text-white">Referral Friend</a>
          <ng-template ngbNavContent>
            <div class="p-3">Referral Friend.</div>
          </ng-template>
        </li>
      </ul>
      <div [ngbNavOutlet]="nav"></div>
    </div>

  </div>
</div>