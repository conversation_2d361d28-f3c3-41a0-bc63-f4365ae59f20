.user-profile {
  background: linear-gradient(135deg, #f9fafb, #eef2f7);
  border-radius: 12px;
  // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.user-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.8rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.6rem;
  font-weight: 500;
  color: #1f2937;
}

.verify-icon {
  width: 18px;
  height: 18px;
}

// Cards CSS

.data-cards {
  border-radius: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.data-table-container {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  height: 100%;
}

.data-table-header {
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #e8f1fc;
  border-bottom: 1px solid #eee;

  .badge-count {
    background-color: var(--primary);
    color: white;
    font-weight: bold;
    font-size: 0.875rem;
    // padding: 2px 10px;
    // border-radius: 12px;
    padding: 4px;
    border-radius: 100%;
    min-width: 28px;
    text-align: center;
  }
}

// Table CSS

.data-table-list {
  padding: 8px 0;
  overflow-y: auto;
  max-height: 340px;

  .data-table-item {
    display: flex;
    flex-direction: column;
    // padding: 16px 20px;
    padding: 8px 14px;
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.2s;
    cursor: pointer;

    &:hover {
      background-color: #f8fafc;
    }

    &:last-child {
      border-bottom: none;
    }

    .property-info {
      display: flex;
      align-items: center;
      // margin-bottom: 8px;

      .property-icon {
        width: 40px;
        height: 40px;
        min-width: 40px;
        min-height: 40px;
        // border-radius: 10px;
        border-radius: 100%;
        color: #ffffff;
        background: linear-gradient(135deg, var(--primary), var(--primary));
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-weight: 600;

        i {
          font-size: 1.25rem;
          color: #ffffff;
        }
      }

      .property-details {
        flex: 1;

        .property-name {
          font-weight: 600;
          font-size: 0.95rem;
          color: #334155;
          margin-bottom: 2px;
        }

        .missing-count {
          font-size: 0.8rem;
          color: #64748b;

          .badge {
            background-color: var(--primary);
            color: white;
            font-size: 0.75rem;
            padding: 1px 6px;
            border-radius: 8px;
          }
        }
      }
    }

    .data-table-details {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 52px; // Align with property name

      .doc-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;

        .doc-tag {
          background-color: #f1f5f9;
          color: #334155;
          font-size: 0.75rem;
          padding: 4px 8px;
          border-radius: 6px;
          white-space: nowrap;
        }

        .more-tag {
          background-color: #e2e8f0;
          color: #475569;
          font-size: 0.75rem;
          padding: 4px 8px;
          border-radius: 6px;
        }
      }

      .arrow-icon {
        color: #94a3b8;
        font-size: 0.875rem;
      }
    }

    .more-button {
      background-color: var(--primary);
      color: #fff;
      border: 1px solid var(--primary);
      padding: 0.25rem 0.75rem;
      border-radius: 2rem;
      font-size: 1rem;
      font-weight: 500;
      text-decoration: none;
      width: fit-content;
      margin: auto;
      transition: all 0.3s ease;

      &:hover {
        color: var(--primary);
        border: 1px solid var(--primary);
        background-color: #fff;
      }
    }
  }

  .no-data-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #94a3b8;
    text-align: center;

    i {
      font-size: 2.5rem;
      margin-bottom: 16px;
      color: #cbd5e1;
    }

    p {
      font-size: 0.95rem;
      margin: 0;
    }
  }
}
