::ng-deep .rounded-modal > .modal-dialog > .modal-content {
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.card-modal {
  border-radius: 1rem;

  .card-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .card-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      min-width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
    }

    .card-modal-title {
      font-size: 1.5rem;
    }
  }

  .card-modal-close {
    background: var(--primary);
    color: white;
    border: 1px solid #fff;
    padding: 8px 14px;
    border-radius: 2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: fit-content;
    margin-left: auto;

    &:hover {
      color: var(--primary);
      border: 1px solid var(--primary);
      background-color: #fff;
    }
  }
}

.add-card-button {
  background: var(--primary);
  color: white;
  border: 1px solid #fff;
  padding: 8px 14px;
  border-radius: 2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: fit-content;
  margin-left: auto;

  &:hover {
    color: var(--primary);
    border: 1px solid var(--primary);
    background-color: #fff;
  }
}

.payment-card-body {
  border-radius: 1rem;
  padding: 1.5rem;
  background: linear-gradient(140deg, #282828, #525252, #000);
  color: white;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
  width: 100%;
  position: relative;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.payment-card-body:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.card-type {
  font-size: 0.9rem;
  letter-spacing: 1px;
  opacity: 0.85;
}

.card-number {
  font-size: 1.4rem;
  letter-spacing: 2px;
  font-weight: bold;
}

.card-footer {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.card-owner {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.owner-name {
  font-weight: 600;
  font-size: 1.1rem;
}

.expiry {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  opacity: 0.85;
}

.card-brand {
  font-weight: bold;
  font-size: 1.5rem;
  opacity: 0.9;
}

.delete-card-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.15);
  color: white;
  padding: 8px 14px;
  border-radius: 2rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.delete-card-btn:hover {
  background: white;
  color: crimson;
}
