import { NgModule } from '@angular/core';
import { CustomerDetailComponent } from './customer-detail.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { OrdersComponent } from './orders/orders.component';
import { BookingsComponent } from './bookings/bookings.component';
import { RewardsComponent } from './rewards/rewards.component';
import { WalletComponent } from './wallet/wallet.component';
import { PaymentComponent } from './payment/payment.component';
import { AddressComponent } from './address/address.component';
import { ProfileComponent } from './profile/profile.component';
import { PrivacyComponent } from './privacy/privacy.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { NgCircleProgressModule } from 'ng-circle-progress';
import { ReferralComponent } from './referral/referral.component';
import { Orders1Component } from './orders/orders1/orders1.component';
import { Orders2Component } from './orders/orders2/orders2.component';
import { Address1Component } from './address/address1/address1.component';
import { Address2Component } from './address/address2/address2.component';
import { Profile1Component } from './profile/profile1/profile1.component';
import { Profile2Component } from './profile/profile2/profile2.component';
import { Payment1Component } from './payment/payment1/payment1.component';
import { Payment2Component } from './payment/payment2/payment2.component';
import { CustomerDetails1Component } from './customer-details1/customer-details1.component';
import { CustomerDetails2Component } from './customer-details2/customer-details2.component';
import { Booking1Component } from './bookings/booking1/booking1.component';
import { Booking2Component } from './bookings/booking2/booking2.component';
import { Referral1Component } from './referral/referral1/referral1.component';
import { Referral2Component } from './referral/referral2/referral2.component';
import { Dashboard1Component } from './dashboard/dashboard1/dashboard1.component';
import { Dashboard2Component } from './dashboard/dashboard2/dashboard2.component';

const routes: Routes = [
  { path: '', redirectTo: 'dashboard', component: CustomerDetailComponent, data: { selectedTab: 0 } },
  { path: 'orders', component: OrdersComponent, data: { selectedTab: 1 } },
  { path: 'bookings', component: BookingsComponent, data: { selectedTab: 2 } },
  { path: 'rewards', component: RewardsComponent, data: { selectedTab: 3 } },
  { path: 'wallet', component: WalletComponent, data: { selectedTab: 4 } },
  { path: 'payments', component: PaymentComponent, data: { selectedTab: 5 } },
  { path: 'addresses', component: AddressComponent, data: { selectedTab: 6 } },
  { path: 'profile', component: ProfileComponent, data: { selectedTab: 7 } },
  { path: 'privacy-settings', component: PrivacyComponent, data: { selectedTab: 8 } },
  { path: 'dashboard', component: DashboardComponent, data: { selectedTab: 9 } },
  { path: 'referral', component: ReferralComponent, data: { selectedTab: 10 } },
];

@NgModule({
  declarations: [CustomerDetailComponent, OrdersComponent, BookingsComponent, RewardsComponent, WalletComponent, PaymentComponent,
    AddressComponent, ProfileComponent, PrivacyComponent, DashboardComponent, ReferralComponent, Orders1Component, Orders2Component,
    Address1Component, Address2Component, Profile1Component, Profile2Component, Payment1Component,
    Payment2Component, CustomerDetails1Component, CustomerDetails2Component, Booking1Component,
    Booking2Component, Referral1Component, Referral2Component, Dashboard1Component, Dashboard2Component],
  imports: [RouterModule.forChild(routes), SharedModule, NgbModule, NgCircleProgressModule.forRoot()],
})
export class CustomerDetailModule { }
