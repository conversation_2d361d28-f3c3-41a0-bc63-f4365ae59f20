import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { ReferralService } from 'src/app/core/services/referral.service';
import { Referral } from 'src/app/core/models/referral';
import { Restaurant } from 'src/app/core/models/restaurant';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { RewardService } from 'src/app/core/services/reward.service';

@Component({
  selector: 'app-customer-details2',
  templateUrl: './customer-details2.component.html',
  styleUrls: ['./customer-details2.component.scss']
})
export class CustomerDetails2Component implements OnInit {
  subs = new Subscription();
  isLoading = false;
  error = null;

  isModelLoading = false;
  Modelerror = null;

  user: User;
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;
  tabString: string;
  referral: Referral = new Referral();
  restaurant: Restaurant = new Restaurant();
  reward: any;

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private currencyPipe: CurrencyPipe,
    private referralService: ReferralService,
    private restaurantService: RestaurantService,
    private rewardService: RewardService,
    private cdr: ChangeDetectorRef,
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    this.modalOptions = {
      backdrop: 'static',
      size: 'lg',
      backdropClass: 'customBackdrop',
    };
    this.clickOnTab('dashboard')
    this.fetchReferral();
    this.fetchRestaurant();
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
        this.cdr.detectChanges();
        this.fetchReward();
      }, err => this.error = err)
    );
  }

  fetchReward() {
    this.isLoading = true;

    this.subs.add(this.rewardService.rewardMain()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.reward = res;
        this.cdr.detectChanges();
      }, err => this.error = err)
    );
  }


  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  fetchReferral() {
    this.isLoading = true;

    this.subs.add(this.referralService.show('1')
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.referral = res;
        this.cdr.detectChanges();
      }, err => this.error = err)
    );
  }

  clickOnTab(tab) {
    if (tab == this.tabString) {
      tab = null;
    }
    this.tabString = tab;
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd', 'en_US')
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
