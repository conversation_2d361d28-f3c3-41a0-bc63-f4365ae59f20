import { NgModule } from '@angular/core';
import { CheckoutComponent } from './checkout.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgxPayPalModule } from 'ngx-paypal';
import { LottieModule } from 'ngx-lottie';
import { Checkout2Component } from './checkout2/checkout2.component';
import { CheckoutThemeComponent } from './checkout-theme/checkout-theme.component';

const routes: Routes = [
  // { path: '', component: CheckoutComponent },
  { path: '', component: CheckoutThemeComponent },
];

export function playerFactory() {
  return import('lottie-web');
}
@NgModule({
  declarations: [CheckoutComponent, Checkout2Component, CheckoutThemeComponent],
  imports: [RouterModule.forChild(routes), SharedModule, NgbModule, NgxPayPalModule, LottieModule.forRoot({ player: playerFactory })],
})
export class CheckoutModule { }
