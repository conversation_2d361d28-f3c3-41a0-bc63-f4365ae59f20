import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorHandler } from 'src/app/shared/error-handler';
import { environment } from 'src/environments/environment';
import { WalletHistory } from '../models/wallet-history';

@Injectable({
  providedIn: 'root',
})
export class WalletHistoryService {
  private url = environment.apiBaseUrl + 'wallet-history/';
  public carts: WalletHistory[] = []
  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.customer_id) params = params.set('customer_id', options.customer_id);
    if (options.query) params = params.set('query', options.query);
    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(history: WalletHistory): Observable<any> {
    return this.http.post<WalletHistory>(this.url, WalletHistory.toFormData(history))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(history: WalletHistory): Observable<any> {
    return this.http.post<WalletHistory>(this.url + history.id, WalletHistory.toFormData(history))
      .pipe(catchError(ErrorHandler.handleError));
  }

}
