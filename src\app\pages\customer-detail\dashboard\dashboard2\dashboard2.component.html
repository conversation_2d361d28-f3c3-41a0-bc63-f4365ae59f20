<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class="row" style="row-gap: 1rem;" *ngIf="!isLoading">

    <div class="col-12 pb-3 d-flex w-100">
      <div class="user-profile body-box-shadow p-3 w-100">
        <h5 class="user-name">{{ user.first_name }}</h5>
        <div class="user-info">
          <div class="info-item">
            <img src="assets/verify_correct.png" *ngIf="user.email_verify" class="verify-icon">
            <img src="assets/verify_close.png" *ngIf="!user.email_verify" class="verify-icon">
            <span>{{ user.username }}</span>
          </div>
          <div class="info-item">
            <img src="assets/verify_correct.png" *ngIf="user.phone_verify" class="verify-icon">
            <img src="assets/verify_close.png" *ngIf="!user.phone_verify" class="verify-icon">
            <span>{{ user.phone_number }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Cards -->
    <!-- Earn Point -->
    <div class="col-12 col-md-6 col-lg-3">
      <div class="p-4 body-box-shadow text-center h-100 data-cards">
        <p>
          <img src="assets/trophy_dashboard.png" class="px-1">
        </p>
        <span class="text-muted">Earn Point</span>
        <p class="fw-bold ">{{dashboardDetails?.stats.total_earn_point}}</p>
      </div>
    </div>
    <!-- Wallet -->
    <div class="col-12 col-md-6 col-lg-3">
      <div class="p-4 body-box-shadow text-center h-100 data-cards">
        <p>
          <img src="assets/wallet_dashboard.png" class="px-1">
        </p>
        <span class="text-muted">Wallet</span>
        <p class="fw-bold ">{{convertNumber(user.wallet_amount)}}</p>
      </div>
    </div>
    <!-- Profile Percentage -->
    <div class="col-12 col-md-6 col-lg-3">
      <div class="p-4 body-box-shadow text-center h-100 data-cards">
        <circle-progress class="col-md-12 col-xs-12" [percent]="dashboardDetails?.stats.profile_percentage"
          [outerStrokeWidth]="8" [outerStrokeColor]="'#3777BE'" [animation]="true" [animationDuration]="1000"
          [subtitleFontSize]="12" lazy="true" radius="65" subtitle="Profile">
        </circle-progress>
      </div>
    </div>
    <!-- Verify -->
    <div class="col-12 col-md-6 col-lg-3">
      <div class="p-4 body-box-shadow text-center h-100 data-cards">
        <p class="text-muted m-1">Verify your email</p>
        <span class="btn btn-danger text-white fw-bold cursor" *ngIf="!user.email_verify && !emailTab"
          (click)="emailOtpSend()">Email
          Verify</span>
        <div class="col-12 py-1 text-center" *ngIf="emailTab">
          <input type="text" class="form-control col-md-3 col-xs-12" id="email_otp" name="email_otp"
            [(ngModel)]="verifyOtp" placeholder="Enter your otp" />
        </div>
        <span class="text-primary fw-bold col-md-4 col-xs-12 px-1 cursor" *ngIf="emailTab"
          (click)="resendEmailOtp()">Resend
          |
        </span>
        <span class="text-primary fw-bold col-md-4 col-xs-12 px-1 cursor" *ngIf="emailTab"
          (click)="validateEmail()">Submit</span>
        <div *ngIf="ModelEmailOtpError">
          <span class="text-danger">{{ ModelEmailOtpError }}</span>
        </div>
        <span class="btn btn-success text-white fw-bold" *ngIf="user.email_verify">Email Verified</span>
        <p class="text-muted m-1">Verify your phone</p>

        <span class="btn btn-danger text-white fw-bold cursor" *ngIf="!user.phone_verify && !phoneTab"
          (click)="otpSend()">Phone
          Verify</span>
        <div class="col-12 py-1 text-center" *ngIf="phoneTab">
          <input type="text" class="form-control col-md-3 col-xs-12" id="otp" name="otp" [(ngModel)]="verifyOtp"
            placeholder="Enter your otp" />
        </div>
        <span class="text-primary fw-bold col-md-4 col-xs-12 px-1 cursor" *ngIf="phoneTab" (click)="resendOtp()">Resend
          |
        </span>
        <span class="text-primary fw-bold col-md-4 col-xs-12 px-1 cursor" *ngIf="phoneTab"
          (click)="validateOtp()">Submit</span>
        <div *ngIf="Modelotperror">
          <span class="text-danger">{{ Modelotperror }}</span>
        </div>

        <span class="btn btn-success text-white fw-bold" *ngIf="user.phone_verify">Phone Verified</span>
      </div>
    </div>

    <!-- Tables -->
    <!-- Orders -->
    <div class="col-md-6 col-xs-12 mt-3">
      <div class="data-table-container border shadow">
        <div class="data-table-header">
          <h5 class="fw-bold">Orders</h5>
          <div class="badge-count">{{ completeOrderList?.length || 0 }}</div>
        </div>
        <div class="data-table-list">
          <ng-container *ngIf="completeOrderList?.length > 0;else noOrders">
            <div *ngFor="let item of completeOrderList;let i = index" class="data-table-item"
              (click)="orderView(item.id)">
              <div class="property-info">
                <div class="property-icon tenant-icon">
                  <i class="fas fa-shopping-bag"></i>
                </div>
                <div class="property-details">
                  <div class="property-name">{{item.order_number}}</div>
                  <div class="missing-count">
                    {{item.status}}
                  </div>
                </div>
                <div class="data-table-details">
                  <i class="fas fa-chevron-right arrow-icon"></i>
                </div>
              </div>
            </div>
            <div class="data-table-item">
              <a routerLink="../orders" class="more-button">
                More Orders <i class="fas fa-chevron-right arrow-icon"></i>
              </a>
            </div>
          </ng-container>
          <ng-template #noOrders>
            <div class="col-md-12 empty-cart-cls text-center">
              <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
              <p><strong>No order(s) Found</strong></p>
            </div>
          </ng-template>
        </div>
      </div>
    </div>
    <!-- Booking -->
    <div class="col-md-6 col-xs-12 mt-3">
      <div class="data-table-container border shadow">
        <div class="data-table-header">
          <h5 class="fw-bold">Booking</h5>
          <div class="badge-count">{{ bookigList?.length || 0 }}</div>
        </div>
        <div class="data-table-list">
          <ng-container *ngIf="bookigList?.length > 0;else noBooking">
            <div *ngFor="let item of bookigList;let i = index" class="data-table-item" (click)="bookView(item.id)">
              <div class="property-info">
                <div class="property-icon tenant-icon">
                  <i class="fas fa-address-book"></i>
                </div>
                <div class="property-details">
                  <div class="property-name">{{item.booking_id}}</div>
                  <div class="missing-count">
                    {{item.status}}
                  </div>
                </div>
                <div class="data-table-details">
                  <i class="fas fa-chevron-right arrow-icon"></i>
                </div>
              </div>
            </div>
            <div class="data-table-item">
              <a routerLink="../bookings" class="more-button">
                More Bookings <i class="fas fa-chevron-right arrow-icon"></i>
              </a>
            </div>
          </ng-container>
          <ng-template #noBooking>
            <div class="col-md-12 empty-cart-cls text-center">
              <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
              <p><strong>No booking(s) Found</strong></p>
            </div>
          </ng-template>
        </div>
      </div>
    </div>
    <!-- Card Details -->
    <div class="col-md-6 col-xs-12 mt-3">
      <div class="data-table-container border shadow">
        <div class="data-table-header">
          <h5 class="fw-bold">Card Details</h5>
          <div class="badge-count">{{ cardList?.length || 0 }}</div>
        </div>
        <div class="data-table-list">
          <ng-container *ngIf="cardList?.length > 0;else noCards">
            <div *ngFor="let item of cardList;let i = index" class="data-table-item">
              <div class="property-info">
                <div class="property-icon tenant-icon">
                  <i class="fas fa-credit-card"></i>
                </div>
                <div class="property-details">
                  <div class="property-name">XXXX-XXXXXXXX-{{item.card_number}}</div>
                  <div class="missing-count">
                    {{item.exp_month}} / {{item.exp_year}}
                  </div>
                </div>
                <div class="data-table-details">
                  <i class="fas fa-chevron-right arrow-icon"></i>
                </div>
              </div>
            </div>
            <div class="data-table-item">
              <a routerLink="../payments" class="more-button">
                More Cards <i class="fas fa-chevron-right arrow-icon"></i>
              </a>
            </div>
          </ng-container>
          <ng-template #noCards>
            <div class="col-md-12 empty-cart-cls text-center">
              <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
              <p><strong>No card(s) Found</strong></p>
            </div>
          </ng-template>
        </div>
      </div>
    </div>
    <!-- Address -->
    <div class="col-md-6 col-xs-12 mt-3">
      <div class="data-table-container border shadow">
        <div class="data-table-header">
          <h5 class="fw-bold">Address</h5>
          <div class="badge-count">{{ addressList?.length || 0 }}</div>
        </div>
        <div class="data-table-list">
          <ng-container *ngIf="addressList?.length > 0;else noAddresses">
            <div *ngFor="let item of addressList;let i = index" class="data-table-item">
              <div class="property-info">
                <div class="property-icon tenant-icon">
                  <i class="fas fa-map-marker-alt" aria-hidden="true"></i>
                </div>
                <div class="property-details">
                  <div class="property-name">{{item.title}}</div>
                  <div class="missing-count">
                    {{item.address}}{{item.zipcode}}
                  </div>
                </div>
                <div class="data-table-details">
                  <i class="fas fa-chevron-right arrow-icon"></i>
                </div>
              </div>
            </div>
            <div class="data-table-item">
              <a routerLink="../addresses" class="more-button">
                More Addresses <i class="fas fa-chevron-right arrow-icon"></i>
              </a>
            </div>
          </ng-container>
          <ng-template #noAddresses>
            <div class="col-md-12 empty-cart-cls text-center">
              <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
              <p><strong>No address(s) Found</strong></p>
            </div>
          </ng-template>
        </div>
      </div>
    </div>
    <!-- Pending Order Review -->
    <div class="col-md-6 col-xs-12 mt-3">
      <div class="data-table-container border shadow">
        <div class="data-table-header">
          <h5 class="fw-bold">Pending Order Review</h5>
          <div class="badge-count">{{ deliveredOrderList?.length || 0 }}</div>
        </div>
        <div class="data-table-list">
          <ng-container *ngIf="deliveredOrderList?.length > 0;else noReview">
            <div *ngFor="let item of deliveredOrderList;let i = index" class="data-table-item"
              (click)="addReview(reviewModal,item)">
              <div class="property-info">
                <div class="property-icon tenant-icon">
                  <i class="fas fa-clock"></i>
                </div>
                <div class="property-details">
                  <div class="property-name">{{item.order_number}}</div>
                  <div class="missing-count">
                    {{item.status}}
                  </div>
                </div>
                <div class="data-table-details">
                  <i class="fas fa-chevron-right arrow-icon"></i>
                </div>
              </div>
            </div>
            <div class="data-table-item">
              <a routerLink="../orders" class="more-button">
                More Orders <i class="fas fa-chevron-right arrow-icon"></i>
              </a>
            </div>
          </ng-container>
          <ng-template #noReview>
            <div class="col-md-12 empty-cart-cls text-center">
              <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
              <p><strong>No order(s) Found</strong></p>
            </div>
          </ng-template>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #addressModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Add New Deliver Address
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">PostCode</label>
      <div class="col-sm-12 col-xs-12 d-flex justify-content-between">
        <input type="text" class="form-control col-md-5 col-xs-12 w-50" id="zipcode" name="zipcode"
          [(ngModel)]="addressBookAdd.zipcode" required />
        <div class="ms-2 spinner-border text-primary" *ngIf="isModelLoading" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <a class="btn bg-primary col-md-5 col-xs-12 text-white align-items-right cursor" [disabled]="isModelLoading"
          style="width:4cm;" (click)="findzipcode(addressBookAdd.zipcode)">
          Find Address
        </a>
      </div>
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Address Title</label>
      <input type="text" class="form-control col-md-8" id="title" name="title" [(ngModel)]="addressBookAdd.title" />
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Door no / Flat no</label>
      <input type="text" class="form-control col-md-8" id="flat_no" name="flat_no" [(ngModel)]="addressBookAdd.flat_no"
        required />
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Address</label>
      <input type="text" class="form-control col-md-8" id="address" name="address" [(ngModel)]="addressBookAdd.address"
        readonly />
    </div>
    <input type="hidden" class="form-control col-md-8" id="latitude" name="latitude"
      [(ngModel)]="addressBookAdd.latitude" />
    <input type="hidden" class="form-control col-md-8" id="longitude" name="longitude"
      [(ngModel)]="addressBookAdd.longitude" />
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isModelLoading" class="btn btn-outline-dark bg-primary cursor text-white"
      (click)="validateAddress(addressModal)">
      Add Address
    </button>
    <div class="ms-2 spinner-border text-primary" *ngIf="isModelLoading" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
</ng-template>

<ng-template #reviewModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Review
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group d-flex">
      <label class="col-md-4">Rating</label>
      <div class="col-md-8">
        <app-rating [stars]="'5'" [(rating)]="reviewAdd.rating" class="pt-0 mt-0"></app-rating>
      </div>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-4">Message</label>
      <div class="col-sm-8">
        <textarea rows="3" class="form-control" nz-input [(ngModel)]="reviewAdd.message" name="message" id="message"
          placeholder="Enter Your Message"> </textarea>
      </div>
    </div>
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isModelLoading" class="btn btn-outline-dark bg-primary cursor text-white"
      (click)="validateReview(reviewModal)">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Submit
    </button>
  </div>
</ng-template>

<ng-template #bookingModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Booking Details
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-6">Booking ID :</label>
      <label class="col-md-6">{{booking.booking_id}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Restaurant Name :</label>
      <label class="col-sm-6">{{restaurant.restaurant_name}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Guest :</label>
      <label class="col-sm-6">{{booking.guest_count}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Booking Date :</label>
      <label class="col-sm-6">{{booking.booking_date}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Booking Time :</label>
      <label class="col-sm-6">{{booking.booking_time}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2" *ngIf="booking.booking_instruction">
      <label class="col-sm-6">Instruction :</label>
      <label class="col-sm-6">{{booking.booking_instruction}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Place Date :</label>
      <label class="col-sm-6">{{convertToDate(booking.created)}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Booking Status :</label>
      <label class="col-sm-6">{{booking.status | titlecase}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2" *ngIf="booking.cancel_reason">
      <label class="col-sm-6">Cancel Reason :</label>
      <label class="col-sm-6">{{booking.cancel_reason}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Customer Name :</label>
      <label class="col-sm-6">{{booking.customer_name}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Customer Email :</label>
      <label class="col-sm-6">{{booking.booking_email}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Customer Phone Number :</label>
      <label class="col-sm-6">{{booking.booking_phone}}</label>
    </div>
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
</ng-template>

<ng-template #otpModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Phone Verify
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Otp</label>
      <input type="text" class="form-control col-md-8" id="otp" name="otp" [(ngModel)]="verifyOtp"
        placeholder="Enter your Otp" />
    </div>
    <div *ngIf="Modelotperror">
      <span class="text-danger">{{ Modelotperror }}</span>
    </div>
  </div>
  <div class="modal-footer justify-content-between">
    <button type="button" [disabled]="isModelOtpLoading"
      class="btn btn-outline-dark bg-primary cursor text-white text-start" (click)="resendOtp()">
      Re-send
    </button>
    <div class="ms-2 spinner-border text-primary" *ngIf="isModelOtpLoading" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <button type="button" [disabled]="isModelOtpLoading" class="btn btn-outline-dark bg-primary cursor text-white"
      (click)="validateOtp()">
      submit
    </button>
  </div>
</ng-template>