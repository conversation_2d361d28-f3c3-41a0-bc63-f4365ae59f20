import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { Component, On<PERSON>estroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { Category } from 'src/app/core/models/category';
import { Menu } from 'src/app/core/models/menu';
import { CategoryService } from 'src/app/core/services/category.service';
import {
  NgbModal,
  ModalDismissReasons,
  NgbModalOptions,
  NgbActiveModal,
} from '@ng-bootstrap/ng-bootstrap';
import { MenuService } from 'src/app/core/services/menu.service';
import { Variant } from 'src/app/core/models/variant';
import { UserService } from 'src/app/core/services/user.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Order } from 'src/app/core/models/order';
import { Cart } from 'src/app/core/models/cart';
import { environment } from 'src/environments/environment';
import { CartService } from 'src/app/core/services/cart.service';
import { AddressBookService } from 'src/app/core/services/address-book.service';
import { AddressBook } from 'src/app/core/models/address-book';
import { Meta, Title } from '@angular/platform-browser';
import { NotificationService } from 'src/app/core/services/notification.service';
import { MessagingService } from 'src/app/core/services/messaging.service';

@Component({
  selector: 'app-pre-checkout',
  templateUrl: './pre-checkout.component.html',
  styleUrls: ['./pre-checkout.component.scss'],
})
export class PreCheckoutComponent implements OnInit, OnDestroy {
  subs = new Subscription();
  isPrecheckoutLoading = false;
  isLoading = false;
  error = null;
  errorAddress = null;
  errorTime = null;
  isModelLoading = false;
  Modelerror = null;
  isPostcodeLoading = false;
  Postcodeerror = null;
  categories: Category[] = [];
  menus: Menu[] = [];
  carts: Cart[] = [];
  addressBooks: AddressBook[] = [];
  timeSlots = [];
  addressBookAdd: AddressBook;
  restaurant: Restaurant = new Restaurant();
  order: Order = new Order();
  categoryOptions = { nopaginate: 1, prefilled: 1, isSuggested: 1 };
  expandableCategoryId: string;
  selectedMenu: Menu;
  selectedVariant: Variant;
  modalOptions: NgbModalOptions;
  expandableMainAddonId: string;
  userId: string;
  restaurant_id: string;
  showCategory: boolean = false;
  minDate: any;
  maxDate: any;
  deliveryDate: any;
  deliveryAddressId: string;
  isRestaurantLoading = false;

  constructor(
    private router: Router,
    private categoryService: CategoryService,
    private menuService: MenuService,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private restaurantService: RestaurantService,
    private cartService: CartService,
    private currencyPipe: CurrencyPipe,
    private addressBookService: AddressBookService,
    private metaTagService: Meta,
    private titleService: Title,
    private notificationService: NotificationService,
    private messagingService: MessagingService,
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.userId = user?.id
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    if (JSON.parse(this.cartService.getOrder())) {
      this.order = JSON.parse(this.cartService.getOrder());
      if (this.order.order_type != this.cartService.getOrderType()) {
        this.order.delivery_time = '';
      }
      this.order.order_type = this.cartService.getOrderType();
      this.deliveryDate = { year: new Date(this.order.delivery_date).getFullYear(), month: new Date(this.order.delivery_date).getMonth() + 1, day: new Date(this.order.delivery_date).getDate() };
    } else {
      // this.order.delivery_date = this.convertToDate(new Date());
      this.order.delivery_date = this.convertToDate(new Date());
      this.order.order_type = this.cartService.getOrderType();
      // Delivery Date Set With Min And Max Date
      this.deliveryDate = { year: new Date().getFullYear(), month: new Date().getMonth() + 1, day: new Date().getDate() };
    }
    this.minDate = { year: new Date().getFullYear(), month: new Date().getMonth() + 1, day: new Date().getDate() };
    if (new Date().getDate() > new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate() - 2) {
      let month = new Date().getMonth() + 2;
      let year = new Date().getFullYear();
      if (month > 12) {
        month = 1;
        year = new Date().getFullYear() + 1;
        this.maxDate = { year: year, month: 1, day: new Date(new Date().setDate(new Date().getDate() + 2)).getDate() };
      } else {
        this.maxDate = { year: year, month: month, day: new Date(new Date().setDate(new Date().getDate() + 2)).getDate() };
      }
    } else {
      this.maxDate = { year: new Date().getFullYear(), month: new Date().getMonth() + 1, day: new Date(new Date().setDate(new Date().getDate() + 2)).getDate() };
    }

    if (this.order.order_type == 'delivery') {
      this.fetchAddressBook();
    }

    this.modalOptions = {
      backdrop: 'static',
      backdropClass: 'customBackdrop',
      windowClass: 'rounded-modal'
    };

    //Catogories Find 
    this.fetchCategories();
    //Restaurant Find 
    this.fetchRestaurant();
    if (this.cartService.carts.length <= 0) {
      this.fetchCarts();
    } else {
      this.carts = this.cartService.carts;
    }

    //Time Slot Date Wise Find
    this.findTimeSlot();
  }

  fetchCategories() {
    this.isLoading = true;

    this.subs.add(
      this.categoryService
        .get({ nopaginate: 1, prefilled: 1, restaurant_id: this.restaurant_id, isSuggested: 1 })
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.categories = res;
            for (let cat of this.categories) {
              this.menus.concat(cat.menu);
            }
          },
          (err) => {
            this.categories = [];
          }
        )
    );
  }

  fetchCarts() {
    this.subs.add(
      this.cartService
        .get({ nopaginate: "1" })
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.carts = res;
            this.cartService.carts = this.carts;
          },
          (err) => {
            this.carts = [];
            this.router.navigateByUrl('/menu');
          }
        )
    );
  }

  fetchAddressBook() {
    this.subs.add(
      this.addressBookService
        .get({ customer_id: this.userId, restaurant_id: this.restaurant_id, nopaginate: "1" })
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            let filteredArray = []
            res.forEach(address => {
              if (address.delivery_status == 'delivery_available') {
                filteredArray.push(address);
              }
            });
            this.addressBooks = Object.assign([], filteredArray);
            return;
            // this.addressBooks = res.filter(item => item.delivery_status == 'delivery_available');
          },
          (err) => {
            this.addressBooks = [];
          }
        )
    );
  }

  fetchRestaurant() {
    this.isRestaurantLoading = true;
    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => {
        this.isLoading = false;
        this.isRestaurantLoading = false;
      }))
      .subscribe(res => {
        this.restaurant = res;
        if (this.restaurant.meta_title) {
          this.titleService.setTitle(this.restaurant.meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.meta_description });
        } else {
          this.titleService.setTitle(this.restaurant.site_setting.meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.site_setting.meta_keywords });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.site_setting.meta_description });
        }
        if (this.order.order_type == 'delivery') {
          if (this.restaurant.currentDeliveryStatus == 'Open') {
            this.order.assoonas = 'now';
          } else {
            this.order.assoonas = 'later';
          }
          this.restaurant.currentStatus = this.restaurant.currentDeliveryStatus;
        } else {
          if (this.restaurant.currentPickupStatus == 'Open') {
            this.order.assoonas = 'now';
          } else {
            this.order.assoonas = 'later';
          }
          this.restaurant.currentStatus = this.restaurant.currentPickupStatus;
        }
      }, err => this.error = err)
    );
  }

  updateToCart(cart: Cart, event: string) {

    if (event == 'add') {
      cart.quantity = cart.quantity + 1;
      cart.total_price = cart.menu_price * cart.quantity;
      this.subs.add(
        this.cartService.update(cart).
          pipe(finalize(() => { }))
          .subscribe(
            (res) => {
              this.carts = res;
              this.cartService.carts = this.carts;
            },
            (err) => {
              this.carts = [];
            }
          )
      )
    }

    if (event == 'remove') {
      var quantity = cart.quantity - 1;
      cart.total_price = cart.menu_price * cart.quantity;
      if (quantity > 0) {
        cart.quantity = quantity;
        this.subs.add(
          this.cartService.update(cart).
            pipe(finalize(() => { }))
            .subscribe(
              (res) => {
                this.carts = res;
                this.cartService.carts = this.carts;
              },
              (err) => {
                this.carts = [];
              }
            )
        )
      } else {
        this.subs.add(
          this.cartService.delete(cart.id).
            pipe(finalize(() => { }))
            .subscribe(
              (res) => {
                this.carts = res;
                this.cartService.carts = this.carts;
              },
              (err) => {
                this.fetchCarts();
              }
            )
        )
      }
    }

    if (event == 'delete') {
      this.subs.add(
        this.cartService.delete(cart.id).
          pipe(finalize(() => { }))
          .subscribe(
            (res) => {
              this.carts = res;
            },
            (err) => {
              this.fetchCarts();
            }
          )
      )
    }
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        let selectedSubAddons = "";
        for (let mainAddon of this.selectedVariant.main_addons) {

          for (let subAddon of mainAddon.sub_addons) {
            if (mainAddon.selectedSubAddonId == subAddon.id) {
            }
            if (subAddon.selected) {
            }
          }
        }
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  getGrandTotal() {
    let grand_total = 0;
    this.carts.forEach(item => {
      grand_total = grand_total + item.total_price;
    });
    return grand_total;
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  addItemToCart(model, menu, menuId, variantId) {
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }

    this.selectedMenu = menu;

    if (this.selectedMenu.menu_addon == 'No' && this.selectedMenu.price_option == 'single') {
      let cart = new Cart();
      cart.menu_id = this.selectedMenu.id;
      cart.restaurant_id = this.restaurant_id;
      cart.menu_name = this.selectedMenu.menu_name;
      cart.menu_price = this.selectedMenu.variants[0]?.orginal_price;
      cart.total_price = this.selectedMenu.variants[0]?.orginal_price;
      cart.quantity = 1;
      cart.customer_id = this.userId;

      this.uploadCart(cart);
    }
  }

  uploadCart(cart: Cart) {
    this.subs.add(
      this.cartService.create(cart).
        pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.carts = res;
            this.cartService.carts = this.carts;
          },
          (err) => {
            this.carts = [];
          }
        )
    )
  }

  convertNumber(event) {
    if (event > 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      var val1 = 'Free'
    }
    return val1
  }

  orderType(order_type: string) {
    this.order.order_type = order_type;
    this.cartService.saveOrderType(order_type);
  }

  onSelect(evt: any) {
    var deliveryDates = this.convertToDate(new Date(evt.year, evt.month - 1, evt.day));
    this.order.delivery_date = deliveryDates;
    this.findTimeSlot();
  }

  findTimeSlot() {
    this.subs.add(
      this.restaurantService.timeslot({ restaurant_id: this.restaurant_id, date: this.order.delivery_date }).
        pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            if (this.order.order_type == 'delivery') {
              var lastChar = res.delivery.today.slice(-1);
              if (lastChar == ',') {
                res.delivery.today = res.delivery.today.slice(0, -1); // trim last character
              }
              if (res.delivery.today != '') {
                this.timeSlots = res.delivery.today.split(',');
                this.order.delivery_time = this.timeSlots[0];
              } else {
                this.timeSlots = [];
                this.order.delivery_time = 'Close';
              }
              // this.restaurant.currentStatus = this.restaurant.currentDeliveryStatus
            } else {
              var lastChar = res.pickup.today.slice(-1);
              if (lastChar == ',') {
                res.pickup.today = res.pickup.today.slice(0, -1); // trim last character
              }
              if (res.pickup.today != '') {
                this.timeSlots = res.pickup.today.split(',');
                this.order.delivery_time = this.timeSlots[0]
              } else {
                this.timeSlots = [];
                this.order.delivery_time = 'Close';
              }
              // this.restaurant.currentStatus = this.restaurant.currentPickupStatus
            }
          },
          (err) => {
            this.carts = [];
          }
        )
    )
  }

  addAddress(model) {
    this.addressBookAdd = new AddressBook();
    this.openModal(model);
  }

  showDeliveryCharge(addressBook) {
    this.deliveryAddressId = addressBook.id;
    this.order.address_id = addressBook.id;
    this.order.address = addressBook.flat_no + ',' + addressBook.address;
    this.order.delivery_charge = addressBook.deliveryCharge.toFixed(2);
    this.order.flat_no = addressBook.flat_no;
    this.order.destination_latitude = addressBook.latitude;
    this.order.destination_longitude = addressBook.longitude;
  }

  deliveryNow(status) {
    this.order.assoonas = status;
    this.order.delivery_time = '';
    this.findTimeSlot();
  }

  findzipcode(postcode: string) {
    this.isPostcodeLoading = true;
    this.Postcodeerror = false;
    this.addressBookAdd.address = '';

    if (postcode == null || postcode == undefined) {
      this.Postcodeerror = 'Please enter valid postcode and press lookup button';
    } else {
      this.subs.add(this.userService.postcode(postcode)
        .pipe(finalize(() => this.isPostcodeLoading = false))
        .subscribe(res => {
          var address = '';
          if (res.street) {
            address += res.street;
          }
          if (res.post_town) {
            address += ',' + res.post_town;
          }
          if (res.post_code) {
            address += ',' + res.post_code;
          }

          this.addressBookAdd.latitude = res.latitude;
          this.addressBookAdd.longitude = res.longitude;
          this.addressBookAdd.zipcode = res.post_code;
          this.addressBookAdd.address = address;
        }, err => this.Postcodeerror = err)
      );
    }
  }

  validateAddress() {
    this.isModelLoading = true;
    this.Modelerror = false;

    if (this.addressBookAdd.zipcode == null) {
      this.Modelerror = 'Please enter valid postcode and press lookup button';
      this.isModelLoading = false;
    } else if (this.addressBookAdd.flat_no == null) {
      this.Modelerror = 'Please enter Flat no';
      this.isModelLoading = false;
    } else if (this.addressBookAdd.address == null) {
      this.Modelerror = 'Please enter valid postcode';
      this.isModelLoading = false;
    } else if (this.addressBookAdd.latitude == null) {
      this.Modelerror = 'Please enter valid postcode and press lookup button';
      this.isModelLoading = false;
    } else {
      this.addressBookAdd.user_id = this.userId;
      this.subs.add(
        this.addressBookService.create(this.addressBookAdd).
          pipe(finalize(() => this.isModelLoading = false))
          .subscribe(
            (res) => {
              this.fetchAddressBook();
              if (this.addressBooks.length > 0) {
                if (res.zipcode != this.addressBooks[0].zipcode) {
                  this.messagingService.error("Sorry your address is out of delivery")
                } else {
                  this.messagingService.success("Address added successfully !!");
                }
              }
            },
            (err) => {
              this.messagingService.error(err)
            }
          )
      )
      this.modalService.dismissAll();
    }
  }

  validatePrecheckout() {
    this.isPrecheckoutLoading = true;
    this.errorAddress = false;
    this.errorTime = false;

    let user = JSON.parse(this.userService.getUser());
    this.order.customer_id = user?.id;
    this.order.customer_name = user?.first_name + ' ' + user?.last_name;
    this.order.customer_email = user?.username;
    this.order.customer_phone = user?.phone_number;

    if (this.order.order_type == 'delivery') {
      if (this.restaurant.servicecharge_delivery) {
        if (this.restaurant.service_charge_type == 1) {
          this.order.service_charge = this.restaurant.service_charge
        } else {
          this.order.service_charge = (this.getGrandTotal() * this.restaurant.service_charge) / 100;
        }
      } else {
        this.order.service_charge = 0;
      }
    }
    if (this.order.order_type == 'pickup') {
      if (this.restaurant.servicecharge_picked) {
        if (this.restaurant.service_charge_type == 1) {
          this.order.service_charge = this.restaurant.service_charge
        } else {
          this.order.service_charge = (this.getGrandTotal() * this.restaurant.service_charge) / 100;
        }
      } else {
        this.order.service_charge = 0;
      }
      this.order.delivery_charge = 0;
    }
    this.order.source_latitude = this.restaurant.sourcelatitude
    this.order.source_longitude = this.restaurant.sourcelongitude
    this.order.order_sub_total = this.getGrandTotal();

    if (this.order.order_type == 'delivery') {
      if (this.deliveryAddressId && !this.order.address) {
        this.order.address = this.addressBooks[0].address + ',' + this.addressBooks[0].address;
        this.order.delivery_charge = this.addressBooks[0].deliveryCharge;
        this.order.flat_no = this.addressBooks[0].flat_no;
        this.order.destination_latitude = this.addressBooks[0].latitude;
        this.order.destination_longitude = this.addressBooks[0].longitude;
      }
      if (!this.deliveryAddressId && !this.order.address) {
        window.scroll(0, 0);
        this.errorAddress = 'Please select delivery address';
        this.isPrecheckoutLoading = false;
        return;
      }
    }
    if (!this.order.assoonas) {
      window.scroll(0, document.body.scrollHeight);
      this.errorTime = 'Please select ' + this.order.order_type + ' time status';
      this.isPrecheckoutLoading = false;
      return;
    } else {
      if (this.order.assoonas == 'now') {
        this.order.delivery_time = this.timeSlots[0];
      }
    }
    if (!this.order.delivery_date) {
      window.scroll(0, document.body.scrollHeight);
      this.errorTime = 'Please select ' + this.order.order_type + ' date';
      this.isPrecheckoutLoading = false;
      return;
    }
    if ((!this.order.delivery_time || this.order.delivery_time == 'Close') && this.order.assoonas == 'later') {
      window.scroll(0, document.body.scrollHeight);
      this.errorTime = 'Please select ' + this.order.order_type + ' time';
      this.isPrecheckoutLoading = false;
      return;
    }

    if (!this.errorAddress && !this.errorTime) {
      localStorage.removeItem(environment.order);
      this.cartService.saveOrder(this.order);
      this.isPrecheckoutLoading = false;
      this.router.navigateByUrl('/checkout');
    }

  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd', 'en_US')
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
