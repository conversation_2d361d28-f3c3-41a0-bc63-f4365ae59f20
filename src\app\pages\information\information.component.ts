import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import {
  NgbModal,
  ModalDismissReasons,
  NgbModalOptions,
  NgbActiveModal,
} from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { DomSanitizer, Meta, SafeResourceUrl, Title } from '@angular/platform-browser';

@Component({
  selector: 'app-information',
  templateUrl: './information.component.html',
  styleUrls: ['./information.component.scss'],
})
export class InformationComponent implements OnInit, OnDestroy {
  subs = new Subscription();

  isLoading = false;
  error = null;
  success = false;

  user: User;
  restaurant: Restaurant = new Restaurant();
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;
  streetAddress: SafeResourceUrl;

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private restaurantService: RestaurantService,
    private currencyPipe: CurrencyPipe,
    private metaTagService: Meta,
    private titleService: Title,
    public sanitizer: DomSanitizer,
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    this.modalOptions = {
      backdrop: 'static',
      size: 'lg',
      backdropClass: 'customBackdrop',
    };
    //Restaurant Find 
    this.fetchRestaurant();
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
        if (this.restaurant.street_address) {
          let address = this.restaurant.street_address.split(' ').join('+');
          let addresses = 'https://maps.google.com/maps?f=q&source=s_q&hl=en&geocode=&q='
            + address.split(',').join('') + '&z=14&output=embed';
          this.streetAddress = this.sanitizer.bypassSecurityTrustResourceUrl(addresses);
        }

        if (this.restaurant.meta_tag.info_meta_title) {
          this.titleService.setTitle(this.restaurant.meta_tag.info_meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.meta_tag.info_meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.meta_tag.info_meta_description });
        }
      }, err => this.error = err)
    );
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd', 'en_US')
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
