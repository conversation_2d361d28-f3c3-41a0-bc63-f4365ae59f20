<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-sm-12 col-md-12">

      <div class="col-md-12 bg-white rounded body-box-shadow p-3">
        <h3>About Restaurant</h3>
        <span>{{restaurant.restaurant_about}}</span>
      </div>

      <div class="col-md-12 bg-white rounded body-box-shadow p-3 mt-3"
        *ngIf="restaurant.alcahol_available || restaurant.alcahol_not_available || restaurant.alcahol_available || restaurant.soft_drink">
        <h3>Drink Service</h3>
        <div class="d-md-flex">
          <div class="col-xs-12 col-md-3" *ngIf="restaurant.alcahol_available">✔ Alcohol Available</div>
          <div class="col-xs-12 col-md-3" *ngIf="restaurant.alcahol_not_available">❌ Alcohol Not Allowed
          </div>
          <div class="col-xs-12 col-md-3" *ngIf="restaurant.bring_your_alcahol">✔ Bring Your Own Alcohol</div>
          <div class="col-xs-12 col-md-3" *ngIf="restaurant.soft_drink">✔ Soft Drinks Served</div>
        </div>
      </div>

      <div class="col-md-12 bg-white rounded body-box-shadow p-3 mt-3"
        *ngIf="streetAddress && this.restaurant.street_address">
        <h3>Get Directions</h3>
        <iframe width="100%" frameborder="0" height="350" frameBorder="0" [src]="streetAddress"
          allowfullscreen></iframe>
      </div>

      <div class="col-md-12 col-xs-12 d-md-flex">
        <div class="col-md-6 col-xs-12 p-1 mt-3" *ngIf="restaurant.restaurant_delivery == 'Yes'">
          <div class="bg-white rounded body-box-shadow p-3">
            <h3>Delivery Opening Hours</h3>
            <!-- Monday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Monday</span>
              <span class="col-sm-9 col-md-9 text-end" *ngIf="restaurant.monday_status != 'Close'">
                <span
                  *ngIf="restaurant.monday_first_opentime != '12:00 PM' || restaurant.monday_first_closetime != '12:00 PM'">
                  {{restaurant.monday_first_opentime}} - {{restaurant.monday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.monday_second_opentime != '12:00 PM' || restaurant.monday_second_closetime != '12:00 PM'">
                  {{restaurant.monday_second_opentime}} - {{restaurant.monday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.monday_status == 'Close'">Closed</span>
            </div>

            <!-- Tuesday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Tuesday</span>
              <span class="col-sm-9 col-md-9 text-end" *ngIf="restaurant.tuesday_status != 'Close'">
                <span
                  *ngIf="restaurant.tuesday_first_opentime != '12:00 PM' || restaurant.tuesday_first_closetime != '12:00 PM'">
                  {{restaurant.tuesday_first_opentime}} - {{restaurant.tuesday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.tuesday_second_opentime != '12:00 PM' || restaurant.tuesday_second_closetime != '12:00 PM'">
                  {{restaurant.tuesday_second_opentime}} - {{restaurant.tuesday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.tuesday_status == 'Close'">Closed</span>
            </div>

            <!-- Wednesday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Wednesday</span>
              <span class="col-sm-9 col-md-9 text-end" *ngIf="restaurant.wednesday_status != 'Close'">
                <span
                  *ngIf="restaurant.wednesday_first_opentime != '12:00 PM' || restaurant.wednesday_first_closetime != '12:00 PM'">
                  {{restaurant.wednesday_first_opentime}} - {{restaurant.wednesday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.wednesday_second_opentime != '12:00 PM' || restaurant.wednesday_second_closetime != '12:00 PM'">
                  {{restaurant.wednesday_second_opentime}} - {{restaurant.wednesday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.wednesday_status == 'Close'">Closed</span>
            </div>

            <!-- Thursday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Thursday</span>
              <span class="col-sm-9 col-md-9 text-end" *ngIf="restaurant.thursday_status != 'Close'">
                <span
                  *ngIf="restaurant.thursday_first_opentime != '12:00 PM' || restaurant.thursday_first_closetime != '12:00 PM'">
                  {{restaurant.thursday_first_opentime}} - {{restaurant.thursday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.thursday_second_opentime != '12:00 PM' || restaurant.thursday_second_closetime != '12:00 PM'">
                  {{restaurant.thursday_second_opentime}} - {{restaurant.thursday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.thursday_status == 'Close'">Closed</span>
            </div>

            <!-- Friday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Friday</span>
              <span class="col-sm-9 col-md-9 text-end" *ngIf="restaurant.friday_status != 'Close'">
                <span
                  *ngIf="restaurant.friday_first_opentime != '12:00 PM' || restaurant.friday_first_closetime != '12:00 PM'">
                  {{restaurant.friday_first_opentime}} - {{restaurant.friday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.friday_second_opentime != '12:00 PM' || restaurant.friday_second_closetime != '12:00 PM'">
                  {{restaurant.friday_second_opentime}} - {{restaurant.friday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.friday_status == 'Close'">Closed</span>
            </div>

            <!-- Saturday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Saturday</span>
              <span class="col-sm-9 col-md-9 text-end" *ngIf="restaurant.saturday_status != 'Close'">
                <span
                  *ngIf="restaurant.saturday_first_opentime != '12:00 PM' || restaurant.saturday_first_closetime != '12:00 PM'">
                  {{restaurant.saturday_first_opentime}} - {{restaurant.saturday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.saturday_second_opentime != '12:00 PM' || restaurant.saturday_second_closetime != '12:00 PM'">
                  {{restaurant.saturday_second_opentime}} - {{restaurant.saturday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.saturday_status == 'Close'">Closed</span>
            </div>

            <!-- Sunday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Sunday</span>
              <span class="col-sm-9 col-md-9 text-end" *ngIf="restaurant.sunday_status != 'Close'">
                <span
                  *ngIf="restaurant.sunday_first_opentime != '12:00 PM' || restaurant.sunday_first_closetime != '12:00 PM'">
                  {{restaurant.sunday_first_opentime}} - {{restaurant.sunday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.sunday_second_opentime != '12:00 PM' || restaurant.sunday_second_closetime != '12:00 PM'">
                  {{restaurant.sunday_second_opentime}} - {{restaurant.sunday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.sunday_status == 'Close'">Closed</span>
            </div>
          </div>
        </div>

        <div class="col-md-6 col-xs-12 p-1 mt-3" *ngIf="restaurant.restaurant_pickup == 'Yes'">
          <div class="bg-white rounded body-box-shadow p-3">
            <h3>Pickup Opening Hours</h3>
            <!-- Monday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Monday</span>
              <span class="col-sm-9 col-md-9 text-end"
                *ngIf="restaurant.restaurant_timing.pick_monday_status != 'Close'">
                <span
                  *ngIf="restaurant.restaurant_timing.pick_monday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_monday_first_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_monday_first_opentime}} -
                  {{restaurant.restaurant_timing.pick_monday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.restaurant_timing.pick_monday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_monday_second_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_monday_second_opentime}} -
                  {{restaurant.restaurant_timing.pick_monday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.restaurant_timing.pick_monday_status == 'Close'">Closed</span>
            </div>

            <!-- Tuesday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Tuesday</span>
              <span class="col-sm-9 col-md-9 text-end"
                *ngIf="restaurant.restaurant_timing.pick_tuesday_status != 'Close'">
                <span
                  *ngIf="restaurant.restaurant_timing.pick_tuesday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_tuesday_first_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_tuesday_first_opentime}} -
                  {{restaurant.restaurant_timing.pick_tuesday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.restaurant_timing.pick_tuesday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_tuesday_second_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_tuesday_second_opentime}} -
                  {{restaurant.restaurant_timing.pick_tuesday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.restaurant_timing.pick_tuesday_status == 'Close'">Closed</span>
            </div>

            <!-- Wednesday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Wednesday</span>
              <span class="col-sm-9 col-md-9 text-end"
                *ngIf="restaurant.restaurant_timing.pick_wednesday_status != 'Close'">
                <span
                  *ngIf="restaurant.restaurant_timing.pick_wednesday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_wednesday_first_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_wednesday_first_opentime}} -
                  {{restaurant.restaurant_timing.pick_wednesday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.restaurant_timing.pick_wednesday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_wednesday_second_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_wednesday_second_opentime}} -
                  {{restaurant.restaurant_timing.pick_wednesday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.restaurant_timing.pick_wednesday_status == 'Close'">Closed</span>
            </div>

            <!-- Thursday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Thursday</span>
              <span class="col-sm-9 col-md-9 text-end"
                *ngIf="restaurant.restaurant_timing.pick_thursday_status != 'Close'">
                <span
                  *ngIf="restaurant.restaurant_timing.pick_thursday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_thursday_first_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_thursday_first_opentime}} -
                  {{restaurant.restaurant_timing.pick_thursday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.restaurant_timing.pick_thursday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_thursday_second_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_thursday_second_opentime}} -
                  {{restaurant.restaurant_timing.pick_thursday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.restaurant_timing.pick_thursday_status == 'Close'">Closed</span>
            </div>

            <!-- Friday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Friday</span>
              <span class="col-sm-9 col-md-9 text-end"
                *ngIf="restaurant.restaurant_timing.pick_friday_status != 'Close'">
                <span
                  *ngIf="restaurant.restaurant_timing.pick_friday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_friday_first_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_friday_first_opentime}} -
                  {{restaurant.restaurant_timing.pick_friday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.restaurant_timing.pick_friday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_friday_second_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_friday_second_opentime}} -
                  {{restaurant.restaurant_timing.pick_friday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.restaurant_timing.pick_friday_status == 'Close'">Closed</span>
            </div>

            <!-- Saturday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Saturday</span>
              <span class="col-sm-9 col-md-9 text-end"
                *ngIf="restaurant.restaurant_timing.pick_saturday_status != 'Close'">
                <span
                  *ngIf="restaurant.restaurant_timing.pick_saturday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_saturday_first_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_saturday_first_opentime}} -
                  {{restaurant.restaurant_timing.pick_saturday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.restaurant_timing.pick_saturday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_saturday_second_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_saturday_second_opentime}} -
                  {{restaurant.restaurant_timing.pick_saturday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.restaurant_timing.pick_saturday_status == 'Close'">Closed</span>
            </div>

            <!-- Sunday -->
            <div class="col-sm-12 col-md-12 d-flex justify-content-between py-1">
              <span class="col-sm-3 col-md-3 text-start">Sunday</span>
              <span class="col-sm-9 col-md-9 text-end"
                *ngIf="restaurant.restaurant_timing.pick_sunday_status != 'Close'">
                <span
                  *ngIf="restaurant.restaurant_timing.pick_sunday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_sunday_first_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_sunday_first_opentime}} -
                  {{restaurant.restaurant_timing.pick_sunday_first_closetime}}
                </span>
                <span
                  *ngIf="restaurant.restaurant_timing.pick_sunday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_sunday_second_closetime != '12:00 PM'">
                  {{restaurant.restaurant_timing.pick_sunday_second_opentime}} -
                  {{restaurant.restaurant_timing.pick_sunday_second_closetime}}
                </span>
              </span>
              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                *ngIf="restaurant.restaurant_timing.pick_sunday_status == 'Close'">Closed</span>
            </div>
          </div>
        </div>
      </div>


    </div>

  </div>
</div>