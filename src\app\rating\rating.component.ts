import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-rating',
  templateUrl: './rating.component.html',
  styleUrls: ['./rating.component.scss']
})
export class RatingComponent implements OnInit {

  @Input() stars: number = 5;
  @Input() rating: number = 0;
  @Input() viewOnly: boolean = false;
  @Output() ratingChange = new EventEmitter<number>();
  @Output() onChange = new EventEmitter<number>();

  // stars: number[] = [1, 2, 3, 4, 5];
  selectedValue: number = 0;
  allStars = [];
  constructor() { }

  ngOnInit(): void {
  }
  ngOnChanges() {
    this.selectedValue = this.rating ? this.rating : 0
    var tempNumStars = Array(+this.stars)
    this.allStars = [];
    for (var index = 0; index < tempNumStars.length; index++) {
      this.allStars.push({ id: index + 1, name: index + 1, checked: (this.selectedValue > 0 && this.selectedValue - 1 >= index ? true : false) })
    }
  }

  countStar(star) {
    this.selectedValue = star;
  }

  onStarClick(rating) {
    if (!this.viewOnly) {
      this.selectedValue = rating;
      this.onChange.emit(this.selectedValue);
      this.ratingChange.emit(this.selectedValue);
    }
  }
  addClass(star) {
    if (this.viewOnly) return
    let ab = "";
    for (let i = 1; i <= star; i++) {
      ab = "starId_" + i;
      document.getElementById(ab).classList.add("selected");
    }
  }

  removeClass(star) {
    if (this.viewOnly) return
    star = star - 1
    let ab = "";
    for (let i = star; i >= this.selectedValue; i--) {
      ab = "starId_" + i;
      document.getElementById(ab).classList.remove("selected");
    }
  }
}
