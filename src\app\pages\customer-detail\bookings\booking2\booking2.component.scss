.order-card {
  border-radius: 1rem;
}

.view-btn {
  background-color: var(--primary);
  color: #fff;
  border: 1px solid var(--primary);
  padding: 0.25rem 0.75rem;
  border-radius: 2rem;
  font-size: 1rem;
  font-weight: 500;

  &:hover {
    color: var(--primary);
    border: 1px solid var(--primary);
    background-color: #fff;
  }
}

::ng-deep .booking2-pagination .pagination {
  justify-content: center;

  .page-item:first-child .page-link {
    border-radius: 1rem 0rem 0rem 1rem !important;
  }

  .page-item:last-child .page-link {
    border-radius: 0rem 1rem 1rem 0rem !important;
  }
}
