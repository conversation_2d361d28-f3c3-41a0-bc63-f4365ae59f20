import { Menu } from "./menu";

export class Cart {
  id: string;
  order_id: string;
  menu_id: string;
  restaurant_id: string;
  menu_name: string;
  subaddons_name: string;

  menu_price: number;
  offer_price: number;
  total_price: number;
  quantity: number;
  customer_id: string;
  menu: Menu;

  created: string;
  updated: string;

  // New Variables
  image_url: string;

  static toFormData(cart: Cart) {
    console.log(`cart in model`, cart);
    const formData = new FormData();

    // if (cart.id) formData.append('id', cart.id);
    if (cart.restaurant_id) formData.append('restaurant_id', cart.restaurant_id);
    console.log("Yaha <PERSON>rror <PERSON>gti hai");
    if (cart.customer_id) formData.append('customer_id', cart.customer_id);
    if (cart.menu_id) formData.append('menu_id', cart.menu_id);
    if (cart.menu_name) formData.append('menu_name', cart.menu_name);
    if (cart.subaddons_name) formData.append('subaddons_name', cart.subaddons_name);
    if (cart.menu_price) formData.append('menu_price', cart.menu_price.toString());
    if (cart.total_price) formData.append('total_price', cart.total_price.toString());
    if (cart.quantity) formData.append('quantity', cart.quantity.toString());


    return formData;
  }
  static toBulformdata(carts: Cart[]) {
    const formData = new FormData();

    if (carts.length > 0) {
      let finalCarts: Cart[] = []
      carts.forEach(cart => {
        var cartObject: Cart = new Cart();
        Cart.toFormData(cart).forEach(function (value, key) {
          cartObject[key] = value;
        });
        finalCarts.push(cartObject);
      });
      formData.append('carts', JSON.stringify(finalCarts));
    }

    return formData;
  }
}
