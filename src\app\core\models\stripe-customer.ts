export class StripeCustomer {
  id: string;
  customer_id: string;
  customer_name: string;
  stripe_token_id: string;
  stripe_customer_id: string;
  card_id: string;
  card_number: string;
  card_brand: string;
  card_type: string;
  service_type: string;
  exp_month: string;
  exp_year: string;
  country: string;
  client_ip: string;
  amount: number;
  restaurant_id: string;
  order_id: string;
  booking_id: string;

  created: string;
  updated: string;

  static toFormData(card: StripeCustomer) {
    const formData = new FormData();

    if (card.id) formData.append('id', card.id);
    if (card.customer_id) formData.append('customer_id', card.customer_id);
    if (card.customer_name) formData.append('customer_name', card.customer_name);
    if (card.stripe_token_id) formData.append('stripe_token_id', card.stripe_token_id);
    if (card.stripe_customer_id) formData.append('stripe_customer_id', card.stripe_customer_id);
    if (card.card_id) formData.append('card_id', card.card_id);
    if (card.card_number) formData.append('card_number', card.card_number);
    if (card.card_brand) formData.append('card_brand', card.card_brand);
    if (card.card_type) formData.append('card_type', card.card_type);
    if (card.exp_month) formData.append('exp_month', card.exp_month);
    if (card.exp_year) formData.append('exp_year', card.exp_year);
    if (card.country) formData.append('country', card.country);
    if (card.client_ip) formData.append('client_ip', card.client_ip);
    if (card.amount) formData.append('amount', card.amount.toString());
    if (card.restaurant_id) formData.append('restaurant_id', card.restaurant_id);
    if (card.service_type) formData.append('service_type', card.service_type);
    if (card.order_id) formData.append('order_id', card.order_id);
    if (card.booking_id) formData.append('booking_id', card.booking_id);
    return formData;
  }

}
