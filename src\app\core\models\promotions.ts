
export class Promotions {
  id: string;
  banner_image: string;
  promo_image: string;
  restaurant_id: string;
  image_url: string;

  created_at: string;
  updated_at: string;

  imageFile: any;

  static toFormData(promotions: Promotions) {
    const formData = new FormData();

    if (promotions.id) formData.append('id', promotions.id);
    if (promotions.restaurant_id) formData.append('restaurant_id', promotions.restaurant_id);
    if (promotions.promo_image) formData.append('promo_image', promotions.promo_image);
    if (promotions.imageFile) formData.append('image', promotions.imageFile, promotions.imageFile.name)

    return formData;
  }
}
