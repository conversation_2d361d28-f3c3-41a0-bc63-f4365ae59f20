import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { RequestInterceptor } from './request.interceptor';
import { PaginationComponent } from '../pagination/pagination.component';
import { RatingComponent } from '../rating/rating.component';
import { AppLoaderComponent } from './app-loader/app-loader.component';
import { SlickCarouselModule } from 'ngx-slick-carousel';
import { MessagingComponent } from './messaging/messaging.component';
import { NgOtpInputModule } from 'ng-otp-input';

@NgModule({
  declarations: [PaginationComponent, RatingComponent, AppLoaderComponent, MessagingComponent],
  imports: [CommonModule, FormsModule, HttpClientModule, SlickCarouselModule, NgOtpInputModule],
  exports: [CommonModule, FormsModule, PaginationComponent, RatingComponent, AppLoaderComponent, SlickCarouselModule, MessagingComponent, NgOtpInputModule],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: RequestInterceptor, multi: true },
  ],
})
export class SharedModule { }
