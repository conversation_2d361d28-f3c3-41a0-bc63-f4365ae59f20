import { Component, OnInit } from '@angular/core';
import { User } from '../core/models/user';
import { UserService } from '../core/services/user.service';
import { SocialAuthService, GoogleLoginProvider, SocialUser } from 'angularx-social-login';
@Component({
  selector: 'app-shell',
  templateUrl: './shell.component.html',
  styleUrls: ['./shell.component.scss']
})
export class ShellComponent implements OnInit {

  user: User
  userId: string;
  userFirstName: string;

  isCollapsed = false;

  constructor(
    public userService: UserService,
    private socialAuthService: SocialAuthService
  ) { }

  ngOnInit(): void {
    let user = this.userService.user;

    this.userId = user?.id
    this.userFirstName = user?.first_name
  }

  logout() {
    this.socialAuthService.signOut();
    this.userService.logout();
  }

}
