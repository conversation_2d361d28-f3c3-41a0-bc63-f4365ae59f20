<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-12" *ngIf="orders.length > 0">
      <h4 class="mb-3">Order History</h4>

      <div class="order-card border shadow p-3 mb-3" *ngFor="let order of orders">

        <!-- Order Header -->
        <div class="d-flex justify-content-between align-items-center mb-2">
          <div>
            <h6 class="mb-0 fw-bold">
              {{order.delivery_date}} • {{order.delivery_time}}
            </h6>
            <small class="text-muted">
              #{{order.order_number}} &nbsp;|&nbsp; Total: {{convertNumber(order.order_grand_total)}}
            </small>
          </div>
          <span class="badge px-3 py-2 rounded-pill" [ngClass]="{
          'bg-success': order.status === 'Delivered',
          'bg-warning': order.status === 'Pending',
          'bg-danger': order.status === 'Failed'
        }">
            {{ order.status === 'Failed' ? 'Rejected' : order.status }}
          </span>
        </div>

        <!-- Actions -->
        <div class="d-flex flex-wrap gap-2 mt-2">
          <button class="view-btn" (click)="orderView(order.id)">
            <i class="fas fa-eye"></i> View
          </button>

          <button *ngIf="order.status === 'Delivered' && !order.reviews" class="view-btn"
            (click)="addReview(reviewModal,order)">
            <i class="fas fa-star"></i> Review
          </button>

          <button *ngIf="order.status === 'Delivered'" class="reorder-btn" (click)="reOrderCreate(order.id)">
            <i class="fas fa-redo"></i> Re-Order
          </button>

          <button *ngIf="order.status === 'Delivered' && false" class="complaint-btn">
            <i class="fas fa-exclamation-circle"></i> Complaint
          </button>
        </div>
      </div>

      <!-- Pagination -->
      <app-pagination *ngIf="orders.length > 0" [total_items]="totalOrders" [per_page]="per_page"
        [(current_page)]="page" (onChange)="loadPage($event)" class="mt-3 order2-pagination">
      </app-pagination>
    </div>

    <div class="col-md-12 empty-cart-cls text-center py-5" *ngIf="orders.length <= 0">
      <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
      <p><strong>No order(s) Found</strong></p>
    </div>
  </div>
</div>

<ng-template #reviewModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Review
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group d-flex">
      <label class="col-md-4">Rating</label>
      <div class="col-md-8">
        <app-rating [stars]="'5'" [(rating)]="reviewAdd.rating" class="pt-0 mt-0"></app-rating>
      </div>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-4">Message</label>
      <div class="col-sm-8">
        <textarea rows="3" class="form-control" nz-input [(ngModel)]="reviewAdd.message" name="message" id="message"
          placeholder="Enter Your Message"> </textarea>
      </div>
    </div>
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isModelLoading" class="btn btn-outline-dark cursor bg-primary text-white"
      (click)="validateReview(reviewModal)">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Submit
    </button>

  </div>
</ng-template>