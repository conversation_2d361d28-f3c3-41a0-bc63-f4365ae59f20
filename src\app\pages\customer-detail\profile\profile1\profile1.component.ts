import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { Component, On<PERSON>estroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { NgForm } from '@angular/forms';
import { NotificationService } from 'src/app/core/services/notification.service';
import { MessagingService } from 'src/app/core/services/messaging.service';

@Component({
  selector: 'app-profile1',
  templateUrl: './profile1.component.html',
  styleUrls: ['./profile1.component.scss']
})
export class Profile1Component implements OnInit {
  @ViewChild('changePasswordForm', { static: false }) changePasswordForm: NgForm;

  subs = new Subscription();
  isLoading = false;
  isProfileLoading = false;
  isChangePasswordLoading = false;
  error = null;
  errorMessage = null;
  errorChangePassword = null;

  isModelLoading = false;
  Modelerror = null;

  user: User;
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;
  previousPage: any;

  restaurant: Restaurant = new Restaurant();

  options = { query: null, page: 1, per_page: 10, customer_id: null };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private currencyPipe: CurrencyPipe,
    private restaurantService: RestaurantService,
    private notificationService: NotificationService,
    private messagingService: MessagingService,
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    this.options.customer_id = user?.id;
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    this.modalOptions = {
      backdrop: 'static',
      size: 'lg',
      backdropClass: 'customBackdrop',
    };
    this.fetchRestaurant();
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
      }, err => this.error = err)
    );
  }

  updateUser(form: NgForm) {
    this.isProfileLoading = true;
    this.errorMessage = null;

    this.user.password = '';
    this.user.confirmPassword = '';
    this.user.current_password = '';

    if (this.userService.user.phone_number != this.user.phone_number) {
      this.user.phone_verify = false;

      this.userService
        .disabledPhoneVerify(this.user)
        .pipe(finalize(() => (this.isProfileLoading = false)))
        .subscribe(
          (res) => { },
          (err) => { }
        );
    }

    this.userService
      .update(this.user)
      .pipe(finalize(() => (this.isProfileLoading = false)))
      .subscribe(
        (res) => {
          this.subs.add(this.userService.me()
            .pipe(finalize(() => this.isProfileLoading = false))
            .subscribe(res => {
              this.messagingService.success("Profile updated successfully !!");
              this.user = res
              this.userService.saveUser(res);
            }, err => this.errorMessage = err)
          );
        },
        (err) => {
          this.errorMessage = err;
        }
      );
  }

  onFileChanged(event) {
    const file = event.target.files[0]
  }

  changePassword(form: NgForm) {
    this.isChangePasswordLoading = true;
    this.errorChangePassword = null;

    if (this.user.password != this.user.confirmPassword) {
      this.errorChangePassword = 'New Password and confirm password not matched';
      this.isChangePasswordLoading = false;
    } else {
      this.userService
        .changepassword(this.user)
        .pipe(finalize(() => (this.isChangePasswordLoading = false)))
        .subscribe(
          (res) => {
            this.messagingService.success("Password changed successfully. !!");
            this.changePasswordForm.resetForm();
          },
          (err) => {
            this.errorChangePassword = err;
          }
        );
    }
  }

  keyPress(event: any) {
    const pattern = /[0-9]/;

    let inputChar = String.fromCharCode(event.charCode);
    if (event.keyCode != 8 && !pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}

