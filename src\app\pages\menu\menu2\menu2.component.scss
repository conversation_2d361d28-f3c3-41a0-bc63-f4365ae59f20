::ng-deep .rounded-modal > .modal-dialog > .modal-content {
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.main-section {
  position: relative;
}

.menu-list {
  background: #ffff;
  border-radius: 8px;
  padding: 8px;
  position: sticky;
  top: 20px;
  max-height: 95dvh;
  overflow-y: scroll;

  .menu-item {
    padding: 10px 16px;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #000;
    font-size: 18px;
    font-weight: 600;
    border-left: 6px solid #fff;

    &.active {
      background: #fff;
      color: var(--primary);
      border-left: 6px solid var(--primary);
    }

    &:last-child {
      border-bottom: none;
    }
  }
}

.menu-list::-webkit-scrollbar {
  width: 5px;
}

.menu-list::-webkit-scrollbar-track {
  border-radius: 1rem;
}

.menu-list::-webkit-scrollbar-thumb {
  border-radius: 1rem;
}

.menu-list::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

.floating-mobile-menu {
  position: fixed;
  bottom: 24px;
  left: 24px;
  z-index: 1000;

  .menu-button {
    background: var(--primary);
    color: #fff;
    padding: 16px 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    box-shadow: 0 8px 32px #0003;
    transition: all 0.3s ease;
    max-width: fit-content;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25);
    }

    .cart-icon {
      position: relative;

      i {
        font-size: 1.5rem;
      }
    }
  }

  .mobile-menu-details {
    background: #fffffff2;
    backdrop-filter: blur(10px);
    border-radius: 16px;
    margin: 12px 0px 12px 0px;
    box-shadow: 0 8px 32px #0000001a;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    width: 18rem;

    &.expanded {
      max-height: 400px;
      padding: 20px;
      overflow-y: scroll;
    }

    .menu-item {
      padding: 10px 16px;
      border-bottom: 1px solid #f5f5f5;
      cursor: pointer;
      transition: all 0.3s ease;
      color: #000;
      font-size: 18px;
      font-weight: 600;
      border-left: 6px solid #fff;

      &.active {
        background: #fff;
        color: var(--primary);
        border-left: 6px solid var(--primary);
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

.menu-details {
  background: #ffff;
  border-radius: 8px;
  padding: 14px 14px 28px 14px;

  .menu-name {
    color: #000;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 14px;

    .description {
      font-size: 14px;
      font-weight: 600;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;

      /* Firefox fallback */
      line-height: 1.4em;
      max-height: 2.8em;
    }
  }

  .menu-items {
    display: flex;
    flex-wrap: wrap;
    row-gap: 18px;
    justify-content: space-between;

    .menu-item-wrapper {
      background: var(--primary);
      width: 49%;
      min-width: 49%;
      max-width: 49%;
      border-radius: 10px;

      @media screen and (max-width: 991.98px) {
        width: 100%;
        min-width: 100%;
        max-width: 100%;
      }

      .menu-item {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        background-color: #fff;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #000;
        font-size: 16px;
        font-weight: 600;
        margin-left: 4px;
        height: 100%;
        position: relative;

        .details-container {
          display: flex;
          flex-direction: column;
          padding: 10px;
          height: 100%;
          width: calc(100% - 10rem);

          .name {
            font-size: 16px;
            font-weight: 600;

            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;

            /* Firefox fallback */
            display: -moz-box;
            -moz-box-orient: vertical;
            -moz-line-clamp: 1;
          }

          .description {
            font-size: 12px;
            font-weight: 600;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;

            /* Firefox fallback */
            display: -moz-box;
            -moz-box-orient: vertical;
            -moz-line-clamp: 2;
          }
        }

        .image-container {
          border-radius: 0px 10px 10px 0px;
          width: 10rem;
          min-width: 10rem;

          @media screen and (max-width: 991.98px) {
            width: 8rem;
            min-width: 8rem;
          }

          .menu-item-image {
            // width: 100%;
            // max-width: -webkit-fill-available;
            // max-width: -moz-available;
            width: 10rem;
            min-width: 10rem;

            @media screen and (max-width: 991.98px) {
              width: 8rem;
              min-width: 8rem;
            }

            height: 100%;
            object-fit: cover;
            border-radius: 0 10px 10px 0;

            position: absolute;
            top: 50%;
            transform: translateY(-50%);
          }
        }

        .item-count {
          position: absolute;
          right: -8px;
          top: -8px;
          background: #000;
          color: #fff;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
        }
      }
    }
  }
}

.add-to-cart {
  position: absolute;
  right: 10px;
  bottom: 10px;
  border-radius: 50%;
  color: #fff;
  background: var(--primary);
  border: none;
  padding: 4px 8px;
  font-size: 14px;
  cursor: pointer;
}

.add-variant-to-cart {
  border-radius: 50%;
  color: #fff;
  background: var(--primary);
  border: none;
  padding: 4px 8px;
  font-size: 14px;
  cursor: pointer;
}

.basket-fixed {
  position: fixed;
  bottom: 30px;
  right: 60px;
  z-index: 1000;
}

.basket-button {
  display: flex;
  column-gap: 2rem;
  align-items: center;
  background-color: var(--primary);
  border: none;
  border-radius: 1rem;
  padding: 0.8rem 1rem;
  color: white;
  font-family: sans-serif;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.basket-info {
  display: flex;
  flex-direction: column;
  margin-right: 1rem;
  font-size: 0.85rem;
  text-align: left;
  line-height: 1.2;
}

.basket-price {
  font-weight: bold;
  font-size: 1rem;
}

.basket-label {
  font-weight: bold;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.basket-label i {
  font-size: 1.2rem;
}

.modal-content {
  border-radius: 10px;
}

.modal-footer .btn-warning {
  background-color: #ff6b00;
  border-color: #ff6b00;
}

.modal-footer .btn-outline-secondary {
  background-color: #6c757d;
  color: #fff;
  border: none;
}

.banner-wrapper {
  position: relative;

  .banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
  }

  .menu_banner {
    width: 100%;
    height: 240px;
    object-fit: cover;

    @media screen and (max-width: 768px) {
      height: 180px;
    }
  }
}

.restaurant_details {
  display: flex;
  flex-direction: column;
  margin: -60px auto auto;
  height: 210px;
  border-radius: 1rem;
  border: 1px solid #ebebeb;
  background: #fff;

  @media screen and (max-width: 991.98px) {
    height: auto;
  }

  .restaurant-name-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0rem 1rem;
    margin-bottom: 1rem;

    @media screen and (max-width: 400px) {
      padding: 0rem 0.5rem;
      flex-direction: column;
    }

    .name {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }

    .info {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      row-gap: 0.5rem;
      column-gap: 0.5rem;

      @media screen and (max-width: 400px) {
        flex-direction: row;
      }
    }
  }

  .restaurant_logo {
    text-align: center;
    width: fit-content;
    margin: -45px auto 0;
    border: 2px solid #f1f1f1;
    border-radius: 1rem;
    background: #fff;

    img {
      width: 80px;
      height: 80px;
      object-fit: contain;
    }
  }

  .delivery-pickup-wrapper {
    display: flex;
    align-items: center;
    column-gap: 0.5rem;
    border-radius: 2rem;
    padding: 3px;
    background: #efefef;

    position: relative;

    .delivery-pickup-slider {
      position: absolute;
      background: var(--primary);
      width: 10rem;
      padding: 0.60rem 2rem;
      border-radius: 2rem;

      transition: all 0.3s ease;

      &.delivery-active {
        left: 5px;
      }

      &.pickup-active {
        left: 168px;
      }
    }

    .delivery-btn,
    .pickup-btn {
      color: #000;
      background: transparent;
      border: none;
      // border: 1px solid transparent;
      padding: 0.25rem 2rem;
      border-radius: 2rem;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 600;

      transition: all 0.3s ease;

      width: 10rem;
      z-index: 1;

      &.active {
        color: #fff;
        // background: var(--primary);
        // border: 1px solid #fff;
      }

      @media screen and (max-width: 400px) {
        padding: 1px 18px;
      }
    }
  }
}

.restaurant-loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 3rem;
}

.recommend-menu {
  display: flex;
  flex-direction: column;
  background: var(--primary);
  border-radius: 1rem;
  // padding: 1rem;

  .menu-name {
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 14px;
    padding: 1rem 1rem 0rem 1rem;
  }

  .menu-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    padding: 0rem 8px 1rem;

    .menu-items {
      display: flex;
      row-gap: 18px;
      align-items: stretch;
      column-gap: 10px;
      justify-content: flex-start;
      overflow-x: auto;
      padding: 0.75rem 0 0 0;
      width: 100%;

      scroll-behavior: smooth;
      scrollbar-width: none;
      -ms-overflow-style: none;

      .item {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 25%;
        min-width: 25%;
        background: #fff;
        border-radius: 0.5rem;
        padding: 8px;
        margin: 0px 4px;
        min-height: 5rem;
        position: relative;

        @media screen and (max-width: 991.98px) {
          width: 50%;
          min-width: 50%;
        }

        @media screen and (max-width: 400px) {
          width: 85%;
          min-width: 85%;
        }

        .name,
        .price {
          font-size: 16px;
        }

        .description {
          font-size: 12px;
        }

        .name,
        .description {
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .recommended-add-to-cart {
          border-radius: 50%;
          color: #fff;
          background: var(--primary);
          border: none;
          padding: 4px 8px;
          font-size: 14px;
          cursor: pointer;
        }

        .recommended-item-count {
          position: absolute;
          right: -8px;
          top: -8px;
          background: #000;
          color: #fff;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
        }
      }
    }

    .menu-items::-webkit-scrollbar {
      display: none;
    }

    .scroll-btn {
      background: #fff;
      border: none;
      border-radius: 50%;
      font-size: 14px;
      cursor: pointer;
      z-index: 2;
      color: #333;
      transition: background 0.3s;
      padding: 4px 10px;

      @media screen and (max-width: 400px) {
        display: none;
      }
    }

    .scroll-btn:hover {
      background: #dddddd;
    }

    .prev {
      margin-right: 5px;
    }

    .next {
      margin-left: 5px;
    }
  }
}

.divider-vertical {
  width: 1px;
  height: 1rem;
  background-color: #ccc;
}

.floating-cart {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;

  .cart-summary {
    background: var(--primary);
    color: white;
    padding: 16px 24px;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    max-width: 20rem;
    margin: 0px auto;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25);
    }

    .cart-icon {
      position: relative;

      i {
        font-size: 1.5rem;
      }

      .cart-count {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #ff6b6b;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 600;
      }
    }

    .cart-info {
      display: flex;
      flex-direction: column;

      .cart-total {
        font-size: 1.2rem;
        font-weight: 700;
      }

      .cart-label {
        font-size: 0.9rem;
        opacity: 0.9;
      }
    }
  }

  .cart-details {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    margin-top: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    // width: 25rem;
    width: min-content;

    &.expanded {
      max-height: 90dvh;
      padding: 20px;
      width: 25rem;
      max-width: 92dvw;
    }

    .cart-items-list {
      max-height: 55dvh;
      overflow-y: auto;
      margin-bottom: 16px;

      .cart-item {
        display: flex;
        flex-direction: column;
        row-gap: 8px;
        padding: 8px 0;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }

        .item-name {
          flex: 1;
          font-weight: 500;
          color: #333;

          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;

          /* Firefox fallback */
          display: -moz-box;
          -moz-box-orient: vertical;
          -moz-line-clamp: 2;
        }

        .item-description {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;

          /* Firefox fallback */
          display: -moz-box;
          -moz-box-orient: vertical;
          -moz-line-clamp: 2;
        }

        .quantity-controls {
          display: flex;
          align-items: center;
          gap: 8px;

          button {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border: 1px solid #ddd;
            color: var(--primary);
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;

            &:hover {
              background: #f5f5f5;
            }

            &.add-btn {
              color: #27ae60;
            }

            &.delete-btn {
              color: #ff0000;
            }
          }

          span {
            min-width: 20px;
            text-align: center;
            font-weight: 600;
          }
        }

        .item-total {
          font-weight: 600;
          color: var(--primary);
        }
      }

      .empty-cart-cls {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 1rem 2rem;
        color: #6c757d;

        img {
          width: 60px;
          height: 45px;
          object-fit: contain;
        }

        .item-name {
          font-size: 1.2rem;
          font-weight: 600;
        }
      }
    }

    .checkout-btn {
      width: 100%;
      background: var(--primary);
      color: white;
      border: none;
      padding: 8px 14px;
      border-radius: 2rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .cart-loader {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  @media (max-width: 576px) {
    .cart-summary {
      .cart-info {
        display: none;
      }
    }
  }

  @media (max-width: 768px) {
    bottom: 16px;
    right: 16px;
    max-width: fit-content;

    .cart-summary {
      justify-content: center;
      margin: 0 0 0 auto;

      .cart-info {
        display: none;
      }
    }
  }

  @media (max-width: 992px) {
    .cart-summary {
      margin: 0 0 0 auto;
      max-width: fit-content;

      .cart-info {
        display: none;
      }
    }
  }
}

.variant-selection-modal {
  position: relative;
  background: #fff;
  border-radius: 1rem;
}

.addons-selection-modal {
  position: relative;
  background: #fff;
  border-radius: 1rem;
}

.variant-option-title {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  background-color: #efefef;
  padding: 10px 14px;
  border-radius: 1rem;
}

.title-truncate {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  /* Firefox fallback */
  line-height: 1.4em;
  max-height: 2.8em;
}

.variant-radio {
  display: flex;
  align-items: flex-start;
  padding: 12px 10px 0;
  border-bottom: 1px solid #c9c9c9;

  input[type="radio"] {
    accent-color: #e60023;
    margin-right: 0.5rem;
  }
}

.variant-checkbox {
  display: flex;
  align-items: flex-start;
  padding: 12px 10px 0;
  border-bottom: 1px solid #c9c9c9;

  input[type="checkbox"] {
    accent-color: #e60023;
    margin-right: 0.5rem;
  }
}

.variant-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1rem;
}

.close-btn {
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  width: 36px;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0px;

  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  /* Firefox fallback */
  line-height: 1.4em;
  max-height: 2.8em;
}

.description-truncate {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  /* Firefox fallback */
  line-height: 1.4em;
  max-height: 2.8em;
}

.option-label {
  font-size: 1.125rem;
}

.variant-option {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #eee;

  input[type="radio"] {
    accent-color: #e60023;
    margin-right: 0.5rem;
  }

  .sub-name-truncate {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;

    /* Firefox fallback */
    line-height: 1.4em;
    max-height: 2.8em;
  }

  .price {
    font-weight: 500;
  }
}

.quantity-selector {
  font-size: 1rem;
  background: #f5f5f5;
  width: fit-content;
  margin: auto;
  border-radius: 1rem;
  padding: 2px;

  .qty-btn {
    width: 36px;
    min-width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 1px solid #ccc;
    background: #fff;
    font-size: 1rem;
    font-weight: 600;
  }
}

.add-to-cart-btn {
  background-color: var(--primary);
  color: #fff;
  padding: 0.75rem;
  border: none;
  border-radius: 2rem;
  font-size: 1rem;
  font-weight: 500;

  &:hover {
    color: var(--primary);
    border: 1px solid var(--primary);
    background-color: #fff;
  }

  &:disabled {
    color: #000;
    border: 1px solid #000;
    background-color: #d8d8d8;
    cursor: not-allowed;
  }
}

.addon-list {
  .addon-section {
    background: #fff;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  }

  .addon-header {
    font-size: 1.125rem;
    margin-bottom: 0.25rem;
  }

  .form-check {
    font-size: 1rem;

    .form-check-input {
      width: 1.2rem;
      height: 1.2rem;
    }

    .form-check-label {
      font-weight: 500;
    }
  }

  .sub-addon-option {
    padding-left: 0.5rem;
  }
}

.back-to-variant-btn {
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  width: 36px;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dot-divider {
  width: 5px;
  height: 5px;
  background: #939393;
  border-radius: 50%;
}

.allergy-modal {
  border-radius: 1rem;

  .allergy-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .allergy-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      min-width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
    }

    .allergy-modal-title {
      font-size: 1.5rem;
    }
  }

  .allergy-modal-close {
    background-color: var(--primary);
    color: #fff;
    padding: 0.75rem;
    border: none;
    border-radius: 2rem;
    font-size: 1rem;
    font-weight: 500;

    &:hover {
      color: var(--primary);
      border: 1px solid var(--primary);
      background-color: #fff;
    }
  }
}

.item-not-available-modal {
  border-radius: 1rem;

  .item-not-available-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .item-not-available-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      min-width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .item-not-available-title {
      font-size: 1.5rem;
    }
  }

  .item-not-available-modal-close {
    background-color: var(--primary);
    color: #fff;
    padding: 0.75rem;
    border: none;
    border-radius: 2rem;
    font-size: 1rem;
    font-weight: 500;

    &:hover {
      color: var(--primary);
      border: 1px solid var(--primary);
      background-color: #fff;
    }
  }
}

.menu-modal {
  border-radius: 1rem;

  .menu-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .menu-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      min-width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .menu-modal-title {
      font-size: 1.5rem;
    }
  }

  .menu-modal-close {
    background-color: var(--primary);
    color: #fff;
    padding: 0.75rem;
    border: none;
    border-radius: 2rem;
    font-size: 1rem;
    font-weight: 500;

    &:hover {
      color: var(--primary);
      border: 1px solid var(--primary);
      background-color: #fff;
    }
  }
}

.otp-modal {
  border-radius: 1rem;

  .otp-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .otp-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      min-width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
    }

    .otp-modal-title {
      font-size: 1.5rem;
    }
  }

  .otp-modal-close {
    background-color: var(--primary);
    color: #fff;
    padding: 0.75rem;
    border: none;
    border-radius: 2rem;
    font-size: 1rem;
    font-weight: 500;

    &:hover {
      color: var(--primary);
      border: 1px solid var(--primary);
      background-color: #fff;
    }
  }
}

.custom-tooltip {
  background-color: #000;
  color: #fff;
  font-size: 14px;
  padding: 0.5rem;
}
