<ng-container *ngIf="!userService.userThemeLoading; else themeLoading">
  <ng-container [ngSwitch]="menuTheme">
    <app-referral1 *ngSwitchCase="'theme1'"></app-referral1>
    <app-referral2 *ngSwitchCase="'theme2'"></app-referral2>
    <app-referral2 *ngSwitchCase="'theme3'"></app-referral2>

    <!-- Optional: fallback -->
    <div *ngSwitchDefault>
      <app-referral1></app-referral1>
    </div>
  </ng-container>
</ng-container>

<ng-template #themeLoading>
  <div class="d-flex justify-content-center align-items-center w-100 h-100" style="min-height: 80vh;">
    <div class="cart-loader">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  </div>
</ng-template>
