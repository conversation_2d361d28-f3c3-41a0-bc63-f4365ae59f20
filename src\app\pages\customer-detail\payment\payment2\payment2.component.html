<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-12 d-flex pb-3" *ngIf="stripeCustomers.length < 3">
      <h4 class="col-6">Saved Card Details</h4>
      <div class="col-6 text-end">
        <div class="add-card-button" (click)="addCard(cardModal)">
          Add Card
        </div>
      </div>
    </div>

    <div class="col-md-6 col-xs-12 mb-2" *ngFor=" let stripeCustomer of stripeCustomers;let i = index;">
      <div class="payment-card-body">
        <div class="card-type">
          {{ stripeCustomer.card_type | uppercase }}
        </div>
        <div class="card-number">
          XXXX-XXXXXXXX-{{ stripeCustomer.card_number }}
        </div>
        <div class="card-footer">
          <div class="card-owner">
            <div class="owner-name">{{ stripeCustomer.customer_name }}</div>
            <div class="expiry">
              <span>Valid till</span>
              <span>{{stripeCustomer.exp_month}}/{{stripeCustomer.exp_year}}</span>
            </div>
          </div>
          <div class="card-brand">
            <i>{{ stripeCustomer.card_brand }}</i>
          </div>
        </div>
        <div class="delete-card-btn" (click)="deleteCard(stripeCustomer)">
          <i class="fas fa-trash-alt"></i>
        </div>
      </div>
    </div>

    <div class="col-md-12 empty-cart-cls text-center" *ngIf="stripeCustomers.length <= 0">
      <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
      <p><strong>No card(s) Found</strong></p>
    </div>

  </div>
</div>

<ng-template #cardModal let-modal>
  <div class="card-modal p-3">
    <div class="card-modal-header">
      <button class="card-close-btn" (click)="modal.dismiss('Cross click')">
        <i class="fas fa-times"></i>
      </button>
      <h4 class="card-modal-title text-primary fw-bold" id="modal-basic-title">
        Add Your New Card
      </h4>
    </div>
    <div class="modal-body my-2">
      <form (submit)="handleForm($event)">
        <div #cardElement id="customCardElement" class="body-box-shadow py-3"
          style='height: 2.6em !important; padding: .8em !important;'>
          <!-- A Stripe Element will be inserted here. -->
        </div>
        <div *ngIf="Modelerror">
          <span class="text-danger">{{ Modelerror }}</span>
        </div>
        <button type="submit" [disabled]="isModelLoading" class="add-card-button w-100 mt-3">
          <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
            role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          Add New Card
        </button>
      </form>
      <button class="card-modal-close w-100 mt-3" (click)="modal.dismiss('Cross click')">
        Close
      </button>
    </div>
  </div>
</ng-template>