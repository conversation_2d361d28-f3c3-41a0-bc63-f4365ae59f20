import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorHandler } from 'src/app/shared/error-handler';
import { environment } from 'src/environments/environment';
import { StripeCustomer } from '../models/stripe-customer';

@Injectable({
  providedIn: 'root',
})
export class StripeCustomerService {
  private url = environment.apiBaseUrl + 'cards/';
  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.service_type) params = params.set('service_type', options.service_type);
    if (options.customer_id) params = params.set('customer_id', options.customer_id);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<StripeCustomer> {
    return this.http.get<StripeCustomer>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(card: StripeCustomer): Observable<any> {
    return this.http.post<StripeCustomer>(this.url, StripeCustomer.toFormData(card))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(card: StripeCustomer): Observable<any> {
    return this.http.post<StripeCustomer>(this.url + card.id, StripeCustomer.toFormData(card))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<any> {
    return this.http.delete<StripeCustomer>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  payment_intent(paymentIntent: StripeCustomer): Observable<any> {
    return this.http
      .post<any>(this.url + 'payment-intent', StripeCustomer.toFormData(paymentIntent))
      .pipe(catchError(ErrorHandler.handleError));
  }
}
