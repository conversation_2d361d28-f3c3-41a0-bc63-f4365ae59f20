<div class="container mt-2 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">
    <div class="col-xs-12 col-sm-12 mt-2">

      <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="booking?.status == 'Pending'">Your reservation has been placed
      </h3>
      <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="booking?.status == 'Pending'">Waiting for restaurant to accept
      </h3>
      <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="booking?.status == 'Approved'">Your reservation has been
        Accepted
      </h3>
      <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="booking?.status == 'Cancel'">Your reservation has been rejected
      </h3>


      <div class="center">
        <ng-lottie width="300px" height="150px" [options]="optionPending" loop autoplay
          *ngIf="booking?.status == 'Pending'" containerClass="moving-box another-class">
        </ng-lottie>
        <ng-lottie width="300px" height="150px" [options]="optionAccepted" loop autoplay
          *ngIf="booking?.status == 'Approved'" containerClass="moving-box another-class">
        </ng-lottie>
        <ng-lottie width="300px" height="150px" [options]="optionFailed" loop autoplay
          *ngIf="booking?.status == 'Cancel'" containerClass="moving-box another-class">
        </ng-lottie>
      </div>

      <div class="stepper d-flex align-items-center justify-content-between py-4" *ngIf="booking?.status != 'Cancel'">
        <ng-container *ngFor="let status of orderStatuses">
          <div class="flex-fill text-center strikethrough"
            *ngIf="status.order_type=='all' || status.order_type==booking.order_type">
            <div class="status d-flex flex-column align-items-center" [class.active]="status.checked">
              <div class="dot text-center" [class.active]="status.checked"></div>
              <div class="title" [class.active]="status.checked">{{status.title}}</div>
            </div>
          </div>
        </ng-container>
      </div>

      <div class="stepper d-flex align-items-center justify-content-between py-4" *ngIf="booking?.status == 'Cancel'">
        <ng-container *ngFor="let status of orderRejectStatuses">
          <div class="flex-fill text-center strikethrough"
            *ngIf="status.order_type=='all' || status.order_type==booking.order_type">
            <div class="status d-flex flex-column align-items-center" [class.active]="status.checked">
              <div class="dot text-center" [class.active]="status.checked"></div>
              <div class="title" [class.active]="status.checked">{{status.title}}</div>
            </div>
          </div>
        </ng-container>
      </div>

      <div class="col-12">
        <h6 class="col-12 text-center fw-bold" *ngIf="booking?.status == 'Pending'">Thank you, your reservation have
          been
          successfully placed. Please wait for the restaurant to confirm your reservation and booking time.</h6>
        <h6 class="col-12 text-center fw-bold" *ngIf="booking?.status == 'Approved'">Your reservation has been accepted.
        </h6>
        <h6 class="col-12 text-center fw-bold" *ngIf="booking?.status == 'Cancel'">Sorry, but your reservation from
          {{restaurant.restaurant_name}} cannot be accepted at the moment.
        </h6>
      </div>
    </div>

    <div class="col-xs-12 col-sm-12 mt-2">
      <div class="bg-white body-box-shadow rounded p-2">
        <h5 class="col-12 text-center">Booking Info</h5>
        <div class="panel-body">
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Booking ID</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{booking.booking_id}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Restaurant Name</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{restaurant.restaurant_name}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Guest</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{booking.guest_count}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Booking Date</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{booking.booking_date}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Booking Time</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{booking.booking_time}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2" *ngIf="booking.booking_instruction">
            <span class="col-4">Booking Instruction</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold" style="word-wrap: break-word;">{{booking.booking_instruction}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2" *ngIf="false">
            <span class="col-4">Place Date</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{convertToDate(booking.created)}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Booking Status</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">
              {{booking?.status == 'Cancel'?'Rejected':(booking?.status | titlecase)}}
            </span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2" *ngIf="booking?.status == 'Cancel'">
            <span class="col-4">Rejected Reason</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{booking.cancel_reason}}</span>
          </div>

          <div class="col-12 d-flex justify-content-between py-2" *ngIf="booking?.booking_amount">
            <span class="col-4">Booking Amount</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{booking.booking_amount}}</span>
          </div>

          <div class="col-12 d-flex justify-content-between py-2" *ngIf="booking?.txn_id">
            <span class="col-4">Booking Transaction ID</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{booking.txn_id}}</span>
          </div>

          <div class="col-12 d-flex justify-content-between py-2" *ngIf="booking?.card_view">
            <span class="col-4">Booking Card</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">XXXX-XXXX-XXXX-{{booking.card_view.card_number}}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xs-12 col-sm-12 mt-2">
      <div class="bg-white body-box-shadow rounded p-2">
        <h5 class="col-12 text-center">Customer Info</h5>
        <div class="panel-body">
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Customer Name</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{booking.customer_name}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Customer Email</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{booking.booking_email}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Customer Phone</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{booking.booking_phone}}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xs-12 col-sm-12 mt-2 text-center align-items-center">
      <a class="mt-3 btn m-t-10 bg-primary text-white fw-bold cursor" routerLink="/menu">
        Back To Homepage
      </a>
    </div>
  </div>