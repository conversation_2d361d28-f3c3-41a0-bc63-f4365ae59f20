<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-md-12 col-xs-12 pb-3 d-flex">
      <h4 class="col-md-6 col-xs-6">Privacy Setting</h4>
      <div class="col-md-6 col-xs-6 text-end float-right px-2">
        <div class="btn-group btn-toggle"
          [ngClass]="{'switch-button': restaurant.web_theme == 'theme2' || restaurant.web_theme == 'theme3'}">
          <button class="btn btn-sm border cursor" [class.active]="selectedToogle == 'on'"
            (click)="buttonStatus('on')">Enable</button>
          <button class="btn btn-sm border cursor" [class.active]="selectedToogle == 'off'"
            (click)="buttonStatus('off')">Disable</button>
        </div>
      </div>
    </div>

    <div class="col-md-12 col-xs-12 body-box-shadow d-md-flex p-2"
      [ngClass]="{'rounded' : restaurant.web_theme == 'theme1'}" [ngStyle]="{
      'border-radius': restaurant.web_theme == 'theme1' ? '0.5rem' : '1rem'
      }">
      <div class="col-md-6 col-xs-12 pb-4 p-3" style="border-right: 1px solid #dee2e6 ;">
        <h5>Email Notifications</h5>
        <label class="col-12 p-1">
          <input type="checkbox" id="email_order_place" name="email_order_place" [(ngModel)]="user.email_order_place">
          When order placed
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="email_order_accept" name="email_order_accept"
            [(ngModel)]="user.email_order_accept">
          When order accepted
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="email_order_reject" name="email_order_reject"
            [(ngModel)]="user.email_order_reject">
          When order rejected
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="email_order_delivered" name="email_order_delivered"
            [(ngModel)]="user.email_order_delivered">
          When order delivered/picked Up
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="email_book_table" name="email_book_table" [(ngModel)]="user.email_book_table">
          When Book table
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="email_book_status" name="email_book_status" [(ngModel)]="user.email_book_status">
          When Accepted/Rejected Book table
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="email_important_update" name="email_important_update"
            [(ngModel)]="user.email_important_update">
          Important update from Tiffintom
        </label>
      </div>

      <div class="col-md-6 col-xs-12 p-3">
        <h5>SMS Notifications</h5>
        <label class="col-12 p-1">
          <input type="checkbox" id="sms_order_place" name="sms_order_place" [(ngModel)]="user.sms_order_place">
          When order placed
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="sms_order_accept" name="sms_order_accept" [(ngModel)]="user.sms_order_accept">
          When order accepted
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="sms_order_reject" name="sms_order_reject" [(ngModel)]="user.sms_order_reject">
          When order rejected
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="sms_order_delivered" name="sms_order_delivered"
            [(ngModel)]="user.sms_order_delivered">
          When order delivered/picked Up
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="sms_book_table" name="sms_book_table" [(ngModel)]="user.sms_book_table">
          When Book table
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="sms_book_status" name="sms_book_status" [(ngModel)]="user.sms_book_status">
          When Accepted/Rejected Book table
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="sms_important_update" name="sms_important_update"
            [(ngModel)]="user.sms_important_update">
          Important update from Tiffintom
        </label>
      </div>

      <div class="col-md-6 col-xs-12 d-none">
        <h5>App Notifications</h5>
        <label class="col-12 p-1">
          <input type="checkbox" id="app_order_place" name="app_order_place" [(ngModel)]="user.app_order_place">
          When order placed
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="app_order_accept" name="app_order_accept" [(ngModel)]="user.app_order_accept">
          When order accepted
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="app_order_reject" name="app_order_reject" [(ngModel)]="user.app_order_reject">
          When order rejected
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="app_order_delivered" name="app_order_delivered"
            [(ngModel)]="user.app_order_delivered">
          When order delivered/picked Up
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="app_book_table" name="app_book_table" [(ngModel)]="user.app_book_table">
          When Book table
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="app_book_status" name="app_book_status" [(ngModel)]="user.app_book_status">
          When Accepted/Rejected Book table
        </label>
        <label class="col-12 p-1">
          <input type="checkbox" id="app_important_update" name="app_important_update"
            [(ngModel)]="user.app_important_update">
          Important update from Tiffintom
        </label>
      </div>

    </div>

    <div *ngIf="error">
      <span class="text-danger">{{ error }}</span>
    </div>
    <div class="col-12 text-center mt-3">
      <button type="button" (click)="validatePrivacy()" [disabled]="isPrivacyLoading" [ngClass]="{
              'btn btn-primary text-white cursor': restaurant.web_theme == 'theme1',
              'update-btn': restaurant.web_theme == 'theme2' || restaurant.web_theme == 'theme3'}">
        <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isPrivacyLoading"
          role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        Update
      </button>
    </div>
  </div>
</div>

<ng-template #addressModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Add New Deliver Address
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">PostCode</label>
      <div class="col-sm-12 col-xs-12 d-flex justify-content-between">
        <input type="text" class="form-control col-md-5 col-xs-12 w-50" id="zipcode" name="zipcode"
          [(ngModel)]="addressBookAdd.zipcode" required />
        <div class="ms-2 spinner-border text-primary" *ngIf="isModelLoading" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <a class="btn bg-primary col-md-5 col-xs-12 text-white align-items-right cursor" [disabled]="isModelLoading"
          style="width:4cm;" (click)="findzipcode(addressBookAdd.zipcode)">
          Find Address
        </a>
      </div>
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Address Title</label>
      <input type="text" class="form-control col-md-8" id="title" name="title" [(ngModel)]="addressBookAdd.title" />
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Door no / Flat no</label>
      <input type="text" class="form-control col-md-8" id="flat_no" name="flat_no" [(ngModel)]="addressBookAdd.flat_no"
        required />
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Address</label>
      <input type="text" class="form-control col-md-8" id="address" name="address" [(ngModel)]="addressBookAdd.address"
        readonly />
    </div>
    <input type="hidden" class="form-control col-md-8" id="latitude" name="latitude"
      [(ngModel)]="addressBookAdd.latitude" />
    <input type="hidden" class="form-control col-md-8" id="longitude" name="longitude"
      [(ngModel)]="addressBookAdd.longitude" />
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isModelLoading" class="btn btn-outline-dark bg-primary cursor text-white"
      (click)="validateAddress(addressModal)">
      Add Address
    </button>
    <div class="ms-2 spinner-border text-primary" *ngIf="isModelLoading" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
</ng-template>