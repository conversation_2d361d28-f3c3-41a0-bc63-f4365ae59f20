import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';

@Component({
  selector: 'app-auth2',
  templateUrl: './auth2.component.html',
  styleUrls: ['./auth2.component.scss']
})
export class Auth2Component implements OnInit {
  @ViewChild('containerDiv') containerDiv!: ElementRef<HTMLElement>;
  theme2: boolean = true;

  constructor() { }

  ngOnInit(): void {
  }

  addClassSingIn() {
    this.containerDiv.nativeElement.classList.remove('right-panel-active');
  }

  addClassSingUp() {
    this.containerDiv.nativeElement.classList.add('right-panel-active');
  }
}
