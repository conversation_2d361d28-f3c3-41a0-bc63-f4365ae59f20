.menu-item {
  .menu-details {
    width: 100%;
  }

  .add-item {
    cursor: pointer;
  }
}

.cart-wrapper-fixed {
  position: fixed;
  z-index: 1;
  top: 70px !important;
}

.order-types {
  box-shadow: 0px 7px 9px 0px #3e3e3e21;
}

.footer-category {
  position: fixed;
  right: 12px;
  bottom: 10px;
  width: 220px;
  z-index: 12;
  max-height: 300px;
  letter-spacing: 0.3px;
  text-align: left;
  overflow-y: scroll;
}

.blink_me {
  animation: blinker 1s linear infinite;
}

@keyframes blinker {
  50% {
    opacity: 0;
  }
}

.loader-bg {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgb(0, 0, 0, 0.8);
  z-index: 900;
  display: flex;
  align-items: center;
  justify-content: center;
}
