<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-12 d-flex pb-3" *ngIf="stripeCustomers.length < 3">
      <h4 class="col-6">Saved Card Details</h4>
      <div class="col-6 text-end">
        <div class="btn bg-primary text-white w-auto cursor" (click)=" addCard(cardModal)">
          Add Card
        </div>
      </div>
    </div>

    <div class="col-md-6 col-xs-12 mb-2" *ngFor=" let stripeCustomer of stripeCustomers;let i = index;">
      <div class="col-12 d-flex border border body-box-shadow rounded p-2">
        <img src="./assets/payment-logo/master.png" class="px-1 pt-2" width="50px" height="36px">
        <span class="fw-bold col-7 px-2">
          XXXX-XXXXXXXX-{{stripeCustomer.card_number}}
          <p class="text-muted">Valid till {{stripeCustomer.exp_month}}/{{stripeCustomer.exp_year}}</p>
        </span>
        <span class="fw-bold col-3 text-end cursor">
          <span (click)="deleteCard(stripeCustomer)">Delete</span>
        </span>
      </div>
    </div>

    <div class="col-md-12 empty-cart-cls text-center" *ngIf="stripeCustomers.length <= 0">
      <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
      <p><strong>No card(s) Found</strong></p>
    </div>

  </div>
</div>

<ng-template #cardModal let-modal>
  <div class=" modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Add Your New Card
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form (submit)="handleForm($event)">
      <div #cardElement id="customCardElement" class="body-box-shadow py-3"
        style='height: 2.6em !important; padding: .8em !important;'>
        <!-- A Stripe Element will be inserted here. -->
      </div>

      <div *ngIf="Modelerror">
        <span class="text-danger">{{ Modelerror }}</span>
      </div>
      <button type="submit" [disabled]="isModelLoading" class="mt-3 btn orng_btn m-t-10 bg-primary cursor text-white">
        <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
          role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        Add New Card
      </button>

    </form>
  </div>
</ng-template>