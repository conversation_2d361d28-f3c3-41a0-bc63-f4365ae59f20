import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Error<PERSON>andler } from '../../shared/error-handler';
import { catchError } from 'rxjs/operators';
import { SiteSetting } from '../models/site-setting';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SiteSettingService {
  private url = `${environment.apiBaseUrl}site-settings/`

  constructor(private http: HttpClient) { }

  show(id: string): Observable<SiteSetting> {
    return this.http.get<SiteSetting>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  show_all(): Observable<SiteSetting> {
    return this.http.get<SiteSetting>(this.url + '1' + '/all')
      .pipe(catchError(ErrorHandler.handleError));
  }
}
