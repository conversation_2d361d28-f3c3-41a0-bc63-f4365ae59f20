<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">
    <div class="col-sm-12 col-xs-12" *ngIf="bookings.length > 0">
      <h4>Booking History</h4>

      <div class="order-card border shadow p-3 mb-3" *ngFor=" let booking of bookings;">

        <!-- Order Header -->
        <div class="d-flex justify-content-between align-items-center mb-2">
          <div>
            <h6 class="mb-0 fw-bold">
              {{booking.booking_date}} • {{booking.booking_time}}
            </h6>
            <small class="text-muted">
              #{{booking.booking_id}} &nbsp;|&nbsp; Guest: {{booking.guest_count}}
            </small>
          </div>
          <span class="badge px-3 py-2 rounded-pill" [ngClass]="{
          'bg-success': booking.status === 'Delivered',
          'bg-warning': booking.status === 'Pending',
          'bg-danger': booking.status === 'Failed'
        }">
            {{ (booking.status == 'Failed' ?'Cancel':booking.status) }}
          </span>
        </div>

        <!-- Actions -->
        <div class="d-flex flex-wrap gap-2 mt-2">
          <button class="view-btn" (click)="bookView(booking.id)">
            <i class="fas fa-eye"></i> View
          </button>
        </div>
      </div>

      <app-pagination *ngIf="bookings.length > 0" [total_items]="totalBookings" class="mt-3 booking2-pagination"
        [per_page]="per_page" [(current_page)]="page" (onChange)="loadPage($event)">
      </app-pagination>

    </div>

    <div class="col-md-12 empty-cart-cls text-center py-5" *ngIf="bookings.length <= 0">
      <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
      <p><strong>No booking(s) Found</strong></p>
    </div>
  </div>
</div>


<ng-template #bookingModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Booking Details
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-6">Booking ID :</label>
      <label class="col-md-6">{{booking.booking_id}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Restaurant Name :</label>
      <label class="col-sm-6">{{restaurant.restaurant_name}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Guest :</label>
      <label class="col-sm-6">{{booking.guest_count}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Booking Date :</label>
      <label class="col-sm-6">{{booking.booking_date}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Booking Time :</label>
      <label class="col-sm-6">{{booking.booking_time}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2" *ngIf="booking.booking_instruction">
      <label class="col-sm-6">Instruction :</label>
      <label class="col-sm-6" style="word-wrap: break-word;">{{booking.booking_instruction}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Place Date :</label>
      <label class="col-sm-6">{{convertToDate(booking.created)}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Booking Status :</label>
      <label class="col-sm-6">{{booking.status | titlecase}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2" *ngIf="booking.cancel_reason">
      <label class="col-sm-6">Cancel Reason :</label>
      <label class="col-sm-6" style="word-wrap: break-word;">{{booking.cancel_reason}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Customer Name :</label>
      <label class="col-sm-6" style="word-wrap: break-word;">{{booking.customer_name}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Customer Email :</label>
      <label class="col-sm-6" style="word-wrap: break-word;">{{booking.booking_email}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Customer Phone Number :</label>
      <label class="col-sm-6" style="word-wrap: break-word;">{{booking.booking_phone}}</label>
    </div>
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
</ng-template>