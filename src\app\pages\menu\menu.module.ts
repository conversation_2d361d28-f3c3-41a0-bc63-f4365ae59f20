import { NgModule } from '@angular/core';
import { MenuComponent } from './menu.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { MenuThemeComponent } from './menu-theme/menu-theme.component';
import { Menu2Component } from './menu2/menu2.component';
import { Menu3Component } from './menu3/menu3.component';

const routes: Routes = [
  // { path: '', component: MenuComponent },
  { path: '', component: MenuThemeComponent }
];

@NgModule({
  declarations: [MenuThemeComponent, MenuComponent, Menu2Component, Menu3Component],
  imports: [RouterModule.forChild(routes), SharedModule],
})
export class MenuModule { }
