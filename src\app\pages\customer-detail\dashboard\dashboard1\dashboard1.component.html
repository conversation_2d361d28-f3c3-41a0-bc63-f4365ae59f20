<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-12 pb-3 d-flex">
      <div class="col-11">
        <h5 class="px-1">{{user.first_name}}</h5>
        <div class="fw-bold">
          <img src="assets/verify_correct.png" *ngIf="user.email_verify" class="px-1">
          <img src="assets/verify_close.png" *ngIf="!user.email_verify" class="px-1">
          {{user.username}}
        </div>
        <div class="fw-bold">
          <img src="assets/verify_correct.png" *ngIf="user.phone_verify" class="px-1">
          <img src="assets/verify_close.png" *ngIf="!user.phone_verify" class="px-1">
          {{user.phone_number}}
        </div>
      </div>
      <img [src]="user.image_url" *ngIf="user.image_url" class="col-1 rounded-circle text-end" width="60px"
        height="60px" alt=" tiffintom {{user.first_name}}">
    </div>

    <div class="col-md-3 col-xs-12">
      <div class="p-5 body-box-shadow rounded text-center">
        <p>
          <img src="assets/trophy_dashboard.png" class="px-1">
        </p>
        <span class="text-muted">Earn Point</span>
        <p class="fw-bold ">{{dashboardDetails?.stats.total_earn_point}}</p>
      </div>
    </div>
    <div class="col-md-3 col-xs-12">
      <div class="p-5 body-box-shadow rounded text-center">
        <p>
          <img src="assets/wallet_dashboard.png" class="px-1">
        </p>
        <span class="text-muted">Wallet</span>
        <p class="fw-bold ">{{convertNumber(user.wallet_amount)}}</p>
      </div>
    </div>
    <div class="col-md-3 col-xs-12">
      <div class="p-4 body-box-shadow rounded text-center">
        <circle-progress class="col-md-12 col-xs-12" [percent]="dashboardDetails?.stats.profile_percentage"
          [outerStrokeWidth]="8" [outerStrokeColor]="'#3777BE'" [animation]="true" [animationDuration]="1000"
          [subtitleFontSize]="12" lazy="true" radius="65" subtitle="Profile">
        </circle-progress>
      </div>
    </div>
    <div class="col-md-3 col-xs-12">
      <div class="p-3 body-box-shadow rounded text-center">
        <p class="text-muted m-1">Verify your email</p>
        <span class="btn btn-danger text-white fw-bold cursor" *ngIf="!user.email_verify && !emailTab"
          (click)="emailOtpSend()">Email
          Verify</span>
        <div class="col-12 py-1 text-center" *ngIf="emailTab">
          <input type="text" class="form-control col-md-3 col-xs-12" id="email_otp" name="email_otp"
            [(ngModel)]="verifyOtp" placeholder="Enter your otp" />
        </div>
        <span class="text-primary fw-bold col-md-4 col-xs-12 px-1 cursor" *ngIf="emailTab"
          (click)="resendEmailOtp()">Resend
          |
        </span>
        <span class="text-primary fw-bold col-md-4 col-xs-12 px-1 cursor" *ngIf="emailTab"
          (click)="validateEmail()">Submit</span>
        <div *ngIf="ModelEmailOtpError">
          <span class="text-danger">{{ ModelEmailOtpError }}</span>
        </div>
        <span class="btn btn-success text-white fw-bold" *ngIf="user.email_verify">Email Verified</span>
        <p class="text-muted m-1">Verify your phone</p>

        <span class="btn btn-danger text-white fw-bold cursor" *ngIf="!user.phone_verify && !phoneTab"
          (click)="otpSend()">Phone
          Verify</span>
        <div class="col-12 py-1 text-center" *ngIf="phoneTab">
          <input type="text" class="form-control col-md-3 col-xs-12" id="otp" name="otp" [(ngModel)]="verifyOtp"
            placeholder="Enter your otp" />
        </div>
        <span class="text-primary fw-bold col-md-4 col-xs-12 px-1 cursor" *ngIf="phoneTab" (click)="resendOtp()">Resend
          |
        </span>
        <span class="text-primary fw-bold col-md-4 col-xs-12 px-1 cursor" *ngIf="phoneTab"
          (click)="validateOtp()">Submit</span>
        <div *ngIf="Modelotperror">
          <span class="text-danger">{{ Modelotperror }}</span>
        </div>

        <span class="btn btn-success text-white fw-bold" *ngIf="user.phone_verify">Phone Verified</span>
      </div>
    </div>

    <div class="col-md-6 col-xs-12 mt-3">
      <div class="py-2 body-box-shadow rounded text-center">
        <h5>Orders</h5>
        <div *ngIf="completeOrderList.length > 0" style="overflow-x: auto;">
          <table class="fees-table w-100">
            <tr>
              <th>sr.no</th>
              <th>Order Number</th>
              <th>Status</th>
              <th>Action</th>
            </tr>
            <tr *ngFor="let item of completeOrderList;let i = index">
              <td>{{i+1}}</td>
              <td>{{item.order_number}}</td>
              <td>{{item.status}}</td>
              <td><span class="btn btn-primary text-white cursor" (click)="orderView(item.id)">View</span></td>
            </tr>
          </table>
          <a routerLink="../orders" class="btn btn-primary text-white mt-2 cursor">More Orders</a>
        </div>
        <div class="col-md-12 empty-cart-cls text-center" *ngIf="completeOrderList.length <= 0">
          <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
          <p><strong>No order(s) Found</strong></p>
        </div>
      </div>
    </div>

    <div class="col-md-6 col-xs-12 mt-3">
      <div class="py-2 body-box-shadow rounded text-center">
        <h5>Booking</h5>
        <div *ngIf="bookigList.length > 0" style="overflow-x: auto;">
          <table class="fees-table w-100">
            <tr>
              <th>sr.no</th>
              <th>Order Number</th>
              <th>Status</th>
              <th>Action</th>
            </tr>
            <tr *ngFor="let item of bookigList;let i = index">
              <td>{{i+1}}</td>
              <td>{{item.booking_id}}</td>
              <td>{{item.status}}</td>
              <td>
                <span class="btn btn-primary text-white cursor" (click)="bookView(item.id)">View</span>
                <span class="btn btn-primary text-white cursor" (click)="bookingView(bookingModal,item)"
                  *ngIf="false">View</span>
              </td>
            </tr>
          </table>
          <a routerLink="../bookings" class="btn btn-primary text-white mt-2 cursor">More Bookings</a>
        </div>
        <div class="col-md-12 empty-cart-cls text-center" *ngIf="bookigList.length <= 0">
          <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
          <p><strong>No booking(s) Found</strong></p>
        </div>
      </div>
    </div>

    <div class="col-md-6 col-xs-12 mt-3">
      <div class="py-2 body-box-shadow rounded text-center">
        <h5>Card Details</h5>
        <div *ngIf="cardList.length > 0" style="overflow-x: auto;">
          <table class="fees-table w-100">
            <tr>
              <th>sr.no</th>
              <th>Card Number</th>
              <th>Status</th>
            </tr>
            <tr *ngFor="let item of cardList;let i = index">
              <td>{{i+1}}</td>
              <td>XXXX-XXXXXXXX-{{item.card_number}}</td>
              <td>{{item.exp_month}} / {{item.exp_year}}</td>
            </tr>
          </table>
          <a routerLink="../payments" class="btn btn-primary text-white mt-2 cursor">More Cards</a>
        </div>
        <div class="col-md-12 empty-cart-cls text-center" *ngIf="cardList.length <= 0">
          <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
          <p><strong>No card(s) Found</strong></p>
        </div>
      </div>
    </div>

    <div class="col-md-6 col-xs-12 mt-3">
      <div class="py-2 body-box-shadow rounded text-center">
        <h5>Address</h5>
        <div *ngIf="addressList.length > 0" style="overflow-x: auto;">
          <table class="fees-table w-100">
            <tr>
              <th>sr.no</th>
              <th>Title</th>
              <th>Address</th>
            </tr>
            <tr *ngFor="let item of addressList;let i = index">
              <td>{{i+1}}</td>
              <td>{{item.title}}</td>
              <td>{{item.address}}{{item.zipcode}}</td>
            </tr>
          </table>
          <a routerLink="../addresses" class="btn btn-primary text-white mt-2 cursor">More Addresses</a>
        </div>
        <div class="col-md-12 empty-cart-cls text-center" *ngIf="addressList.length <= 0">
          <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
          <p><strong>No address(s) Found</strong></p>
        </div>
      </div>
    </div>

    <div class="col-md-6 col-xs-12 mt-3">
      <div class="py-2 body-box-shadow rounded text-center">
        <h5>Pending Order Review</h5>
        <div *ngIf="deliveredOrderList.length > 0" style="overflow-x: auto;">
          <table class="fees-table w-100">
            <tr>
              <th>sr.no</th>
              <th>Order Number</th>
              <th>Status</th>
              <th>Action</th>
            </tr>
            <tr *ngFor="let item of deliveredOrderList;let i = index">
              <td>{{i+1}}</td>
              <td>{{item.order_number}}</td>
              <td>{{item.status}}</td>
              <td><span class="btn btn-primary text-white cursor" (click)="addReview(reviewModal,item)">Review</span>
              </td>
            </tr>
          </table>
          <a routerLink="../orders" class="btn btn-primary text-white mt-2 cursor">More Orders</a>
        </div>
        <div class="col-md-12 empty-cart-cls text-center" *ngIf="deliveredOrderList.length <= 0">
          <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
          <p><strong>No order(s) Found</strong></p>
        </div>
      </div>
    </div>

  </div>
</div>

<ng-template #addressModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Add New Deliver Address
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">PostCode</label>
      <div class="col-sm-12 col-xs-12 d-flex justify-content-between">
        <input type="text" class="form-control col-md-5 col-xs-12 w-50" id="zipcode" name="zipcode"
          [(ngModel)]="addressBookAdd.zipcode" required />
        <div class="ms-2 spinner-border text-primary" *ngIf="isModelLoading" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <a class="btn bg-primary col-md-5 col-xs-12 text-white align-items-right cursor" [disabled]="isModelLoading"
          style="width:4cm;" (click)="findzipcode(addressBookAdd.zipcode)">
          Find Address
        </a>
      </div>
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Address Title</label>
      <input type="text" class="form-control col-md-8" id="title" name="title" [(ngModel)]="addressBookAdd.title" />
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Door no / Flat no</label>
      <input type="text" class="form-control col-md-8" id="flat_no" name="flat_no" [(ngModel)]="addressBookAdd.flat_no"
        required />
    </div>
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Address</label>
      <input type="text" class="form-control col-md-8" id="address" name="address" [(ngModel)]="addressBookAdd.address"
        readonly />
    </div>
    <input type="hidden" class="form-control col-md-8" id="latitude" name="latitude"
      [(ngModel)]="addressBookAdd.latitude" />
    <input type="hidden" class="form-control col-md-8" id="longitude" name="longitude"
      [(ngModel)]="addressBookAdd.longitude" />
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isModelLoading" class="btn btn-outline-dark bg-primary cursor text-white"
      (click)="validateAddress(addressModal)">
      Add Address
    </button>
    <div class="ms-2 spinner-border text-primary" *ngIf="isModelLoading" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
</ng-template>

<ng-template #reviewModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Review
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group d-flex">
      <label class="col-md-4">Rating</label>
      <div class="col-md-8">
        <app-rating [stars]="'5'" [(rating)]="reviewAdd.rating" class="pt-0 mt-0"></app-rating>
      </div>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-4">Message</label>
      <div class="col-sm-8">
        <textarea rows="3" class="form-control" nz-input [(ngModel)]="reviewAdd.message" name="message" id="message"
          placeholder="Enter Your Message"> </textarea>
      </div>
    </div>
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isModelLoading" class="btn btn-outline-dark bg-primary cursor text-white"
      (click)="validateReview(reviewModal)">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Submit
    </button>
  </div>
</ng-template>

<ng-template #bookingModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Booking Details
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-6">Booking ID :</label>
      <label class="col-md-6">{{booking.booking_id}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Restaurant Name :</label>
      <label class="col-sm-6">{{restaurant.restaurant_name}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Guest :</label>
      <label class="col-sm-6">{{booking.guest_count}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Booking Date :</label>
      <label class="col-sm-6">{{booking.booking_date}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Booking Time :</label>
      <label class="col-sm-6">{{booking.booking_time}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2" *ngIf="booking.booking_instruction">
      <label class="col-sm-6">Instruction :</label>
      <label class="col-sm-6">{{booking.booking_instruction}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Place Date :</label>
      <label class="col-sm-6">{{convertToDate(booking.created)}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Booking Status :</label>
      <label class="col-sm-6">{{booking.status | titlecase}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2" *ngIf="booking.cancel_reason">
      <label class="col-sm-6">Cancel Reason :</label>
      <label class="col-sm-6">{{booking.cancel_reason}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Customer Name :</label>
      <label class="col-sm-6">{{booking.customer_name}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Customer Email :</label>
      <label class="col-sm-6">{{booking.booking_email}}</label>
    </div>
    <div class="col-sm-12 col-xs-12 d-flex pt-2">
      <label class="col-sm-6">Customer Phone Number :</label>
      <label class="col-sm-6">{{booking.booking_phone}}</label>
    </div>
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
</ng-template>

<ng-template #otpModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Phone Verify
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Otp</label>
      <input type="text" class="form-control col-md-8" id="otp" name="otp" [(ngModel)]="verifyOtp"
        placeholder="Enter your Otp" />
    </div>
    <div *ngIf="Modelotperror">
      <span class="text-danger">{{ Modelotperror }}</span>
    </div>
  </div>
  <div class="modal-footer justify-content-between">
    <button type="button" [disabled]="isModelOtpLoading"
      class="btn btn-outline-dark bg-primary cursor text-white text-start" (click)="resendOtp()">
      Re-send
    </button>
    <div class="ms-2 spinner-border text-primary" *ngIf="isModelOtpLoading" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <button type="button" [disabled]="isModelOtpLoading" class="btn btn-outline-dark bg-primary cursor text-white"
      (click)="validateOtp()">
      submit
    </button>
  </div>
</ng-template>