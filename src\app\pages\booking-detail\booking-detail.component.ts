import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import {
  NgbModal,
  ModalDismissReasons,
  NgbModalOptions,
  NgbActiveModal,
} from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { BookingService } from 'src/app/core/services/booking.service';
import { Booking } from 'src/app/core/models/booking';
import { AnimationItem } from 'lottie-web';
import { AnimationOptions } from 'ngx-lottie';

@Component({
  selector: 'app-booking-detail',
  templateUrl: './booking-detail.component.html',
  styleUrls: ['./booking-detail.component.scss'],
})
export class BookingDetailComponent implements OnInit, OnDestroy {
  subs = new Subscription();
  isLoading = false;
  error = null;
  isModelLoading = false;
  Modelerror = null;
  user: User;
  restaurant: Restaurant = new Restaurant();
  booking: Booking = new Booking();
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;


  milliSecondsInASecond = 1000;
  hoursInADay = 24;
  minutesInAnHour = 60;
  SecondsInAMinute = 60;

  public timeDifference;
  public secondsToDday;
  public minutesToDday;
  public hoursToDday;
  public daysToDday;


  orderStatuses = [
    {
      "title": "Booking Placed",
      "checked": true,
      "status": 'Pending',
      "order_type": "all"
    },
    {
      "title": "Booking Accepted",
      "checked": false,
      "status": 'Approved',
      "order_type": "all"
    },
  ]

  orderRejectStatuses = [
    {
      "title": "Booking Placed",
      "checked": true,
      "status": 'Pending',
      "order_type": "all"
    },
    {
      "title": "Booking has been Rejected",
      "checked": true,
      "status": 'Cancel',
      "order_type": "all"
    },
  ]

  optionPending: AnimationOptions = {
    path: './assets/Animations/Waitingtoaccept.json',
  };
  optionAccepted: AnimationOptions = {
    path: './assets/Animations/OrderPlaced.json',
  };
  optionDelivered: AnimationOptions = {
    path: './assets/Animations/Fooddelivered.json',
  };
  optionCollected: AnimationOptions = {
    path: './assets/Animations/Foodontheway.json',
  };
  optionWaiting: AnimationOptions = {
    path: './assets/Animations/Placed.json',
  };
  optionFailed: AnimationOptions = {
    path: './assets/Animations/Orderrejected.json',
  };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private restaurantService: RestaurantService,
    private currencyPipe: CurrencyPipe,
    private bookingService: BookingService,
  ) { }

  ngOnInit(): void {
    this.booking.id = atob(this.route.snapshot.paramMap.get('id'));
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    this.modalOptions = {
      backdrop: 'static',
      size: 'lg',
      backdropClass: 'customBackdrop',
    };
    //Restaurant Find 
    this.fetchUser();
    this.fetchRestaurant();
    this.fetchBooking();

    setTimeout(() => { this.ngOnInit() }, 120000);
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
      }, err => this.error = err)
    );
  }

  fetchBooking() {
    this.isLoading = true;

    this.subs.add(this.bookingService.show(this.booking.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        (res) => {
          this.booking = res;
          if (this.userId != this.booking.customer_id) {
            this.router.navigateByUrl('/menu');
          }
          if (this.booking.status != 'Cancel') {
            this.orderStatuses.forEach(statuses => {
              if (this.booking.status == 'Approved') {
                statuses.checked = true;
              }
              if (this.booking.status == 'Pending' && statuses.status == 'Pending') {
                statuses.checked = true;
              }
            });
          }
        }, (err) => {
          this.router.navigateByUrl('/menu');
        }
      )
    )
  }

  fetchUser() {
    this.isLoading = true;
    this.subs.add(this.userService.show(this.user.id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        (res) => {
          this.userService.saveUser(res);
          this.user = res;
        }, (err) => {
        }
      )
    )
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd', 'en_US')
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
