<section class="product-main-box-section">

  <div class="container loader-height" *ngIf="isLoading">
    <div class="grubz-loader">
      <div class="set-one">
        <div class="circle"></div>
        <div class="circle"></div>
      </div>
      <div class="set-two">
        <div class="circle"></div>
        <div class="circle"></div>
      </div>
    </div>
  </div>

  <div class="container" *ngIf="!isLoading">

    <div class="product-details-left pe-0">
      <div class="row">
        <div class="col-md-12">
          <div class="product-main-box">
            <div class="reservation-restaurant-title">
              <h5>{{ restaurant?.restaurant_name }}</h5>
            </div>
            <img class="product-main-image" [src]="promotions?.image_url" [alt]="restaurant?.restaurant_name"
              onerror="this.src='./assets/images/product-main-image.png';">
            <button (click)="redirectRestaurant(restaurant)" class="view-menu-btn cursor">View Menu</button>
            <div class="product-logo">
              <img [src]="restaurant?.image_url" [alt]="restaurant?.restaurant_name"
                onerror="this.src='./assets/favicon.png';">
            </div>
          </div>
        </div>
      </div>

      <div class="product-rating-section">
        <div class="row">
          <div class="col-xl-8 col-lg-8" *ngIf="!bookingPayment">
            <div class="make-reservation-box" *ngIf="!bookingComplete">
              <div class="main-heading">
                <h6>Make a reservation</h6>
              </div>
              <form nz-form #bookingForm="ngForm" (ngSubmit)="onSubmit(bookingForm)" nzLayout="vertical">
                <div class="row">

                  <div class="col-lg-6 pe-lg-4">
                    <div class="form-group">
                      <label>Reservation Name</label>
                      <nz-form-item>
                        <nz-form-control nzHasFeedback nzErrorTip="Please enter your name!">
                          <nz-input-group>
                            <input class="form-control" type="text" name="reservation-name" id="reservation-name"
                              nz-input [(ngModel)]="booking.customer_name" required placeholder="Name">
                            <!-- (keydown)="onSpaceKeyDown($event)" -->
                          </nz-input-group>
                        </nz-form-control>
                      </nz-form-item>
                    </div>
                  </div>

                  <div class="col-lg-6 ps-lg-4">
                    <div class="form-group">
                      <label>Email Address</label>
                      <nz-form-item>
                        <nz-form-control nzHasFeedback [nzErrorTip]="resemailErrorTpl">
                          <nz-input-group>
                            <input class="form-control" type="email" email="true"
                              pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$" #reseEmail="ngModel"
                              name="reservation-email" id="reservation-email" nz-input
                              (keydown.space)="onSpaceKeyDown($event)" [(ngModel)]="booking.booking_email" required
                              placeholder="Email">
                          </nz-input-group>
                        </nz-form-control>
                        <ng-template #resemailErrorTpl let-control>
                          <ng-container *ngIf="control.hasError('required')">
                            Please enter your email!
                          </ng-container>
                          <ng-container
                            *ngIf="control.hasError('email') || (!control.hasError('required') && reseEmail.touched) || (!control.hasError('required') && !reseEmail.valid)">
                            Email must be a valid email address
                          </ng-container>
                        </ng-template>
                      </nz-form-item>
                    </div>
                  </div>

                  <div class="col-lg-6 pe-lg-4">
                    <div class="form-group">
                      <label>Number Of Guests</label>
                      <nz-form-item>
                        <nz-form-control nzHasFeedback nzErrorTip="Please enter guest count!">
                          <nz-input-group>
                            <div class="form-group select-box">
                              <select class="form-control" name="number-of-guests" id="number-of-guests"
                                [(ngModel)]="booking.guest_count" required>
                                <option *ngFor="let item of noOfGuest;" [value]="item" selected>
                                  {{item}}
                                </option>
                              </select>
                            </div>
                          </nz-input-group>
                        </nz-form-control>
                      </nz-form-item>
                    </div>
                  </div>

                  <div class="col-lg-6 ps-lg-4">
                    <div class="form-group">
                      <label>Phone Number</label>
                      <nz-form-item>
                        <nz-form-control nzHasFeedback [nzErrorTip]="phoneErrorTpl">
                          <nz-input-group>
                            <input class="form-control" type="text" inputmode="numeric" name="phone-number"
                              (keypress)="validateMobile($event)" id="phone-number" nz-input minlength="8"
                              (keydown.space)="onSpaceKeyDown($event)" [(ngModel)]="booking.booking_phone" required
                              placeholder="Phone Number">
                          </nz-input-group>
                          <ng-template #phoneErrorTpl let-control>
                            <ng-container *ngIf="control.hasError('required')">
                              Please enter phone number!
                            </ng-container>
                            <ng-container *ngIf="control.hasError('minlength')">
                              phone number at least 8 digit!
                            </ng-container>
                          </ng-template>
                        </nz-form-control>
                      </nz-form-item>
                    </div>
                  </div>

                  <div class="col-lg-6 pe-lg-4">
                    <div class="form-group">
                      <label>Selected Date</label>
                      <nz-form-item>
                        <nz-form-control nzHasFeedback nzErrorTip="Please select date!">
                          <nz-input-group [nzSuffix]="suffixIconSearch">
                            <!-- <input class="form-control" type="date" name="date" id="date" nz-input> -->
                            <input class="form-control" style="position: relative;" type="text" name="date" id="date"
                              [(ngModel)]="bookingDate" [minDate]="minDate" [maxDate]="maxDate" required ngbDatepicker
                              #d="ngbDatepicker" (click)="d.toggle()" (dateSelect)="d.toggle()"
                              (dateSelect)="onSelect($event)" class="form-control" readonly>
                            <i class="fa-regular fa-calendar" style="position: absolute;right: 30px;bottom: 14px;"></i>
                          </nz-input-group>
                          <ng-template #suffixIconSearch>
                            <i class="fa-regular fa-calendar"></i>
                          </ng-template>
                        </nz-form-control>
                      </nz-form-item>
                    </div>
                  </div>

                  <div class="col-lg-6 ps-lg-4">
                    <div class="form-group">
                      <label>Selected Time</label>
                      <nz-form-item>
                        <nz-form-control nzHasFeedback nzErrorTip="Please select time!">
                          <nz-input-group>
                            <div class="form-group select-box">
                              <input type="text" name="booking_time" id="booking_time"
                                [(ngModel)]="booking.booking_time" nz-input value="Close" *ngIf="timeSlots.length == 0"
                                class="form-control" placeholder="Close" readonly />

                              <select class="form-control" name="time" id="time" [(ngModel)]="booking.booking_time"
                                *ngIf="timeSlots.length > 0" required>
                                <option *ngFor="let time of timeSlots;let j=idx" value="{{time}}" [selected]="j === 0">
                                  {{time}}
                                </option>
                              </select>
                            </div>
                          </nz-input-group>
                        </nz-form-control>
                      </nz-form-item>
                    </div>
                    <nz-form-item *ngIf="errorTime">
                      <span class="text-danger">{{ errorTime }}</span>
                    </nz-form-item>
                  </div>

                  <div class="col-lg-12">
                    <div class="form-group instructions-box">
                      <label>Special Requests</label>
                      <nz-form-item>
                        <nz-form-control nzHasFeedback nzErrorTip="Enter text!">
                          <nz-input-group>
                            <textarea class="form-control" name="instructions" id="instructions-msg" nz-input
                              [(ngModel)]="booking.booking_instruction"
                              placeholder="Enter instruction here...."></textarea>
                          </nz-input-group>
                        </nz-form-control>
                      </nz-form-item>
                    </div>
                  </div>

                  <nz-form-item *ngIf="errorBooking">
                    <span class="text-danger">{{ errorBooking }}</span>
                  </nz-form-item>

                  <div class="col-lg-12">
                    <button class="btn cursor"
                      *ngIf="restaurant.booking_status == 'Yes' && restaurant.restaurant_booktable == 'Yes'"
                      [disabled]="isBookingLoading">
                      <i class="spinner-border" *ngIf="isBookingLoading"></i>
                      Request Booking
                    </button>
                  </div>
                </div>
              </form>
            </div>

            <div class="make-reservation-box text-center" *ngIf="bookingComplete">
              <div class="main-heading">
                <h6>{{ restaurant?.restaurant_name }}</h6>
              </div>
              <div class="reservation-request-title">Your reservation request has been sent</div>
              <p class="reservation-request-description">
                Please wait for the restaurant to accept the reservation. An email<br>
                confirmation will be sent when approved. You can view the status<br>
                of your reservation in the profile section.
              </p>
              <div class="reservation-id">
                <p><strong>Reservation ID</strong> : {{ booking.booking_id }}</p>
              </div>
              <img class="request-sent-icon" src="assets/images/success-icon.svg" alt="Go-Grubz-success-image"
                loading="lazy">
              <div class="table-responsive">
                <table class="table">
                  <tr>
                    <td>Reservation Name:</td>
                    <td>{{ booking.customer_name }}</td>
                  </tr>
                  <tr>
                    <td>Email Address:</td>
                    <td>{{ booking.booking_email }}</td>
                  </tr>
                  <tr>
                    <td>Phone Number:</td>
                    <td>{{ booking.booking_phone }}</td>
                  </tr>
                  <tr>
                    <td>Number Of Guests:</td>
                    <td>{{ booking.guest_count }}</td>
                  </tr>
                  <tr>
                    <td>Selected Date:</td>
                    <td>{{ booking.booking_date }}</td>
                  </tr>
                  <tr>
                    <td>Selected Time:</td>
                    <td>{{ booking.booking_time }}</td>
                  </tr>
                  <tr>
                    <td>Special Requests:</td>
                    <td>
                      {{ booking.booking_instruction }}
                    </td>
                  </tr>
                  <tr *ngIf="booking.booking_amount">
                    <td>Booking Amount:</td>
                    <td>
                      {{ booking.booking_amount }}
                    </td>
                  </tr>
                  <tr *ngIf="booking.txn_id">
                    <td>Booking Transaction ID:</td>
                    <td>
                      {{ booking.txn_id }}
                    </td>
                  </tr>
                  <tr *ngIf="booking.card_view">
                    <td>Card Number:</td>
                    <td>
                      XXXX-XXXX-{{ booking.card_view.card_number }}
                    </td>
                  </tr>
                </table>
              </div>
            </div>

          </div>

          <div class="col-xl-8 col-lg-8" *ngIf="bookingPayment">
            <div class="make-reservation-box" *ngIf="!bookingComplete">
              <div class="main-heading d-flex justify-content-between">
                <a (click)="bookingPayment = false"><i class="fa-solid fa-arrow-left"></i></a>
                <h6>Make a payment</h6>
                <h6></h6>
              </div>
              <form nz-form #bookingForm="ngForm" nzLayout="vertical">
                <div class="row">

                  <div class="step-box" *ngIf="this.userService.user?.id">
                    <div class="step-body-box">
                      <div class="accordion" id="payment-details">
                        <div class="accordion-item" *ngFor=" let stripeCustomer of stripeCustomers;let i = index;">
                          <div class="accordion-header">
                            <button class="cursor d-flex justify-content-between align-items-center"
                              [ngClass]="{'collapsed' : booking.card_id != stripeCustomer.id}"
                              (click)="cardPaymentMethod(stripeCustomer.id)">
                              <span class="check" *ngIf="booking.card_id == stripeCustomer.id">
                                <img src=" assets/images/success-icon.svg" alt="Go-Grubz-success-image" loading="lazy">
                              </span>
                              <span class="check" *ngIf="booking.card_id != stripeCustomer.id">
                                <img src=" assets/images/unfill-radio.png" alt="Go-Grubz-unfill-image">
                              </span>
                              {{ stripeCustomer.card_brand | uppercase }}
                              xxxx{{stripeCustomer.card_number}}
                              Valid till
                              {{stripeCustomer.exp_month}}/{{stripeCustomer.exp_year}}
                              <i class="fa-solid fa-chevron-down"></i>
                            </button>
                          </div>
                        </div>
                      </div>

                      <div class="accordion" id="payment-details"
                        *ngFor=" let paymentMethod of restaurant?.payment_methods;">
                        <div class="accordion-item" *ngIf="paymentMethod.payment_method_name=='Stripe'">
                          <div class=" accordion-header">

                            <button class="cursor d-flex justify-content-between align-items-center"
                              [ngClass]="{'collapsed' : !booking.card_id}" (click)="checkPaymentMethod('Stripe')">
                              <!-- data-bs-toggle="collapse" data-bs-target="#creditpayment{{ order.card_id }}" -->
                              <span class="check" *ngIf="!booking.card_id">
                                <img src="assets/images/success-icon.svg" alt="Go-Grubz-success-image" loading="lazy">
                              </span>
                              <span class="check" *ngIf="booking.card_id">
                                <img src="assets/images/unfill-radio.png" alt="Go-Grubz-unfill-image">
                              </span>
                              Pay By Card <ul class="credit-card-list">
                                <li>
                                  <img src="assets/images/american-card.png" alt="american-card">
                                </li>
                                <li>
                                  <img src="assets/images/visa-card.png" alt="visa-card">
                                </li>
                                <li>
                                  <img src="assets/images/master-card.png" alt="master-card">
                                </li>
                                <li>
                                  <img src="assets/images/other-credit-card.png" alt="other-credit-card">
                                </li>
                              </ul> <i class="fa-solid fa-chevron-down"></i>
                            </button>
                          </div>

                          <div id="creditpayment" class="accordion-collapse collapse"
                            [ngClass]="{'show' : !booking.card_id}" data-bs-parent="#payment-details">
                            <div class="accordion-body">
                              <div class="pt-2">
                                <form nz-form #cardInfo="ngForm" (ngSubmit)="handleForm($event)" nzLayout="vertical">
                                  <div class="row">
                                    <div class="col-md-8">
                                      <div class="form-group">
                                        <div class="input-group">
                                          <span id="card-number" class="form-control rounded"></span>
                                        </div>
                                      </div>
                                    </div>

                                    <div class="col-md-4">
                                      <div class="form-group">
                                        <span id="card-exp" nz-input class="form-control rounded"></span>
                                      </div>
                                    </div>

                                    <div class="col-md-6">
                                      <div class="form-group">
                                        <span id="card-cvc" class="form-control rounded">
                                        </span>
                                      </div>
                                    </div>

                                    <div class="col-md-6">
                                      <div class="form-group">
                                        <span id="postalCode" nz-input class="form-control rounded"></span>
                                      </div>
                                    </div>

                                    <nz-form-item *ngIf="errorBooking">
                                      <span class="text-danger">{{
                                        errorBooking}}</span>
                                    </nz-form-item>
                                    <div class="next-btn pl-2 pr-2">
                                      <button
                                        *ngIf="restaurant.booking_status == 'Yes' && restaurant.restaurant_booktable == 'Yes'"
                                        class="btn w-100 justify-content-between d-none d-xl-flex mt-2" nz-button
                                        [disabled]="isBookingLoading">
                                        <span> make a payment</span>
                                        <i class="spinner-border" *ngIf="isBookingLoading"></i>
                                        <span>
                                          Total: {{
                                          convertNumber(restaurant.booking_payment
                                          == 'single' ?
                                          restaurant.booking_amount :
                                          restaurant.booking_amount *
                                          booking.guest_count) }}
                                        </span>
                                      </button>
                                    </div>
                                  </div>
                                </form>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>

                  <nz-form-item *ngIf="errorBooking && booking.card_id">
                    <span class="text-danger">{{ errorBooking }}</span>
                  </nz-form-item>

                  <div class="next-btn pl-2 pr-2" *ngIf="booking.card_id">
                    <button (click)="validateBooking()"
                      *ngIf="restaurant.booking_status == 'Yes' && restaurant.restaurant_booktable == 'Yes'"
                      class="btn w-100 justify-content-between d-none d-xl-flex mt-2" nz-button
                      [disabled]="isBookingLoading">
                      <span> make a payment</span>
                      <i class="spinner-border" *ngIf="isBookingLoading"></i>
                      <span>
                        Total: {{
                        convertNumber(restaurant.booking_payment
                        == 'single' ?
                        restaurant.booking_amount :
                        restaurant.booking_amount *
                        booking.guest_count) }}
                      </span>
                    </button>
                  </div>
                </div>
              </form>
            </div>

            <div class="make-reservation-box text-center" *ngIf="bookingComplete">
              <div class="main-heading">
                <h6>{{ restaurant?.restaurant_name }}</h6>
              </div>
              <div class="reservation-request-title">Your reservation request has been sent</div>
              <p class="reservation-request-description">
                Please wait for the restaurant to accept the reservation. An email<br>
                confirmation will be sent when approved. You can view the status<br>
                of your reservation in the profile section.
              </p>
              <div class="reservation-id">
                <p><strong>Reservation ID</strong> : {{ booking.booking_id }}</p>
              </div>
              <img class="request-sent-icon" src="assets/images/success-icon.svg" alt="Go-Grubz-success-image"
                loading="lazy">
              <div class="table-responsive">
                <table class="table">
                  <tr>
                    <td>Reservation Name:</td>
                    <td>{{ booking.customer_name }}</td>
                  </tr>
                  <tr>
                    <td>Email Address:</td>
                    <td>{{ booking.booking_email }}</td>
                  </tr>
                  <tr>
                    <td>Phone Number:</td>
                    <td>{{ booking.booking_phone }}</td>
                  </tr>
                  <tr>
                    <td>Number Of Guests:</td>
                    <td>{{ booking.guest_count }}</td>
                  </tr>
                  <tr>
                    <td>Selected Date:</td>
                    <td>{{ booking.booking_date }}</td>
                  </tr>
                  <tr>
                    <td>Selected Time:</td>
                    <td>{{ booking.booking_time }}</td>
                  </tr>
                  <tr>
                    <td>Special Requests:</td>
                    <td>
                      {{ booking.booking_instruction }}
                    </td>
                  </tr>
                </table>
              </div>
            </div>

          </div>


          <div class="col-xl-4 col-lg-4 text-end">
            <div class="restaurant-location-box">
              <div class="location-map" *ngIf="streetAddress && restaurant.street_address">
                <iframe class="border-0" [src]="streetAddress" allowfullscreen="" loading="lazy"
                  referrerpolicy="no-referrer-when-downgrade"></iframe>
              </div>
              <div class="location-details">
                <ul>
                  <li *ngIf="streetRedirectAddress">
                    <a [href]="streetRedirectAddress" target="_blank">
                      <img src="assets/images/location.svg" alt="Go-Grubz-location-image" loading="lazy">
                      {{ restaurant?.street_address }}
                      <span class="arrow-right">
                        <img src="assets/images/arrow-right.svg" alt="Go-Grubz-Right-Arrow-Image" loading="lazy">
                      </span>
                    </a>
                  </li>
                  <li>
                    <a class="collapsed" data-bs-toggle="collapse" href="#opening-times">
                      <img src="assets/images/clock.svg" alt="Go-Grubz-Clock-Image" loading="lazy">
                      Opening Times
                      <span class="arrow-right">
                        <i class="fa-solid fa-chevron-down"></i>
                      </span>
                    </a>
                    <div id="opening-times" class="collapse">
                      <ul class="nav nav-tabs">
                        <li [ngClass]="{'active' : timingOrderType == 'delivery'}">
                          <button (click)="timingOrderType = 'delivery'" data-toggle="tab"
                            class="cursor">Delivery</button>
                        </li>
                        <li [ngClass]="{'active' : timingOrderType == 'pickup'}">
                          <button (click)="timingOrderType = 'pickup'" data-toggle="tab" class="cursor">Pickup</button>
                        </li>
                      </ul>
                      <div class="tab-content">
                        <div id="delivery" class="tab-pane" [ngClass]="{'in active' : timingOrderType == 'delivery'}">
                          <ul>
                            <li>
                              <span>Monday</span>
                              <span
                                *ngIf="restaurant?.monday_status != 'Close' && (restaurant?.monday_first_opentime != '12:00 PM' || restaurant?.monday_first_closetime != '12:00 PM')">
                                {{restaurant?.monday_first_opentime}} -
                                {{restaurant?.monday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.monday_status != 'Close' && (restaurant?.monday_second_opentime != '12:00 PM' || restaurant?.monday_second_closetime != '12:00 PM')">
                                {{restaurant?.monday_second_opentime}} -
                                {{restaurant?.monday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.monday_status == 'Close'">Closed</span>
                            </li>
                            <li>
                              <span>Tuesday</span>
                              <span
                                *ngIf="restaurant?.tuesday_status != 'Close' && (restaurant?.tuesday_first_opentime != '12:00 PM' || restaurant?.tuesday_first_closetime != '12:00 PM')">
                                {{restaurant?.tuesday_first_opentime}} -
                                {{restaurant?.tuesday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.tuesday_status != 'Close' && (restaurant?.tuesday_second_opentime != '12:00 PM' || restaurant?.tuesday_second_closetime != '12:00 PM')">
                                {{restaurant?.tuesday_second_opentime}} -
                                {{restaurant?.tuesday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.tuesday_status == 'Close'">Closed</span>
                            </li>
                            <li>
                              <span>Wednesday</span>
                              <span
                                *ngIf="restaurant?.wednesday_status != 'Close' && (restaurant?.wednesday_first_opentime != '12:00 PM' || restaurant?.wednesday_first_closetime != '12:00 PM')">
                                {{restaurant?.wednesday_first_opentime}} -
                                {{restaurant?.wednesday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.wednesday_status != 'Close' && (restaurant?.wednesday_second_opentime != '12:00 PM' || restaurant?.wednesday_second_closetime != '12:00 PM')">
                                {{restaurant?.wednesday_second_opentime}} -
                                {{restaurant?.wednesday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.wednesday_status == 'Close'">Closed</span>
                            </li>
                            <li>
                              <span>Thursday</span>
                              <span
                                *ngIf="restaurant?.wednesday_status != 'Close' && (restaurant?.thursday_first_opentime != '12:00 PM' || restaurant?.thursday_first_closetime != '12:00 PM')">
                                {{restaurant?.thursday_first_opentime}} -
                                {{restaurant?.thursday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.thursday_status != 'Close' && (restaurant?.thursday_second_opentime != '12:00 PM' || restaurant?.thursday_second_closetime != '12:00 PM')">
                                {{restaurant?.thursday_second_opentime}} -
                                {{restaurant?.thursday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.thursday_status == 'Close'">Closed</span>
                            </li>
                            <li>
                              <span>Friday</span>
                              <span
                                *ngIf="restaurant?.friday_status != 'Close' && (restaurant?.friday_first_opentime != '12:00 PM' || restaurant?.friday_first_closetime != '12:00 PM')">
                                {{restaurant?.friday_first_opentime}} -
                                {{restaurant?.friday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.friday_status != 'Close' && (restaurant?.friday_second_opentime != '12:00 PM' || restaurant?.friday_second_closetime != '12:00 PM')">
                                {{restaurant?.friday_second_opentime}} -
                                {{restaurant?.friday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.friday_status == 'Close'">Closed</span>
                            </li>
                            <li>
                              <span>Saturday</span>
                              <span
                                *ngIf="restaurant?.saturday_status != 'Close' && (restaurant?.saturday_first_opentime != '12:00 PM' || restaurant?.saturday_first_closetime != '12:00 PM')">
                                {{restaurant?.saturday_first_opentime}} -
                                {{restaurant?.saturday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.saturday_status != 'Close' && (restaurant?.saturday_second_opentime != '12:00 PM' || restaurant?.saturday_second_closetime != '12:00 PM')">
                                {{restaurant?.saturday_second_opentime}} -
                                {{restaurant?.saturday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.saturday_status == 'Close'">Closed</span>
                            </li>
                            <li>
                              <span>Sunday</span>
                              <span
                                *ngIf="restaurant?.sunday_status != 'Close' && (restaurant?.sunday_first_opentime != '12:00 PM' || restaurant?.sunday_first_closetime != '12:00 PM')">
                                {{restaurant?.sunday_first_opentime}} -
                                {{restaurant?.sunday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.sunday_status != 'Close' && (restaurant?.sunday_second_opentime != '12:00 PM' || restaurant?.sunday_second_closetime != '12:00 PM')">
                                {{restaurant?.sunday_second_opentime}} -
                                {{restaurant?.sunday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.sunday_status == 'Close'">Closed</span>
                            </li>
                          </ul>
                        </div>
                        <div id="pickup" class="tab-pane" [ngClass]="{'in active' : timingOrderType == 'pickup'}">
                          <ul>
                            <li>
                              <span>Monday</span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_monday_status != 'Close' && (restaurant?.restaurant_timing?.pick_monday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_monday_first_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_monday_first_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_monday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_monday_status != 'Close' && (restaurant?.restaurant_timing?.pick_monday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_monday_second_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_monday_second_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_monday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.restaurant_timing?.pick_monday_status == 'Close'">Closed</span>
                            </li>
                            <li>
                              <span>Tuesday</span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_tuesday_status != 'Close' && (restaurant?.restaurant_timing?.pick_tuesday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_tuesday_first_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_tuesday_first_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_tuesday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_tuesday_status != 'Close' && (restaurant?.restaurant_timing?.pick_tuesday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_tuesday_second_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_tuesday_second_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_tuesday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.restaurant_timing?.pick_tuesday_status == 'Close'">Closed</span>
                            </li>
                            <li>
                              <span>Wednesday</span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_wednesday_status != 'Close' && (restaurant?.restaurant_timing?.pick_wednesday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_wednesday_first_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_wednesday_first_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_wednesday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_wednesday_status != 'Close' && (restaurant?.restaurant_timing?.pick_wednesday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_wednesday_second_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_wednesday_second_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_wednesday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.restaurant_timing?.pick_wednesday_status == 'Close'">Closed</span>
                            </li>
                            <li>
                              <span>Thursday</span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_wednesday_status != 'Close' && (restaurant?.restaurant_timing?.pick_thursday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_thursday_first_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_thursday_first_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_thursday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_thursday_status != 'Close' && (restaurant?.restaurant_timing?.pick_thursday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_thursday_second_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_thursday_second_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_thursday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.restaurant_timing?.pick_thursday_status == 'Close'">Closed</span>
                            </li>
                            <li>
                              <span>Friday</span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_friday_status != 'Close' && (restaurant?.restaurant_timing?.pick_friday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_friday_first_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_friday_first_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_friday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_friday_status != 'Close' && (restaurant?.restaurant_timing?.pick_friday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_friday_second_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_friday_second_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_friday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.restaurant_timing?.pick_friday_status == 'Close'">Closed</span>
                            </li>
                            <li>
                              <span>Saturday</span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_saturday_status != 'Close' && (restaurant?.restaurant_timing?.pick_saturday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_saturday_first_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_saturday_first_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_saturday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_saturday_status != 'Close' && (restaurant?.restaurant_timing?.pick_saturday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_saturday_second_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_saturday_second_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_saturday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.restaurant_timing?.pick_saturday_status == 'Close'">Closed</span>
                            </li>
                            <li>
                              <span>Sunday</span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_sunday_status != 'Close' && (restaurant?.restaurant_timing?.pick_sunday_first_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_sunday_first_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_sunday_first_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_sunday_first_closetime}}
                              </span>
                              <span
                                *ngIf="restaurant?.restaurant_timing?.pick_sunday_status != 'Close' && (restaurant?.restaurant_timing?.pick_sunday_second_opentime != '12:00 PM' || restaurant?.restaurant_timing?.pick_sunday_second_closetime != '12:00 PM')">
                                {{restaurant?.restaurant_timing?.pick_sunday_second_opentime}}
                                -
                                {{restaurant?.restaurant_timing?.pick_sunday_second_closetime}}
                              </span>
                              <span class="col-sm-9 col-md-9 text-end text-danger fw-bold"
                                *ngIf="restaurant?.restaurant_timing?.pick_sunday_status == 'Close'">Closed</span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <a href="tel:(+44) {{ restaurant?.restaurant_phone }};">
                      <img src="assets/images/phone.svg" alt="Go-Grubz-Phone-Image" loading="lazy">
                      (+44) {{ restaurant?.restaurant_phone }}
                      <span class="arrow-right">
                        <img src="assets/images/arrow-right.svg" alt="Go-Grubz-Right-Image" loading="lazy">
                      </span>
                    </a>
                  </li>
                  <!-- <li *ngIf="restaurant?.is_allergy">
                                        <a (click)="allergyShow()">
                                            <img src="assets/images/allergy.png" alt="allergy">
                                            Allergy
                                            <span class="arrow-right">
                                                <img src="assets/images/arrow-right.svg" alt="arrow-right" loading="lazy">
                                            </span>
                                        </a>
                                    </li> -->
                  <li *ngIf="restaurant.hygiene_link">
                    <a class="d-flex" [href]="restaurant.hygiene_link" target="_blank">
                      <img src="assets/images/hygiene.svg" alt="Go-Grubz-Hygiene-Image" loading="lazy">
                      Hygiene Rating
                      <img class="hygiene-rating-img" src="assets/images/hygiene-rating.svg"
                        alt="Go-Grubz-Hygiene-Image" loading="lazy">
                      <span class="arrow-right">
                        <img src="assets/images/arrow-right.svg" alt="Go-Grubz-Arrow-Image" loading="lazy">
                      </span>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</section>

<!-- Allergy-Modal -->
<ng-template #allergyModal let-modal>
  <div id="allergy-popup">
    <div class="modal-header justify-content-center border-0">
      <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
        <i class="fa-solid fa-xmark"></i>
      </button>
      <h5 class="login-title pt-lg-1 mt-2 m-0">Do you have a food allergy?</h5>
    </div>
    <div class="modal-body login-body">
      <p class="text-center" *ngIf="restaurant?.allergy_message">{{restaurant?.allergy_message}}</p>
      <button class="btn mt-3 modal-black-btn" type="button" (click)="modal.dismiss('Cross click')">Close</button>
    </div>
  </div>
</ng-template>

<!-- Login-Modal -->
<ng-template #loginModal let-modal>
  <div id="login-modal">
    <div class="modal-content">
      <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
        <i class="fa-solid fa-xmark"></i>
      </button>
      <div class="modal-body login-body">
        <h5 class="login-title">Sign In</h5>
        <p class="dont-have-account-text">
          Don’t have an account? <button data-bs-toggle="modal" (click)="openModal(signupModal)">Sign up
            here.</button>
        </p>

        <form nz-form #loginForm="ngForm" (ngSubmit)="loginSubmit(loginForm)" nzLayout="vertical">
          <div class="form-group">
            <nz-form-item>
              <nz-form-control nzHasFeedback [nzErrorTip]="reslogemailErrorTpl">
                <nz-input-group>
                  <input class="form-control" type="email" email="true"
                    pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$" #reslogEmail="ngModel" nz-input
                    name="lusername" (keydown.space)="onSpaceKeyDown($event)" id="lusername"
                    [(ngModel)]="secondUser.username" required placeholder="Email Address">
                </nz-input-group>
                <ng-template #reslogemailErrorTpl let-control>
                  <ng-container *ngIf="control.hasError('required')">
                    Please enter your email!
                  </ng-container>
                  <ng-container
                    *ngIf="control.hasError('email') || (!control.hasError('required') && reslogEmail.touched) || (!control.hasError('required') && !reslogEmail.valid)">
                    Email must be a valid email address
                  </ng-container>
                </ng-template>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div class="form-group">
            <nz-form-item>
              <nz-form-control nzHasFeedback [nzErrorTip]="passwordErrorTpl">
                <nz-input-group>
                  <input class="form-control" [type]="lhide ? 'password' : 'text'" nz-input
                    (keydown.space)="onSpaceKeyDown($event)" name="password" id="password"
                    [(ngModel)]="secondUser.password" required placeholder="Password">
                </nz-input-group>

                <ng-template #passwordErrorTpl let-control>
                  <ng-container *ngIf="control.hasError('required')">
                    Please enter your password!
                  </ng-container>
                  <ng-container *ngIf="control.hasError('minlength')">
                    Password must be atleast 6 characters long!
                  </ng-container>
                </ng-template>
              </nz-form-control>
            </nz-form-item>
            <span class="icon" (click)="hideLpassword()">
              <img src="assets/images/eye.svg" *ngIf="lhide" alt="Go-Grubz-Eye-Image" loading="lazy">
              <img src="assets/images/eye-off.svg" *ngIf="!lhide" alt="Go-Grubz-Eye-Off-Image" loading="lazy">
            </span>
          </div>

          <nz-form-item *ngIf="error">
            <span class="text-danger">{{ error }}</span>
          </nz-form-item>
          <div class="forgot-password text-end">
            <span data-bs-toggle="modal" (click)="openModal(forgotModal)" class="cursor">Forgot
              Password?</span>
          </div>
          <button class="btn" nz-button [disabled]="isLoading">
            <i class="spinner-border" *ngIf="isLoading"></i>
            Sign In
          </button>

          <!-- <div class="or-option"
                        *ngIf="siteSetting.google_login == 'Y' || siteSetting.facebook_login == 'Y' || siteSetting.apple_login == 'Y'">
                        <span>or</span>
                    </div>

                    <div class="socials-login-options">
                        <ul>
                            <li *ngIf="siteSetting.google_login == 'Y'">
                                <a class="google-btn" (click)="loginWithGoogle()">
                                    <img src="assets/images/google.svg" alt="google">
                                    Continue with Google
                                </a>
                            </li>
                            <li *ngIf="siteSetting.facebook_login == 'Y'">
                                <a class="facebook-btn" (click)="loginWithFacebook()">
                                    <img src="assets/images/facebook.svg" alt="google">
                                    Continue with Facebook
                                </a>
                            </li>
                            <li *ngIf="siteSetting.apple_login == 'Y'">
                                <a class="apple-btn">
                                    <img src="assets/images/white-apple.svg" alt="google">
                                    Continue with Apple
                                </a>
                            </li>
                        </ul>
                    </div> -->
        </form>
      </div>
    </div>
  </div>
</ng-template>

<!-- Sign-up-Modal -->
<ng-template #signupModal let-modal>
  <div id="signup-modal">
    <div class="modal-content">
      <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
        <i class="fa-solid fa-xmark"></i>
      </button>
      <div class="modal-body login-body">
        <h5 class="login-title">Sign Up</h5>
        <p class="dont-have-account-text">
          Already have an account? <button data-bs-toggle="modal" (click)="openModal(loginModal)">Sign in
            here.</button>
        </p>
        <form nz-form #signupForm="ngForm" (ngSubmit)="onSignupSubmit(signupForm)" nzLayout="vertical">
          <div class="row">

            <div class="col-md-6">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback nzErrorTip="Please enter first name!">
                    <nz-input-group>
                      <input class="form-control" type="text" nz-input (keydown.space)="onSpaceKeyDown($event)"
                        name="first_name" id="first_name" [(ngModel)]="firstUser.first_name" required
                        placeholder="First Name">
                    </nz-input-group>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <div class="col-md-6">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback nzErrorTip="Please enter last name!">
                    <nz-input-group>
                      <input class="form-control" type="text" nz-input (keydown.space)="onSpaceKeyDown($event)"
                        name="last_name" id="last_name" [(ngModel)]="firstUser.last_name" required
                        placeholder="Last Name">
                    </nz-input-group>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback [nzErrorTip]="ressignemailErrorTpl">
                    <nz-input-group>
                      <input class="form-control" type="email" email="true"
                        pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$" #resSignEmail="ngModel" nz-input
                        (keydown.space)="onSpaceKeyDown($event)" name="username" id="username"
                        [(ngModel)]="firstUser.username" required placeholder="Email Address">
                    </nz-input-group>
                    <ng-template #ressignemailErrorTpl let-control>
                      <ng-container *ngIf="control.hasError('required')">
                        Please enter your email!
                      </ng-container>
                      <ng-container
                        *ngIf="control.hasError('email') || (!control.hasError('required') && resSignEmail.touched) || (!control.hasError('required') && !resSignEmail.valid)">
                        Email must be a valid email address
                      </ng-container>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback nzErrorTip="Please enter phone number!">
                    <nz-input-group>
                      <input class="form-control" type="text" inputmode="numeric" nz-input
                        (keydown.space)="onSpaceKeyDown($event)" name="phone_number" id="phone_number"
                        (keypress)="validateMobile($event)" [(ngModel)]="firstUser.phone_number" required
                        placeholder="Mobile Number">
                    </nz-input-group>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback [nzErrorTip]="passwordRegErrorTpl">
                    <nz-input-group>
                      <input class="form-control" [type]="hide ? 'password' : 'text'" nz-input
                        (keydown.space)="onSpaceKeyDown($event)" name="password" id="password" minlength="6"
                        [(ngModel)]="firstUser.password" required placeholder="Password">
                      <span class="icon" (click)="hidePassword()">
                        <img class="cursor" src="assets/images/eye.svg" *ngIf="hide" alt="Go-Grubz-Eye-Image"
                          loading="lazy">
                        <img class="cursor" src="assets/images/eye-off.svg" *ngIf="!hide" alt="Go-Grubz-Eye-Off-Image"
                          loading="lazy">
                      </span>
                    </nz-input-group>

                    <ng-template #passwordRegErrorTpl let-control>
                      <ng-container *ngIf="control.hasError('required')">
                        Please enter your password!
                      </ng-container>
                      <ng-container *ngIf="control.hasError('minlength')">
                        Password must be atleast 6 characters long!
                      </ng-container>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback nzErrorTip="Please enter confirm password!">
                    <nz-input-group>
                      <input class="form-control" [type]="chide ? 'password' : 'text'" nz-input name="confirmPassword"
                        id="confirmPassword" (keydown.space)="onSpaceKeyDown($event)"
                        [(ngModel)]="firstUser.confirmPassword" required placeholder="Confirm Password">
                      <span class="icon" (click)="hideCpassword()">
                        <img class="cursor" src="assets/images/eye.svg" *ngIf="chide" alt="Go-Grubz-Eye-Image"
                          loading="lazy">
                        <img class="cursor" src="assets/images/eye-off.svg" *ngIf="!chide" alt="Go-Grubz-Eye-Off-Image"
                          loading="lazy">
                      </span>
                    </nz-input-group>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>

            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback nzErrorTip="Please enter referred code!">
                    <nz-input-group>
                      <input class="form-control" type="text" nz-input name="referred_by"
                        (keydown.space)="onSpaceKeyDown($event)" id="referred_by" [(ngModel)]="firstUser.referred_by"
                        placeholder="Referral Code (optional)">
                    </nz-input-group>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
          </div>

          <div class="terms-conditions">
            <p>
              By signing up or using “Continue with Google, Facebook, or Apple,” you agree to Go
              Grubz’s <a href="/terms-condition" target="_blank">Terms and Conditions</a> and
              <a href="/privacy-policy" target="_blank">Privacy Policy.</a>
            </p>
          </div>

          <nz-form-item *ngIf="SignModelerror">
            <span class="text-danger">{{ SignModelerror }}</span>
          </nz-form-item>

          <button class="btn" nz-button [disabled]="isSignModelLoading">
            <i class="spinner-border" *ngIf="isSignModelLoading"></i>
            Sign Up
          </button>

          <!-- <div class="or-option"
                        *ngIf="siteSetting.google_login == 'Y' || siteSetting.facebook_login == 'Y' || siteSetting.apple_login == 'Y'">
                        <span>or</span>
                    </div>

                    <div class="socials-login-options">
                        <ul>
                            <li *ngIf="siteSetting.google_login == 'Y'">
                                <a class="google-btn" (click)="loginWithGoogle()">
                                    <img src="assets/images/google.svg" alt="google">
                                    Continue with Google
                                </a>
                            </li>
                            <li *ngIf="siteSetting.facebook_login == 'Y'">
                                <a class="facebook-btn" (click)="loginWithFacebook()">
                                    <img src="assets/images/facebook.svg" alt="google">
                                    Continue with Facebook
                                </a>
                            </li>
                            <li *ngIf="siteSetting.apple_login == 'Y'">
                                <a class="apple-btn">
                                    <img src="assets/images/white-apple.svg" alt="google">
                                    Continue with Apple
                                </a>
                            </li>
                        </ul>
                    </div> -->

        </form>
      </div>

    </div>
  </div>
</ng-template>

<!-- Forgot-Password-Modal -->
<ng-template #forgotModal let-modal>
  <div id="forgot-password-popup">
    <div class="modal-content">
      <button type="button" class="btn-close" #closeForgotUpModal data-bs-dismiss="modal"
        (click)="modal.dismiss('Cross click')">
        <i class="fa-solid fa-xmark"></i>
      </button>
      <div class="modal-body login-body">
        <h5 class="login-title">Forgot Password</h5>
        <!-- <p class="dont-have-account-text">Already have an account? <button data-bs-toggle="modal"
                    href="#login-modal">Sign in
                    here.</button></p> -->

        <form nz-form #forgotForm="ngForm" (ngSubmit)="onSubmitForgot(forgotForm)" *ngIf="!otpVisible">
          <p class="dont-have-account-text">Please enter the e-mail address used to register. We will send
            your
            new OTP to that address.</p>
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <nz-form-item>
                  <nz-form-control nzHasFeedback [nzErrorTip]="resforemailErrorTpl">
                    <nz-input-group>
                      <input class="form-control" type="email" email="true"
                        pattern="^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$" #resForEmail="ngModel" nz-input
                        email="true" (keydown.space)="onSpaceKeyDown($event)" [(ngModel)]="forgotUser.username" required
                        id="otp-verification-email" placeholder="Enter your email">
                    </nz-input-group>
                    <ng-template #resforemailErrorTpl let-control>
                      <ng-container *ngIf="control.hasError('required')">
                        Please enter your email!
                      </ng-container>
                      <ng-container
                        *ngIf="control.hasError('email') || (!control.hasError('required') && resForEmail.touched) || (!control.hasError('required') && !resForEmail.valid)">
                        Email must be a valid email address
                      </ng-container>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
          </div>

          <nz-form-item *ngIf="Modelerror">
            <span class="text-danger">{{ Modelerror }}</span>
          </nz-form-item>

          <button class="btn" nz-button [disabled]="isModelLoading">
            <i class="spinner-border" *ngIf="isModelLoading"></i>
            Send
          </button>
          <div class="back-to-login">
            <span>Back to</span> <a class="cursor" data-bs-toggle="modal" (click)="openModal(loginModal)">Sign in</a>
          </div>
        </form>

        <form nz-form #otpForm="ngForm" (ngSubmit)="onNewSubmitOtp(otpForm)" *ngIf="otpVisible">
          <p class="dont-have-account-text text-success"> Check your email for OTP.</p>
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <label class="form-label">
                  Enter OTP
                </label>
                <!-- <div class="form-otp-list">
                                    <input class="form-control" type="text" inputmode="numeric" nz-input required
                                        maxlength="1" (input)="handleInput($event, 0)" name="otp-input-1"
                                        id="otp-input-1" autocomplete="off">
                                    <input class="form-control" type="text" inputmode="numeric" nz-input required
                                        maxlength="1" (input)="handleInput($event, 1)" name="otp-input-2"
                                        id="otp-input-2" autocomplete="off">
                                    <input class="form-control" type="text" inputmode="numeric" nz-input required
                                        maxlength="1" (input)="handleInput($event, 2)" name="otp-input-3"
                                        id="otp-input-3" autocomplete="off">
                                    <input class="form-control" type="text" inputmode="numeric" nz-input required
                                        maxlength="1" (input)="handleInput($event, 3)" name="otp-input-4"
                                        id="otp-input-4" autocomplete="off">
                                    <input class="form-control" type="text" inputmode="numeric" nz-input required
                                        maxlength="1" (input)="handleInput($event, 4)" name="otp-input-5"
                                        id="otp-input-5" autocomplete="off">
                                    <input class="form-control" type="text" inputmode="numeric" nz-input required
                                        maxlength="1" (input)="handleInput($event, 5)" name="otp-input-6"
                                        id="otp-input-6" autocomplete="off">
                                </div>
                                <input type="text" nz-input [value]="emailOtp" hidden readonly /> -->

                <nz-form-item>
                  <nz-form-control nzHasFeedback [nzErrorTip]="phoneVeriErrorTpl">
                    <nz-input-group>
                      <input type="text" inputmode="numeric" class="form-control" nz-input minlength="6"
                        (keydown.space)="onSpaceKeyDown($event)" maxlength="6" id="email_otp" name="email_otp"
                        (keypress)="validateMobile($event)" [(ngModel)]="forgotUser.email_otp" required
                        placeholder="Enter 6-digit code">
                    </nz-input-group>
                    <ng-template #phoneVeriErrorTpl let-control>
                      <ng-container *ngIf="control.hasError('required')">
                        Please enter verification code!
                      </ng-container>
                      <ng-container *ngIf="control.hasError('minlength')">
                        statement verification code at least 6 digit!
                      </ng-container>
                      <ng-container *ngIf="control.hasError('maxlength')">
                        statement verification code maximum 6 digits long!
                      </ng-container>
                    </ng-template>
                  </nz-form-control>
                </nz-form-item>

              </div>
            </div>
          </div>

          <nz-form-item *ngIf="Otperror">
            <span class="text-danger">{{ Otperror }}</span>
          </nz-form-item>

          <button class="btn" nz-button [disabled]="isOtpLoading">
            <i class="spinner-border" *ngIf="isOtpLoading"></i>
            Verify
          </button>
          <div class="resend-code text-center">
            <a class="cursor" (click)="forgotPassword(forgotForm)">
              Resend OTP
            </a>
          </div>
        </form>
      </div>
    </div>
  </div>
</ng-template>

<!-- New-Password-Modal -->
<ng-template #newPassword let-modal>
  <div id="new-password-popup">
    <button type="button" class="btn-close" data-bs-dismiss="modal" (click)="modal.dismiss('Cross click')">
      <i class="fa-solid fa-xmark"></i>
    </button>
    <div class="modal-body login-body">
      <h5 class="login-title">New Password</h5>
      <!-- <p class="dont-have-account-text">Already have an account? <a data-bs-toggle="modal" href="#login-modal">Sign in
            here.</a></p> -->
      <form nz-form #changePasswordForm="ngForm" (ngSubmit)="onChangePasswordSubmit(changePasswordForm)">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <!-- <input class="form-control" type="password" id="new-email" placeholder="Enter New Password"> -->
              <nz-form-item>
                <nz-form-control nzHasFeedback [nzErrorTip]="passwordChnageErrorTpl">
                  <nz-input-group>
                    <input type="password" id="password" name="password" [(ngModel)]="forgotUser.password" required
                      minlength="6" placeholder="Enter new password" class="form-control">
                  </nz-input-group>

                  <ng-template #passwordChnageErrorTpl let-control>
                    <ng-container *ngIf="control.hasError('required')">
                      Please enter your current password!
                    </ng-container>
                    <ng-container *ngIf="control.hasError('minlength')">
                      Password must be atleast 6 characters long!
                    </ng-container>
                  </ng-template>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <div class="col-md-12">
            <div class="form-group">
              <!-- <input class="form-control" type="password" id="new-email" placeholder="Enter Confirm Password"> -->
              <nz-form-item>
                <nz-form-control nzHasFeedback nzErrorTip="Please enter confirm password!">
                  <nz-input-group>
                    <input type="password" id="confirmPassword" name="confirmPassword"
                      [(ngModel)]="forgotUser.confirmPassword" required placeholder="Enter your confirm password"
                      class="form-control">
                  </nz-input-group>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </div>

        <nz-form-item *ngIf="errorChangePassword">
          <span class="text-danger">{{ errorChangePassword }}</span>
        </nz-form-item>

        <button class="btn" nz-button [disabled]="isChangePasswordLoading">
          <i class="spinner-border" *ngIf="isChangePasswordLoading"></i>
          Submit
        </button>
      </form>
    </div>
  </div>
</ng-template>

<!-- Edit-Phone-Number-Popup -->
<ng-template #profileModal let-modal>
  <div id="edit-number-popup">
    <button type="button" class="btn-close" data-bs-dismiss="modal"
      (click)="modal.dismiss('Cross click');counter=20;mySubscription.unsubscribe()">
      <i class="fa-solid fa-xmark"></i>
    </button>
    <form nz-form #userForm="ngForm" (ngSubmit)="updateUser(userForm)">
      <div class="modal-body">
        <img class="verification-icon" src="assets/images/verification.png" alt="GoGrubz-verification">
        <h6 class="mb-4">Edit Phone Number</h6>
        <div class="form-group">
          <nz-form-item>
            <nz-form-control nzHasFeedback [nzErrorTip]="phoneErrorTpl">
              <nz-input-group>
                <input type="text" inputmode="numeric" class="form-control" nz-input
                  (keydown.space)="onSpaceKeyDown($event)" (keypress)="validateMobile($event)" id="phone_number"
                  name="phone_number" [(ngModel)]="loginuser.phone_number" required placeholder="Enter phone number">
              </nz-input-group>
              <ng-template #phoneErrorTpl let-control>
                <ng-container *ngIf="control.hasError('required')">
                  Please enter mobile number!
                </ng-container>
                <ng-container *ngIf="control.hasError('minlength')">
                  statement mobile number at least 10 digit!
                </ng-container>
                <ng-container *ngIf="control.hasError('maxlength')">
                  statement mobile number maximum 11 digits long!
                </ng-container>
              </ng-template>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
      <div class="modal-footer">

        <nz-form-item *ngIf="ModelProfileerror">
          <span class="text-danger">{{ ModelProfileerror }}</span>
        </nz-form-item>

        <button class="btn modal-black-btn" nz-button>
          <i class="spinner-border" *ngIf="isModelProfileLoading"></i>
          Save
        </button>
      </div>
    </form>
  </div>
</ng-template>

<!-- Phone-Number-Verification-Popup -->
<ng-template #otpModal let-modal>
  <div id="number-verification-popup">
    <div class="modal-body">
      <button type="button" class="btn-close" data-bs-dismiss="modal"
        (click)="modal.dismiss('Cross click');counter=20;mySubscription.unsubscribe()">
        <!-- <i class="fa-solid fa-xmark"></i> -->
      </button>
      <form nz-form #otpForm="ngForm" (ngSubmit)="onSubmitOtp(otpForm)">
        <div class="modal-body">
          <div class="text-center">
            <img class="verification-icon" src="assets/images/verification.png" alt="GoGrubz-verification">
          </div>
          <h6>Phone Number Verification</h6>
          <p>Before we place your order, we need to verify your phone<br> number for security purposes</p>
          <div class="form-group">
            <nz-form-item>
              <nz-form-control nzHasFeedback [nzErrorTip]="phoneVeriErrorTpl">
                <nz-input-group>
                  <input type="text" inputmode="numeric" class="form-control" nz-input minlength="6"
                    (keydown.space)="onSpaceKeyDown($event)" maxlength="6" id="otp" name="otp"
                    (keypress)="validateMobile($event)" [(ngModel)]="user.otp" required
                    placeholder="Enter 6-digit code">
                </nz-input-group>
                <ng-template #phoneVeriErrorTpl let-control>
                  <ng-container *ngIf="control.hasError('required')">
                    Please enter verification code!
                  </ng-container>
                  <ng-container *ngIf="control.hasError('minlength')">
                    statement verification code at least 6 digit!
                  </ng-container>
                  <ng-container *ngIf="control.hasError('maxlength')">
                    statement verification code maximum 6 digits long!
                  </ng-container>
                </ng-template>
              </nz-form-control>
            </nz-form-item>
          </div>
          <span>We sent a code to {{ user.phone_number }}</span>
          <ul class="more-option">
            <li><a class="d-flex text-nowrap cursor" (click)="resendOtp()"
                [style.pointer-events]="counter >= 1 ?'none':'auto'" [ngClass]="{'opacity-resend' : counter > 0}">
                Resend
                Code
                <span class="ps-2" *ngIf="counter > 1"></span>({{ counter }})
              </a>
            </li>
            <li><i class="fa-solid fa-circle"></i></li>
            <li><a class="cursor" (click)="openPhoneEdit()"> Update Number</a></li>
          </ul>
        </div>
        <div class="modal-footer">
          <nz-form-item *ngIf="Modelotperror">
            <span class="text-danger">{{ Modelotperror }}</span>
          </nz-form-item>

          <button class="btn modal-black-btn" nz-button [disabled]="isModelOtpLoading">
            <i class="spinner-border" *ngIf="isModelOtpLoading"></i>
            Submit
          </button>
        </div>
      </form>
    </div>
  </div>
</ng-template>

<script>
  window.dataLayer = window.dataLayer || [];
  function gtag() { dataLayer.push(arguments); }
  gtag('js', new Date());

  gtag('config', 'G-DMXH9C9D17');
</script>