<div class="container mt-2 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">
    <div class="col-xs-12 col-sm-12 mt-2">

      <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="order.status == 'Pending'">Your order has been placed</h3>
      <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="order.status == 'Pending'">Waiting for restaurant to accept
      </h3>
      <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="order.status == 'Accepted'">Your order preparation time</h3>
      <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="order.status == 'Delivered'">Your order has been Delivered</h3>
      <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="order.status == 'Collected'">Your order has been collected</h3>
      <h3 class="col-sm-12 col-xs-12 text-center"
        *ngIf="order.status == 'Waiting' || order.status == 'Driver Accepted'">Your order preparation time</h3>
      <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="order.status == 'Failed'">Your order has been rejected</h3>


      <div class="center">
        <ng-lottie width="300px" height="150px" [options]="optionPending" loop autoplay
          *ngIf="order.status == 'Pending'" containerClass="moving-box another-class">
        </ng-lottie>
        <ng-lottie width="300px" height="150px" [options]="optionAccepted" loop autoplay
          *ngIf="order.status == 'Accepted'" containerClass="moving-box another-class">
        </ng-lottie>
        <ng-lottie width="300px" height="150px" [options]="optionDelivered" loop autoplay
          *ngIf="order.status == 'Delivered'" containerClass="moving-box another-class">
        </ng-lottie>
        <ng-lottie width="300px" height="150px" [options]="optionCollected" loop autoplay
          *ngIf="order.status == 'Collected'" containerClass="moving-box another-class">
        </ng-lottie>
        <ng-lottie width="300px" height="150px" [options]="optionWaiting" loop autoplay
          *ngIf="order.status == 'Waiting' || order.status == 'Driver Accepted'"
          containerClass="moving-box another-class">
        </ng-lottie>
        <ng-lottie width="300px" height="150px" [options]="optionFailed" loop autoplay *ngIf="order.status == 'Failed'"
          containerClass="moving-box another-class">
        </ng-lottie>
      </div>

      <div class="col-xs-12 col-sm-12 align-items-center text-center" *ngIf="order.status == 'Accepted'">
        <div class="p-2 d-flex justify-content-center"
          *ngIf="daysToDday>0 || hoursToDday>0 || minutesToDday>0 || secondsToDday>0 ">
          <span class="bg-white body-box-shadow rounded h-100 p-2 m-2">{{daysToDday}}
            <p class="m-0"> Day</p>
          </span>
          <span class="bg-white body-box-shadow rounded h-100 p-2 m-2">
            {{hoursToDday}}
            <p class="m-0"> Hours</p>
          </span>
          <span class="bg-white body-box-shadow rounded h-100 p-2 m-2">
            {{minutesToDday}}
            <p class="m-0"> Minutes</p>
          </span>
          <span class="bg-white body-box-shadow rounded h-100 p-2 m-2">
            {{secondsToDday}}
            <p class="m-0"> Seconds</p>
          </span>
        </div>
      </div>

      <div class="stepper d-flex align-items-center justify-content-between py-4" *ngIf="order.status != 'Failed'">
        <ng-container *ngFor="let status of orderStatuses">
          <div class="flex-fill text-center strikethrough"
            *ngIf="status.order_type=='all' || status.order_type==order.order_type">
            <div class="status d-flex flex-column align-items-center" [class.active]="status.checked">
              <div class="dot text-center" [class.active]="status.checked"></div>
              <div class="title" [class.active]="status.checked">{{status.title}}</div>
            </div>
          </div>
        </ng-container>
      </div>

      <div class="stepper d-flex align-items-center justify-content-between py-4" *ngIf="order.status == 'Failed'">
        <ng-container *ngFor="let status of orderRejectStatuses">
          <div class="flex-fill text-center strikethrough"
            *ngIf="status.order_type=='all' || status.order_type==order.order_type">
            <div class="status d-flex flex-column align-items-center" [class.active]="status.checked">
              <div class="dot text-center" [class.active]="status.checked"></div>
              <div class="title" [class.active]="status.checked">{{status.title}}</div>
            </div>
          </div>
        </ng-container>
      </div>

      <div class="col-12">
        <h6 class="col-12 text-center fw-bold" *ngIf="order.status == 'Pending'">Thank you, your order have been
          successfully placed. Please wait for the restaurant to confirm your order and delivery time.</h6>
        <h6 class="col-12 text-center fw-bold px-md-3" *ngIf="order.status != 'Delivered'">
          In order to receive updates on your order via email or SMS, please verify your email and phone number. This
          can be done on the profile section.
          <br>Please contact the restaurant if you do not receive any notifications once verified.
        </h6>
        <h6 class="col-12 text-center fw-bold" *ngIf="order.status == 'Accepted'">Your order has been accepted,
          pleased wait while they prepare your food.</h6>
        <h6 class="col-12 text-center fw-bold" *ngIf="order.status == 'Delivered'">Enjoy your meal. Remember to
          leave a review once your finished.</h6>
        <h6 class="col-12 text-center fw-bold" *ngIf="order.status == 'Collected'">Sit tight, your meal is on its
          way to you.</h6>
        <h6 class="col-12 text-center fw-bold" *ngIf="order.status == 'Waiting' || order.status == 'Driver Accepted'">
          Your meal is being cooked up by the chef.
        </h6>
        <h6 class="col-12 text-center fw-bold" *ngIf="order.status == 'Failed'">cannot be accepted at the moment.
        </h6>
      </div>
    </div>

    <div class="col-xs-12 col-sm-6 mt-2">
      <div class="bg-white body-box-shadow rounded p-2">
        <h5 class="col-12 text-center">Order Info</h5>
        <div class="panel-body">
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Order ID</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">#{{order.order_number}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Restaurant Name</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{restaurant.restaurant_name}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Order Type</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{order.order_type | titlecase}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2"
            *ngIf="order.address && order.order_type == 'delivery'">
            <span class="col-4">Address</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{order.address}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Delivery Time</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{order.delivery_date}} {{order.delivery_time}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Order At</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{convertToDate(order.created)}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2">
            <span class="col-4">Order Status</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">
              {{order.status == 'Failed'?order.status == 'Delivered'&& order.order_type !=
              'delivery'?'Pickedup':order.status:order.status}}
            </span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2" *ngIf="order.status == 'Failed'">
            <span class="col-4">Rejected Reason</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{order.failed_reason}}</span>
          </div>
          <div class="col-12 d-flex justify-content-between py-2" *ngIf="order.order_description">
            <span class="col-4">Order Instruction</span>
            <span class="col-1">:</span>
            <span class="col-7 fw-bold">{{order.order_description}}</span>
          </div>

        </div>
      </div>
    </div>

    <div class="col-xs-12 col-sm-6 mt-2">
      <div class="bg-white body-box-shadow rounded p-2">
        <div class="text-center">
          <div class="card-body cart p-0" *ngIf="order?.cart_view?.length != 0"
            style="max-height: calc(100vh - 65vh);overflow-y: auto;">
            <div class="col-12 text-start" *ngIf="order?.cart_view?.length > 0">
              <div class="col-12 d-flex p-2 fw-bold border-bottom bg-light">
                <div class="col-9">Item Name</div>
                <div class="col-1 text-center">Qty</div>
                <div class="col-2 text-end">Total</div>
              </div>
              <div class="fw-bold p-1 col-12  border-bottom" *ngFor=" let cart of order.cart_view; let i=index">
                <div class="col-12 d-flex p-1">
                  <div class="col-9">
                    <div class="w-100 ml-1">{{cart.menu_name}}</div>
                    <div class="w-100" style="font-size: 12px;color: #999;">{{cart.subaddons_name}}</div>
                  </div>
                  <span class="col-1 text-center">{{cart.quantity}}</span>
                  <span class="col-2 text-end">{{convertNumber(cart.total_price)}}</span>
                </div>
              </div>
              <div class="col-12 d-flex p-2 fw-bold border-bottom bg-light" *ngIf="order?.applied_offers?.length > 0">
                <div class="col-12 text-center">Offer Items</div>
              </div>
              <div class="fw-bold p-1 col-12  border-bottom"
                *ngFor=" let appliedOffer of order.applied_offers; let i=index">
                <div class="col-12 d-flex p-1">
                  <div class="col-9">
                    <div class="w-100 ml-1">{{appliedOffer.menu_name}}</div>
                    <div class="w-100" style="font-size: 12px;color: #999;">{{appliedOffer.subaddons_name}}</div>
                  </div>
                  <span class="col-1 text-center">{{appliedOffer.quantity}}</span>
                  <span class="col-2 text-end">
                    {{appliedOffer.total_price==0?'Free':convertNumber(appliedOffer.total_price)}}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom">
            <span class="col-10 text-start">Sub Total</span>
            <span class="col-2 text-end">{{convertNumber(order.order_sub_total)}}</span>
          </div>

          <div *ngIf="order?.surcharges?.length > 0">
            <div class="col-12 p-2 d-flex justify-content-between border-bottom"
              *ngFor="let surcharge of order.surcharges">
              <span class="col-10 text-start">{{surcharge.surcharge_name}}</span>
              <span class="col-2 text-end">{{convertNumber(surcharge.surcharge_amount)}}</span>
            </div>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom" *ngIf="order.service_charge > 0">
            <span class="col-10 text-start">Service Charge</span>
            <span class="col-2 text-end">{{convertNumber(order.service_charge)}}</span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom"
            *ngIf="order.order_type == 'delivery' && order.delivery_charge > 0">
            <span class="col-10 text-start">Delivery Charge</span>
            <span class="col-2 text-end">{{convertNumber(order.delivery_charge)}}</span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="order.voucher_code && order.voucher_amount > 0">
            <span class="col-9 text-start">Voucher ({{order.voucher_code}})</span>
            <span class="col-3 text-end">
              (-) {{convertNumber(order.voucher_amount)}}
            </span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="order.offer_amount > 0">
            <span class="col-9 text-start">Offer
              {{order.offer_percentage?'('+order.offer_percentage+'%)':''}}</span>
            <span class="col-3 text-end">
              (-) {{convertNumber(order.offer_amount)}}
            </span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="order.charity_amount > 0">
            <span class="col-9 text-start">Charity {{order.charity_message}}</span>
            <span class="col-3 text-end">
              {{convertNumber(order.charity_amount)}}
            </span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="order.reward_offer > 0 && order.reward_used == 'Y'">
            <span class="col-9 text-start">Redeem Offer
              {{order.reward_offer_percentage?'('+order.reward_offer_percentage+'%)':''}}</span>
            <span class="col-3 text-end">
              (-) {{convertNumber(order.reward_offer)}}
            </span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="order.driver_tip > 0">
            <span class="col-9 text-start">Driver Tip</span>
            <span class="col-3 text-end">
              {{convertNumber(order.driver_tip)}}
            </span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="order.wallet_amount > 0">
            <span class="col-9 text-start">
              <img src="./assets/wallet.png" class="px-2">
              My Wallet </span>
            <span class="col-3 text-end">
              (-) {{convertNumber(order.wallet_amount)}}
            </span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between fw-bold border-bottom">
            <span class="col-10 text-start">Total Amount</span>
            <span class="col-2 text-end">{{convertNumber(order.order_grand_total + order.charity_amount)}}</span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="order.split_payment == 'Yes'">
            <span class="col-9 text-start">Paid via Wallet + {{order.payment_method ==
              'cod'?'Cash':order.payment_method ==
              'Stripe'?'Credit/Debit Card':'Paypal'}}</span>
            <span class="col-3 text-end">
              <img src="./assets/payment-logo/paypal-small-logo.png" alt="paypal tiffintom" *ngIf="order.payment_method ==
                'paypal'">
              <img src="./assets/payment-logo/cod2.png" alt="paypal tiffintom" *ngIf="order.payment_method ==
                'cod'">
              <span *ngIf="order.payment_method == 'Stripe'"> XXXX-{{order.card_view?.card_number}}</span>
            </span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="order.split_payment == 'No'">
            <span class="col-9 text-start" *ngIf="order.payment_method != 'cod'">Paid via {{order.payment_method
              == 'Stripe'?'Credit/Debit Card':order.payment_method == 'paypal' ?'Paypal' : 'Wallet'
              }}</span>
            <span class="col-9 text-start" *ngIf="order.payment_method == 'cod'">Payment Method</span>
            <span class="col-3 text-end">
              <img src="./assets/payment-logo/paypal-small-logo.png" alt="paypal tiffintom" *ngIf="order.payment_method ==
                'paypal'">
              <span *ngIf="order.payment_method == 'cod'">cash on delivery</span>
              <span *ngIf="order.payment_method == 'Stripe'"> XXXX-{{order.card_view?.card_number}}</span>
            </span>
          </div>

          <div class="col-12 p-2 d-flex justify-content-between border-bottom fw-bold border-top bg-light"
            *ngIf="order.payment_method == 'cod'">
            <span class="col-9 text-start">Payment Status</span>
            <span class="col-3 text-end">
              <span>Unpaid</span>
            </span>
          </div>

          <div class="col-sm-12 empty-cart-cls text-center" *ngIf="order?.cart_view?.length == 0">
            <img src="assets/cartitem.png" class="img-fluid mb-4 mr-3">
            <p><strong>No Item(s) Added</strong></p>
          </div>

        </div>
      </div>
    </div>

    <div class="col-xs-12 col-sm-12 mt-2 text-center align-items-center">
      <a class="mt-3 btn m-t-10 bg-primary text-white fw-bold" routerLink="/menu">
        Back To Homepage
      </a>
    </div>
  </div>