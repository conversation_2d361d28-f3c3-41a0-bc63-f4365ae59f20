import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { ErrorHandler } from 'src/app/shared/error-handler';
import { catchError } from 'rxjs/operators';
import { Restaurant } from '../models/restaurant';
import { UserService } from './user.service';

@Injectable({
  providedIn: 'root'
})
export class RestaurantService {
  public zipcode: string;

  private url = `${environment.apiBaseUrl}restaurants/`

  constructor(private http: HttpClient, private userService: UserService) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.query) params = params.set('query', options.query);
    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.from_date) params = params.set('from_date', options.from_date);
    if (options.to_date) params = params.set('to_date', options.to_date);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<Restaurant> {
    return this.http.get<Restaurant>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  timeslot(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.restaurant_id) params = params.set('restaurant_id', options.restaurant_id);
    if (options.date) params = params.set('date', options.date);

    return this.http.get<any>(`${this.url}time-slot`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  bookingtimeslot(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.restaurant_id) params = params.set('restaurant_id', options.restaurant_id);
    if (options.date) params = params.set('date', options.date);

    return this.http.get<any>(`${this.url}booking-time-slot`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  // New Functions
  show_name(name: string): Observable<Restaurant> {
    var user = JSON.parse(this.userService.getUser());
    let params = new HttpParams();
    if (user?.id) params = params.set('customer_id', user?.id);
    if (this.zipcode) params = params.set('postcode', this.zipcode);

    return this.http.get<Restaurant>(`${this.url + name + '/fetch'}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  favourite(restaurant: Restaurant): Observable<any> {
    return this.http.post<Restaurant>(environment.apiBaseUrl + 'favourite-restaurants', Restaurant.toFavouriteFormData(restaurant))
      .pipe(catchError(ErrorHandler.handleError));
  }
}
