import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OrderDetailComponent } from './order-detail.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgxPayPalModule } from 'ngx-paypal';
import { LottieModule } from 'ngx-lottie';
import { OrderDetail1Component } from './order-detail1/order-detail1.component';
import { OrderDetail2Component } from './order-detail2/order-detail2.component';

const routes: Routes = [{ path: '', component: OrderDetailComponent }];

export function playerFactory() {
  return import('lottie-web');
}

@NgModule({
  declarations: [OrderDetailComponent, OrderDetail1Component, OrderDetail2Component],
  imports: [RouterModule.forChild(routes), SharedModule, NgbModule, NgxPayPalModule, LottieModule.forRoot({ player: playerFactory })],
})
export class OrderDetailModule { }
