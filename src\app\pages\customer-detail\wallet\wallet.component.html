<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-12 d-flex pb-3">
      <h4 class="col-6">Wallet</h4>
      <div class="col-6 text-end">
        <div class="btn bg-primary text-white w-auto cursor" (click)=" walletView(walletHistoryModal)">
          Wallet History
        </div>
      </div>
    </div>

    <div class="col-12 border p-3">
      <span class="fw-bold"> Balance : {{convertNumber(user.wallet_amount)}}</span>
    </div>

    <div class="btn bg-primary text-white w-auto mt-3 cursor" (click)="loadWalllet(addMoneyModal)">
      Load Money
    </div>
  </div>
</div>


<ng-template #walletHistoryModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Wallet Transaction History
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 mt-2" style="overflow-x: auto;" *ngIf="WalletHistories.length > 0">
      <table class="fees-table w-100">
        <tr>
          <th>id</th>
          <th>Purpose</th>
          <th>Date</th>
          <th>Amount</th>
          <th>Transaction Detail</th>
        </tr>
        <tr *ngFor="let item of WalletHistories">
          <td>{{item.id}}</td>
          <td>{{item.purpose}}</td>
          <td>{{convertToDate(item.created)}}</td>
          <td>{{convertNumber(item.amount)}}</td>
          <td>{{item.transaction_details}}</td>
        </tr>
      </table>
    </div>
    <app-pagination *ngIf="WalletHistories.length > 0" [total_items]="totalWalletHistories" class="pt1 mt-1"
      [per_page]="options.per_page" [(current_page)]="options.page" (onChange)="loadPage($event)">
    </app-pagination>
    <div class="col-md-12 empty-cart-cls text-center py-5" *ngIf="WalletHistories.length <= 0">
      <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
      <p><strong>No wallet history(s) Found</strong></p>
    </div>
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
</ng-template>

<ng-template #addMoneyModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Load Money
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-12 mt-2">
      <div class="d-flex pb-3">
        <div class="col-md-2 px-1">
          <input type="number" [(ngModel)]="addAmount" name="addAmount" id="addAmount" class="form-control"
            placeholder="0.00">
        </div>
        <div class="col-md-1 px-1">
          <button class="btn btn-primary btn-circle btn-md cursor" name="money" (click)="addMoney(100)">
            100
          </button>
        </div>
        <div class="col-md-1 px-1">
          <div class="btn btn-primary btn-circle btn-md cursor" name="money" (click)="addMoney(200)">
            200</div>
        </div>
        <div class="col-md-1 px-1">
          <div class="btn btn-primary btn-circle btn-md cursor" name="money" (click)="addMoney(500)">
            500</div>
        </div>
      </div>

      <div class="col-md-2" *ngIf="stripeCustomers.length <= 0">
        <a routerLink="../payments" (click)="modal.dismiss('Cross click')"
          class="btn btn-primary cursor text-white mt-2">Add
          Card</a>
      </div>

      <div class="col-12 border border rounded p-2 mb-2" *ngFor=" let stripeCustomer of stripeCustomers;let i = index;">
        <label class="form-check-label fw-bold col-12 p-1 pb-0 text-truncate text-dark"
          for="ccard_select_{{stripeCustomer.id}}">
          <input type="radio" class="form-check-input" name="credit_card_choose" [(ngModel)]="cardId"
            [id]="'ccard_select_'+stripeCustomer.id" [value]="stripeCustomer.id" [checked]="i==0">
          <img src="./assets/payment-logo/visa-logo.png" class="px-1" *ngIf="stripeCustomer.card_brand == 'visa'">
          <img src="./assets/payment-logo/master-card-logo.png" class="px-1"
            *ngIf="stripeCustomer.card_brand == 'MasterCard'">
          <img src="./assets/payment-logo/american-express-logo.png" class="px-1"
            *ngIf="stripeCustomer.card_brand == 'American Express'">
          <img src="./assets/payment-logo/master.png" class="px-1"
            *ngIf="stripeCustomer.card_brand != 'visa' && stripeCustomer.card_brand != 'MasterCard' && stripeCustomer.card_brand != 'American Express'">
          {{ stripeCustomer.payment_method_name}}
          XXXX-XXXXXXXX-{{stripeCustomer.card_number}} Valid till
          {{stripeCustomer.exp_month}}/{{stripeCustomer.exp_year}}
        </label>
      </div>
    </div>

    <div *ngIf="ModelPaymenterror">
      <span class="text-danger">{{ ModelPaymenterror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isModelPaymentLoading" class="btn orng_btn m-t-10 bg-primary cursor text-white"
      (click)="loadMoney()">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelPaymentLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Add Payment
    </button>
  </div>
</ng-template>