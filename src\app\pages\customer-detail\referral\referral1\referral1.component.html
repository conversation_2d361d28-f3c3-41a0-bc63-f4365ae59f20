<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-12 pb-3">
      <h4 class="text-center pt-5">Invite your friend to TiffinTom, you get {{convertNumber(referral.invite_amount)}}
        after your friend
        registered with us. Your friend will also get {{convertNumber(referral.receive_amount)}}</h4>

      <div class="col-12 text-muted text-center pb-4">Your referral code is</div>

      <div class="col-12 text-center text-muted pb-4">
        <span class="fs-3 p-2" style="border: 3px dashed;">
          {{user.referral_code}}
        </span>
      </div>

      <div class="col-md-12 col-xs-12 d-md-flex pt-3 pb-4">
        <div class="col-md-4 col-xs-12 px-2 py-2">
          <a [href]="'http://www.facebook.com/sharer.php?u='+this.refferUrl" target="_blank">
            <span class="btn w-100 rounded text-white text-center fw-bold text-white cursor"
              style="background: #345C97;">
              Share on Facebook</span>
          </a>
        </div>
        <div class="col-md-4 col-xs-12 px-2 py-2">
          <a [href]="'https://twitter.com/share?url='+this.refferUrl" target="_blank">
            <span class="btn w-100 rounded text-white text-center fw-bold text-white cursor"
              style="background: #16CCFD;">
              Share on Twitter</span>
          </a>
        </div>
        <div class="col-md-4 col-xs-12 px-2 py-2">
          <span class="btn w-100 rounded text-white text-center fw-bold text-white cursor"
            (click)="smsReferral(phoneModal)" style="background: #f39c12;">
            Share on SMS</span>
        </div>
      </div>

      <div class="col-md-12 col-xs-12 d-md-flex pb-4">
        <div class="col-md-4 col-xs-12 px-2 py-2">
          <a [href]="'https://api.whatsapp.com/send?text='+this.refferUrl" target="_blank">
            <span class="btn w-100 rounded text-white text-center fw-bold text-white cursor"
              style="background: #4AC959;">
              Share on What's App</span>
          </a>
        </div>
        <div class="col-md-4 col-xs-12 px-2 py-2">
          <span class="btn w-100 rounded text-white text-center fw-bold text-white cursor"
            (click)="emailReferral(emailModal)" style="background: #e34133;">
            Share on Email</span>
        </div>
      </div>

      <div class="col-12 input-group pb-4">
        <input type=" search" class="form-control border border-default body-box-shadow rounded"
          [value]="this.refferUrl" aria-label="Search" readonly />
        <button type="button" class="btn bg-primary text-white body-box-shadow cursor" style="width:120px;"
          (click)="copyMessage()">{{copyTextChange?'Copied':'Copy'}}</button>
      </div>

      <div class="col-12 text-center pb-3 fs-5 fw-bold">How does referral friend work</div>

      <div class="col-xs-12 d-md-flex">
        <div class="col-sm-4 col-xs-12">
          <div class="w-100 text-left p-2 d-flex">
            <span class="bg-primary text-center p-2" style="width:45px;height:45px;border-radius: 25px;">
              <img src="./assets/invite-frd.png" alt="invite your friend join in tiffintom">
            </span>
            <span class="px-3 text-center p-2 fw-bold">Invite your Friends</span>
          </div>
        </div>
        <div class="col-sm-4 col-xs-12">
          <div class="w-100 text-left p-2 d-flex">
            <span class="bg-primary text-center p-2" style="width:45px;height:45px;border-radius: 25px;">
              <img src="./assets/sucessful.png" alt="referral your friend join tiffintom">
            </span>
            <span class="px-3 text-center p-2 fw-bold">Successful Registration</span>
          </div>
        </div>
        <div class="col-sm-4 col-xs-12">
          <div class="w-100 text-left p-2 d-flex">
            <span class="bg-primary text-center p-2" style="width:45px;height:45px;border-radius: 25px;">
              <img src="./assets/gift.png" alt="get reward for referral your friend with tiffintom">
            </span>
            <span class="px-3 text-center p-2 fw-bold">You will get {{convertNumber(referral.invite_amount)}}</span>
          </div>
        </div>
      </div>

      <div class="col-md-12 col-xs-12 mt-3">
        <div class="py-2 body-box-shadow rounded">
          <div class="px-2 col-md-12 col-xs-12 d-flex justify-content-between">
            <div class="fs-5 fw-bold">View Referral List</div>
            <span class="px-2 text-end">
              <img src="./assets/icons/plus.png" class="cursor" *ngIf="!tableExpand" (click)="tableExpand=true">
              <img src="./assets/icons/minus.png" class="cursor" *ngIf="tableExpand" (click)="tableExpand=false">
            </span>
          </div>
          <div class="w-100" style="overflow-x: auto;" [class.d-none]="!tableExpand" [class.d-block]="tableExpand">
            <table class="w-100 fees-table" *ngIf="referralPendings.length > 0">
              <tr>
                <th>S.No</th>
                <th>Referral Name</th>
                <th>Email ID</th>
                <th>Amount</th>
                <th>Date</th>
                <th>Status</th>
              </tr>
              <tr *ngFor="let item of referralPendings;let i = index">
                <td>{{i+1}}</td>
                <td>{{item.first_name}} {{item.last_name}}</td>
                <td>{{item.username}}</td>
                <td>{{item.invite_amount}}</td>
                <td>{{convertToDate(item.created)}}</td>
                <td>{{(item.status == 'Eligible')?'Pending':item.status}}</td>
              </tr>
            </table>
            <div class="col-md-12 empty-cart-cls text-center" *ngIf="referralPendings.length <= 0">
              <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
              <p><strong>No reffrral(s) Found</strong></p>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<ng-template #phoneModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Share On SMS
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Phone Number</label>
      <input type="text" class="form-control col-md-8" (keypress)="keyPress($event)" minlength="10" maxlength="11"
        id="phoneNumber" name="phoneNumber" [(ngModel)]="phoneNumber" />
    </div>
    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isModelLoading" class="btn btn-primary text-white cursor"
      (click)="validatePhone(phoneModal)">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Send
    </button>
  </div>
</ng-template>

<ng-template #emailModal let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Share On Email
    </h4>
    <button type="button" class="close bg-primary text-white cursor" aria-label="Close"
      (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-sm-12 col-xs-12 form-group">
      <label class="col-md-4">Email</label>
      <input type="email" class="form-control col-md-8" id="emailAddress" name="emailAddress" placeholder="Enter email"
        [(ngModel)]="emailAddress" />
    </div>
    <div *ngIf="ModelEmailerror">
      <span class="text-danger">{{ ModelEmailerror }}</span>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isEmailModelLoading" class="btn btn-primary text-white cursor"
      (click)="validateEmail(emailModal)">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isEmailModelLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Send
    </button>
  </div>
</ng-template>