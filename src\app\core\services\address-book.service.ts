import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorHandler } from 'src/app/shared/error-handler';
import { environment } from 'src/environments/environment';
import { AddressBook } from '../models/address-book';
import * as CryptoJS from 'crypto-js';

@Injectable({
  providedIn: 'root',
})
export class AddressBookService {
  private url = environment.apiBaseUrl + 'address-book/';
  public carts: AddressBook[] = []
  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.customer_id) params = params.set('customer_id', options.customer_id);
    if (options.restaurant_id) params = params.set('restaurant_id', options.restaurant_id);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<AddressBook> {
    return this.http.get<AddressBook>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(address_book: AddressBook): Observable<any> {
    return this.http.post<AddressBook>(this.url, AddressBook.toFormData(address_book))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(address_book: AddressBook): Observable<any> {
    return this.http.post<AddressBook>(this.url + address_book.id, AddressBook.toFormData(address_book))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<any> {
    return this.http.delete<AddressBook>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }
}
