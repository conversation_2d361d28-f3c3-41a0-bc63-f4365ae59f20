<ng-container *ngIf="!userService.userThemeLoading; else themeLoading">
  <ng-container [ngSwitch]="menuTheme">
    <ng-container *ngSwitchCase="'theme1'">
      <div class="container">
        <div class="row">
          <div class="col-md-6 mt-3">
            <div class="bg-white body-box-shadow rounded p-3">
              <app-login></app-login>
            </div>
          </div>
          <div class="col-md-6 mt-3 mb-3">
            <div class="bg-white body-box-shadow rounded p-3">
              <app-signup></app-signup>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
    <app-auth2 *ngSwitchCase="'theme2'"></app-auth2>
    <app-auth2 *ngSwitchCase="'theme3'"></app-auth2>

    <!-- Optional: fallback -->
    <div *ngSwitchDefault>
      <div class="container">
        <div class="row">
          <div class="col-md-6 mt-3">
            <div class="bg-white body-box-shadow rounded p-3">
              <app-login></app-login>
            </div>
          </div>
          <div class="col-md-6 mt-3 mb-3">
            <div class="bg-white body-box-shadow rounded p-3">
              <app-signup></app-signup>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</ng-container>

<ng-template #themeLoading>
  <div class="d-flex justify-content-center align-items-center w-100 h-100" style="min-height: 80vh;">
    <div class="cart-loader">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  </div>
</ng-template>
