<ng-container *ngIf="!userService.userThemeLoading; else themeLoading">
  <ng-container [ngSwitch]="menuTheme">
    <app-booking-add *ngSwitchCase="'theme1'"></app-booking-add>
    <app-booking-add2 *ngSwitchCase="'theme2'"></app-booking-add2>
    <app-booking-add3 *ngSwitchCase="'theme3'"></app-booking-add3>

    <!-- Optional: fallback -->
    <div *ngSwitchDefault>
      <app-booking-add></app-booking-add>
    </div>
  </ng-container>
</ng-container>

<ng-template #themeLoading>
  <div class="d-flex justify-content-center align-items-center w-100 h-100" style="min-height: 80vh;">
    <div class="cart-loader">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  </div>
</ng-template>