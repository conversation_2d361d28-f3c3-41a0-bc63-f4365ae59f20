import { Cart } from "./cart";
import { Review } from "./review";
import { Surcharge } from "./surcharge";

export class Order {
  id: string;
  order_number: string;
  restaurant_id: string;
  customer_id: string;
  driver_id: string;
  ref_number: string;
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  source_latitude: string;
  source_longitude: string;
  destination_latitude: string;
  destination_longitude: string;
  flat_no: string;
  google_address: string;
  address: string;
  address_id: string;
  landmark: string;
  state_name: string;
  city_name: string;
  location_name: string;
  user_type: string = 'Guest';
  order_type: string;
  assoonas: string;
  order_description: string;
  delivery_date: string;
  delivery_time: string;
  delivery_time_slot: string;
  delivered_time: string;

  offer_percentage: number = 0;
  offer_amount: number = 0;
  day_offer: number = 0;
  tax_percentage: number = 0;
  tax_amount: number = 0;
  delivery_charge: number = 0;
  service_charge: number = 0;
  voucher_code: string;
  voucher_percentage: number = 0;
  voucher_amount: number = 0;
  tip_percentage: number = 0;
  tip_amount: number = 0;
  order_sub_total: number = 0;
  order_grand_total: number = 0;
  cardfee_percentage: number = 0;
  cardfee_price: number = 0;

  payment_status: string = 'NP';
  payment_method: string;
  card_id: string;
  payment_wallet: string;
  paid_full: string = 'No';
  split_payment: string = 'No';
  wallet_amount: number = 0;
  transaction_id: string;
  reward_used: string = 'N';
  distance: string;
  driver_invoice: string;
  driver_invoice_number: string;
  driver_deliver_date: string;
  driver_charge: string;
  failed_reason: string;
  order_point: string;
  spent_point: string;
  reward_offer: number = 0;
  reward_offer_percentage: number = 0;
  rewardPoint: number = 0;
  rewardPercentage: number = 0;
  stripe_customerid: string;
  payerID: string;
  paymentToken: string;
  paymentID: string;
  order_proof: string;
  payout_type: string;
  payout_amount: string;
  status: string = 'Pending';
  type: string = 'Web';
  completed_time: string;
  preparation: string;
  dont_bell: string;
  dont_cutlery: string;
  is_favourite: string;
  charity_amount: number = 0;
  driver_tip: number = 0;
  charity_message: string;
  delivery_instruction: string;

  created: string;
  modified: string;

  carts: Cart[];
  cart_view: Cart[];
  card: any;
  applied_offers: Cart[];
  eligible_offers: any;
  surcharges: Surcharge[];
  surcharge_amount: number;

  reviews: Review = new Review()

  static toFormData(order: Order) {
    const formData = new FormData();

    if (order.id) formData.append('id', order.id);
    if (order.restaurant_id) formData.append('restaurant_id', order.restaurant_id);
    if (order.customer_id) formData.append('customer_id', order.customer_id);
    if (order.customer_name) formData.append('customer_name', order.customer_name);
    if (order.customer_email) formData.append('customer_email', order.customer_email);
    if (order.customer_phone) formData.append('customer_phone', order.customer_phone);
    if (order.source_latitude) formData.append('source_latitude', order.source_latitude);
    if (order.source_longitude) formData.append('source_longitude', order.source_longitude);
    if (order.destination_latitude) formData.append('destination_latitude', order.destination_latitude);
    if (order.destination_longitude) formData.append('destination_longitude', order.destination_longitude);
    if (order.flat_no) formData.append('flat_no', order.flat_no);
    if (order.address) formData.append('address', order.address);
    if (order.address_id) formData.append('address_id', order.address_id);
    if (order.order_type) formData.append('order_type', order.order_type);
    if (order.assoonas) formData.append('assoonas', order.assoonas);
    if (order.order_description) formData.append('order_description', order.order_description);
    if (order.delivery_date) formData.append('delivery_date', order.delivery_date);
    if (order.delivery_time) formData.append('delivery_time', order.delivery_time);
    if (order.delivery_charge) formData.append('delivery_charge', order.delivery_charge.toString());
    if (order.service_charge) formData.append('service_charge', order.service_charge.toString());
    if (order.voucher_code) formData.append('voucher_code', order.voucher_code);
    if (order.voucher_percentage) formData.append('voucher_percentage', order.voucher_percentage.toString());
    if (order.voucher_amount) formData.append('voucher_amount', order.voucher_amount.toString());
    if (order.order_sub_total) formData.append('order_sub_total', order.order_sub_total.toString());
    if (order.order_grand_total) formData.append('order_grand_total', order.order_grand_total.toString());
    if (order.payment_status) formData.append('payment_status', order.payment_status);
    if (order.payment_method) formData.append('payment_method', order.payment_method);
    if (order.card_id) formData.append('card_id', order.card_id);
    if (order.payment_wallet) formData.append('payment_wallet', order.payment_wallet);
    if (order.paid_full) formData.append('paid_full', order.paid_full);
    if (order.split_payment) formData.append('split_payment', order.split_payment);
    if (order.wallet_amount) formData.append('wallet_amount', order.wallet_amount.toString());
    if (order.transaction_id) formData.append('transaction_id', order.transaction_id);
    if (order.reward_used) formData.append('reward_used', order.reward_used);
    if (order.failed_reason) formData.append('failed_reason', order.failed_reason);
    if (order.order_point) formData.append('order_point', order.order_point);
    if (order.spent_point) formData.append('spent_point', order.spent_point);
    if (order.reward_offer) formData.append('reward_offer', order.reward_offer.toString());
    if (order.reward_offer_percentage) formData.append('reward_offer_percentage', order.reward_offer_percentage.toString());
    if (order.stripe_customerid) formData.append('stripe_customerid', order.stripe_customerid);
    if (order.payerID) formData.append('payerID', order.payerID);
    if (order.paymentToken) formData.append('paymentToken', order.paymentToken);
    if (order.paymentID) formData.append('paymentID', order.paymentID);
    if (order.status) formData.append('status', order.status);
    if (order.type) formData.append('type', order.type);
    if (order.offer_amount) formData.append('offer_amount', order.offer_amount.toString());
    if (order.offer_percentage) formData.append('offer_percentage', order.offer_percentage.toString());
    if (order.charity_amount) formData.append('charity_amount', order.charity_amount.toString());
    if (order.driver_tip) formData.append('driver_tip', order.driver_tip.toString());
    if (order.charity_message) formData.append('charity_message', order.charity_message);
    if (order.delivery_instruction) formData.append('delivery_instruction', order.delivery_instruction);
    if (order.surcharge_amount) formData.append('surcharge_amount', order.surcharge_amount.toString());

    if (order.carts?.length > 0) {
      let finalCartItems: Cart[] = []
      order.carts.forEach(cart => {
        var cartObject: Cart = new Cart();
        Cart.toFormData(cart).forEach(function (value, key) {
          cartObject[key] = value;
        });
        finalCartItems.push(cartObject);
      });
      formData.append('carts', JSON.stringify(finalCartItems));
    }

    if (order.surcharges?.length > 0) {
      let finalSurchargeItems: Surcharge[] = []
      order.surcharges.forEach(surcharge => {
        var surchargeObject: Surcharge = new Surcharge();
        Surcharge.toFormData(surcharge).forEach(function (value, key) {
          surchargeObject[key] = value;
        });
        finalSurchargeItems.push(surchargeObject);
      });
      formData.append('surcharges', JSON.stringify(finalSurchargeItems));
    }
    return formData;
  }
}
