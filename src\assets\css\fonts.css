/* ----------------------------
          <PERSON><PERSON>-Fonts
---------------------------- */
@font-face {
  font-family: "Fredoka One";
  src: url("../fonts/FredokaOne-Regular.woff2") format("woff2"),
    url("../fonts/FredokaOne-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Fredoka";
  src: url("../fonts/Fredoka-Light.woff2") format("woff2"),
    url("../fonts/Fredoka-Light.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Fredoka";
  src: url("../fonts/Fredoka-Bold.woff2") format("woff2"),
    url("../fonts/Fredoka-Bold.woff") format("woff");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Fredoka";
  src: url("../fonts/Fredoka-Regular.woff2") format("woff2"),
    url("../fonts/Fredoka-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Fredoka";
  src: url("../fonts/Fredoka-Medium.woff2") format("woff2"),
    url("../fonts/Fredoka-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Fredoka";
  src: url("../fonts/Fredoka-SemiBold.woff2") format("woff2"),
    url("../fonts/Fredoka-SemiBold.woff") format("woff");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* ----------------------------
        Visby-CF-Fonts
---------------------------- */
@font-face {
  font-family: "Visby CF";
  src: url("../fonts/VisbyCF-BoldOblique.woff2") format("woff2"),
    url("../fonts/VisbyCF-BoldOblique.woff") format("woff");
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Visby CF";
  src: url("../fonts/VisbyCF-Bold.woff2") format("woff2"),
    url("../fonts/VisbyCF-Bold.woff") format("woff");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Visby CF Demi Bold Oblique";
  src: url("../fonts/VisbyCF-DemiBoldOblique.woff2") format("woff2"),
    url("../fonts/VisbyCF-DemiBoldOblique.woff") format("woff");
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Visby CF Demi";
  src: url("../fonts/VisbyCF-DemiBold.woff2") format("woff2"),
    url("../fonts/VisbyCF-DemiBold.woff") format("woff");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Visby CF";
  src: url("../fonts/VisbyCF-ThinOblique.woff2") format("woff2"),
    url("../fonts/VisbyCF-ThinOblique.woff") format("woff");
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Visby CF Extra";
  src: url("../fonts/VisbyCF-ExtraBold.woff2") format("woff2"),
    url("../fonts/VisbyCF-ExtraBold.woff") format("woff");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Visby CF";
  src: url("../fonts/VisbyCF-MediumOblique.woff2") format("woff2"),
    url("../fonts/VisbyCF-MediumOblique.woff") format("woff");
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Visby CF";
  src: url("../fonts/VisbyCF-HeavyOblique.woff2") format("woff2"),
    url("../fonts/VisbyCF-HeavyOblique.woff") format("woff");
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Visby CF";
  src: url("../fonts/VisbyCF-Medium.woff2") format("woff2"),
    url("../fonts/VisbyCF-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Visby CF";
  src: url("../fonts/VisbyCF-LightOblique.woff2") format("woff2"),
    url("../fonts/VisbyCF-LightOblique.woff") format("woff");
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Visby CF";
  src: url("../fonts/VisbyCF-Thin.woff2") format("woff2"),
    url("../fonts/VisbyCF-Thin.woff") format("woff");
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Visby CF";
  src: url("../fonts/VisbyCF-ExtraBoldOblique.woff2") format("woff2"),
    url("../fonts/VisbyCF-ExtraBoldOblique.woff") format("woff");
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Visby CF";
  src: url("../fonts/VisbyCF-Light.woff2") format("woff2"),
    url("../fonts/VisbyCF-Light.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Visby CF";
  src: url("../fonts/VisbyCF-Heavy.woff2") format("woff2"),
    url("../fonts/VisbyCF-Heavy.woff") format("woff");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* ----------------------------
          Inter-Fonts
---------------------------- */
@font-face {
  font-family: "Inter";
  src: url("../fonts/Inter-Regular.woff2") format("woff2"),
    url("../fonts/Inter-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("../fonts/Inter-Medium.woff2") format("woff2"),
    url("../fonts/Inter-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("../fonts/Inter-SemiBold.woff2") format("woff2"),
    url("../fonts/Inter-SemiBold.woff") format("woff");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("../fonts/Inter-Bold.woff2") format("woff2"),
    url("../fonts/Inter-Bold.woff") format("woff");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("../fonts/Inter-ExtraBold.woff2") format("woff2"),
    url("../fonts/Inter-ExtraBold.woff") format("woff");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}
