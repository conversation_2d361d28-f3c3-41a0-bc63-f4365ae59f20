<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">

    <div class="col-sm-12 col-md-8" *ngIf="reviews.length > 0">
      <div class="p-2 border-bottom bg-white rounded body-box-shadow" *ngFor="let review of reviews">
        <div class="col-md-12 col-sm-12 text-end">
          <app-rating [stars]="'5'" [rating]="review.rating" [viewOnly]="true"></app-rating>
        </div>
        <div class="col-md-12 col-sm-12 d-flex justify-content-between">
          <span class="col-md-9 col-sm-7 fw-bold">{{review.first_name}}</span>
          <div class="col-md-3 col-sm-5 text-end fw-bold ">
            {{convertToDate(review.created)}}
          </div>
        </div>
        <div class="col-md-12 col-sm-12 text-break">
          {{review.message}}
        </div>
        <div class="p-2 border-primary" style="border-left: 5px solid" *ngIf="review.responce">
          <div class="col-sm-12 col-sm-12 fw-bold">Response from the Restaurant</div>
          <div class="col-sm-12 col-sm-12 text-break">
            {{review.responce}}
          </div>
        </div>
      </div>

    </div>

    <div class="col-md-12 bg-white rounded body-box-shadow empty-cart-cls text-center py-5" *ngIf="reviews.length <= 0">
      <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
      <p><strong>No review(s) Found</strong></p>
    </div>

    <div class="col-sm-12 col-md-4" *ngIf="reviews.length > 0">
      <div class="bg-white rounded body-box-shadow p-3">
        <h1 class="col-sm-12 col-md-12 text-center">{{finalReview}}/5</h1>
        <div class="col-sm-12 col-md-12 text-center">
          <app-rating [stars]="'5'" [rating]="finalReview" [viewOnly]="true" class="pt-0 mt-0"></app-rating>
        </div>
        <div class="col-md-12 col-sm-12 text-center" *ngIf="finalReview >= 4">Highly Recommended</div>
        <div class="col-md-12 col-sm-12 text-center border-bottom">({{reviews.length}} Reviews)</div>
        <div class="col-md-12 col-sm-12 fw-bold text-primary p-0">FILTER BY</div>

        <div class="col-md-12 col-sm-12 fw-bold cursor" (click)="reviewFilter('all')">
          All
        </div>
        <div class="col-md-12 col-sm-12 cursor" (click)="reviewFilter(5)">
          <app-rating [stars]="'5'" [rating]="'5'" [viewOnly]="true"></app-rating>
        </div>
        <div class="col-md-12 col-sm-12 cursor" (click)="reviewFilter(4)">
          <app-rating [stars]="'5'" [rating]="'4'" [viewOnly]="true"></app-rating>
        </div>
        <div class="col-md-12 col-sm-12 cursor" (click)="reviewFilter(3)">
          <app-rating [stars]="'5'" [rating]="'3'" [viewOnly]="true"></app-rating>
        </div>
        <div class="col-md-12 col-sm-12 cursor" (click)="reviewFilter(2)">
          <app-rating [stars]="'5'" [rating]="'2'" [viewOnly]="true"></app-rating>
        </div>
        <div class="col-md-12 col-sm-12 cursor" (click)="reviewFilter(1)">
          <app-rating [stars]="'5'" [rating]="'1'" [viewOnly]="true"></app-rating>
        </div>
      </div>
    </div>
  </div>
</div>