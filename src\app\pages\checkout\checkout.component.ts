import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import {
  NgbModal,
  ModalDismissReasons,
  NgbModalOptions,
  NgbActiveModal,
} from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Order } from 'src/app/core/models/order';
import { Cart } from 'src/app/core/models/cart';
import { environment } from 'src/environments/environment';
import { CartService } from 'src/app/core/services/cart.service';
import { User } from 'src/app/core/models/user';
import { OrderService } from 'src/app/core/services/order.service';
import { StripeCustomer } from 'src/app/core/models/stripe-customer';
import { StripeCustomerService } from 'src/app/core/services/stripe-customer.service';
import { VoucherService } from 'src/app/core/services/voucher.service';
import { Voucher } from 'src/app/core/models/voucher';
declare var Stripe;
import { IPayPalConfig, ICreateOrderRequest } from 'ngx-paypal';
import { Meta, Title } from '@angular/platform-browser';
import { NotificationService } from 'src/app/core/services/notification.service';
import { NgForm } from '@angular/forms';
import { AnimationOptions } from 'ngx-lottie';
import { Surcharge } from 'src/app/core/models/surcharge';
import { MessagingService } from 'src/app/core/services/messaging.service';
@Component({
  selector: 'app-checkout',
  templateUrl: './checkout.component.html',
  styleUrls: ['./checkout.component.scss'],
})
export class CheckoutComponent implements OnInit, OnDestroy {
  @ViewChild('multiplePriceOfferModal', { static: true }) multiplePriceOfferModal: ElementRef;
  @ViewChild('otpModal', { static: true }) otpModal: ElementRef;
  @ViewChild('profileModal', { static: true }) profileModal: ElementRef;
  @ViewChild('placeModal', { static: true }) placeModal: ElementRef;
  @ViewChild('itemNotAvailableModal', { static: true }) itemNotAvailableModal: ElementRef;

  subs = new Subscription();
  isLoading = false;
  isCheckoutLoading = false;
  error = null;
  errorPaymentMethod = null;
  errorPaypal = null;
  isModelLoading = false;
  Modelerror = null;
  isOfferModelLoading = false;
  isEligible = false;
  OfferModelerror = null;
  errorVoucher = null;
  isModelOtpLoading = false;
  Modelotperror = null;
  carts: Cart[] = [];
  surcharges: Surcharge[] = [];
  stripeCustomers: StripeCustomer[] = [];
  stripeCustomer: StripeCustomer = new StripeCustomer();
  user: User;
  restaurant: Restaurant = new Restaurant();
  order: Order = new Order();
  categoryOptions = { nopaginate: 1, prefilled: 1, isSuggested: 1 };
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;
  addStripeCustomer: StripeCustomer;
  voucher: Voucher = new Voucher();
  payPalConfig?: IPayPalConfig;
  paypalClientKey: string
  verifyOtp: string;
  phoneTab = false;
  isModelProfileLoading = true;
  ModelProfileerror = false;
  publishKey: string;
  isConnect: string;

  eligibleQty: number;
  surchargeAmount: number = 0;

  hasOrderTypeMismatch = false;
  hasDayMismatch = false;

  optionPlace: AnimationOptions = {
    path: './assets/Animations/OrderPlaced.json',
  };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  selected_checkbox_array = [];

  isRestaurantLoading = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private restaurantService: RestaurantService,
    private cartService: CartService,
    private currencyPipe: CurrencyPipe,
    private orderService: OrderService,
    private stripeCustomerService: StripeCustomerService,
    private voucherService: VoucherService,
    private metaTagService: Meta,
    private titleService: Title,
    private notificationService: NotificationService,
    private messagingService: MessagingService,
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    this.order.restaurant_id = this.restaurant_id;
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    if (JSON.parse(this.cartService.getOrder())) {
      this.order = JSON.parse(this.cartService.getOrder());
    } else {
      this.router.navigateByUrl('/menu');
    }

    // this.order.payment_method = 'Stripe';
    this.modalOptions = {
      backdrop: 'static',
      size: 'lg',
      backdropClass: 'customBackdrop',
    };

    //Restaurant Find 
    this.fetchRestaurant();
    //Cart Find 
    this.fetchCarts();
  }

  initPaypal() {
    this.payPalConfig = {
      currency: 'GBP',
      clientId: this.paypalClientKey,
      createOrderOnClient: (data) => <ICreateOrderRequest>{
        // intent: 'CAPTURE',
        purchase_units: [
          {
            amount: {
              currency_code: 'GBP',
              value: this.getGrandTotal().toString(),
            },
          }
        ]
      },
      advanced: {
        commit: 'true',
        extraQueryParams: [{ name: "disable-funding", value: "credit,card" }]
      },
      style: {
        size: 'small',
        label: 'paypal',
        layout: 'vertical',
        color: 'gold',
        shape: 'pill',
      },
      onApprove: (data, actions) => {
        console.log('onApprove - transaction was approved, but not authorized', data, actions);
        actions.order.get().then(details => {
          console.log('onApprove - you can get full order details inside onApprove: ', details);
        });
      },
      onClientAuthorization: (data) => {
        console.log('onClientAuthorization - you should probably inform your server about completed transaction at this point', data);
        // this.order.transaction_id = data.purchase_units[0]?.payments.captures[0].id;
        this.order.payerID = data.payer.payer_id;
        this.order.payment_status = 'P';
        this.order.paid_full = 'Yes';
        this.order.payment_method = 'paypal';
        this.saveOrder()
      },
      onCancel: (data, actions) => {
        console.log('OnCancel', data, actions);
        this.isCheckoutLoading = false;
      },
      onError: err => {
        console.log('OnError', err);
        this.isCheckoutLoading = false;
      },
      onClick: (data, actions) => {
        console.log('onClick', data, actions);
        this.isCheckoutLoading = false;
      },
    };
  }

  offerCheck() {
    this.isLoading = true
    this.subs.add(
      this.orderService.offercheck(this.order).
        pipe(finalize(() => this.isLoading = false))
        .subscribe(
          (res) => {
            this.order = res;
            this.getGrandTotal();
            this.initPaypal();
            if (this.order.eligible_offers.length > 0) {
              this.modalService.open(this.multiplePriceOfferModal, { backdrop: 'static', size: 'lg', backdropClass: 'customBackdrop', });
              this.eligibleQty = this.order.eligible_offers[0].quantity;
            }
          },
          (err) => { }
        )
    )
  }

  selectCheckBox(offer, event, index) {
    this.OfferModelerror = false;

    if (event) {
      let sum: number = offer.quantity;
      this.selected_checkbox_array.forEach(inc => sum += inc.quantity);
      this.selected_checkbox_array.push(offer);
      if (sum <= this.eligibleQty) {
        this.isEligible = true;
      } else {
        this.OfferModelerror = "please select only " + this.eligibleQty + " qty.";
        this.isEligible = false;
      }
    } else {
      let decsum: number = 0;
      this.selected_checkbox_array.forEach(dec => decsum += dec.quantity);
      const index = this.selected_checkbox_array.indexOf(offer);
      this.selected_checkbox_array.splice(index, 1);
      decsum = decsum - offer.quantity;
      if (decsum <= this.eligibleQty) {
        this.isEligible = true;
      } else {
        this.OfferModelerror = "please select only " + this.eligibleQty + " qty.";
        this.isEligible = false;
      }
    }
  }

  updateToOffer(offer, type, index) {
    this.OfferModelerror = false;

    if (type == 'add') {
      this.order.eligible_offers[index].quantity = offer.quantity + 1;
      let sum: number = 0;
      this.selected_checkbox_array.forEach(dec => sum += dec.quantity);
      if (sum > this.eligibleQty) {
        this.OfferModelerror = "please select only " + this.eligibleQty + " qty.";
        this.isEligible = false;
      } else {
        this.isEligible = true;
      }
    }
    if (type == 'update') {
      var quantity = offer.quantity - 1;
      if (quantity > 0) {
        this.order.eligible_offers[index].quantity = quantity;
        let sum: number = 0;
        this.selected_checkbox_array.forEach(dec => sum += dec.quantity);
        if (sum > this.eligibleQty) {
          this.OfferModelerror = "please select only " + this.eligibleQty + " qty.";
          this.isEligible = false;
        } else {
          this.isEligible = true;
        }
      } else {
        this.order.eligible_offers[index].quantity = 1;
        let sum: number = 0;
        this.selected_checkbox_array.forEach(dec => sum += dec.quantity);
        if (sum > this.eligibleQty) {
          this.OfferModelerror = "please select only " + this.eligibleQty + " qty.";
          this.isEligible = false;
        } else {
          this.isEligible = true;
        }
      }
    }
  }

  validateOffer() {
    let validatesum: number = 0;
    this.selected_checkbox_array.forEach(validate => validatesum += validate.quantity);
    if (validatesum == this.eligibleQty) {
      this.selected_checkbox_array.map((obj) => {
        obj.total_price = 0;
        obj.menu_price = 0;
        obj.menu_id = 0;
        return obj;
      })
      this.order.applied_offers = this.order.applied_offers.concat(this.selected_checkbox_array);
      this.modalService.dismissAll();
      this.isEligible = true;
    } else if (validatesum != 0 && validatesum > this.eligibleQty) {
      this.OfferModelerror = "please select only " + this.eligibleQty + " qty.";
      this.isEligible = false;
    } else if (validatesum != 0 && validatesum < this.eligibleQty) {
      this.OfferModelerror = "you have must select at least" + this.eligibleQty + " qty.";
      this.isEligible = false;
    } else {
      this.OfferModelerror = "please select product first.";
      this.isEligible = false;
    }
  }

  initCard() {
    this.cardElement = <HTMLInputElement>(
      document.getElementById('customCardElement')
    );
    var elements = this.stripe.elements();
    this.card = elements.create('card', { hidePostalCode: true });
    this.card.mount(this.cardElement);
    this.isModelLoading = false;
  }

  async handleForm(e) {
    e.preventDefault();
    this.isModelLoading = true;
    this.Modelerror = false;
    let createPaymentMethodPromise = this.stripe
      .createPaymentMethod({
        type: 'card',
        card: this.card,
      })
      .then((result) => {
        // this.createPaymentIntent(result.paymentMethod.id);
        if (!result.error) {
          this.stripeCustomer.customer_id = this.order.customer_id;
          this.stripeCustomer.customer_name = this.order.customer_name;
          this.stripeCustomer.stripe_token_id = result.paymentMethod.id;
          this.stripeCustomer.exp_month = result.paymentMethod.card.exp_month;
          this.stripeCustomer.exp_year = result.paymentMethod.card.exp_year;
          this.stripeCustomer.country = result.paymentMethod.card.country;
          this.stripeCustomer.card_brand = result.paymentMethod.card.brand;
          this.stripeCustomer.card_number = result.paymentMethod.card.last4;
          this.stripeCustomer.card_type = result.paymentMethod.card.funding;
          this.stripeCustomer.service_type = this.isConnect;
          this.subs.add(
            this.stripeCustomerService.create(this.stripeCustomer).
              pipe(finalize(() => this.isModelLoading = false))
              .subscribe(
                (res) => {
                  this.fetchCards();
                  this.messagingService.success("Card added successfully !!");
                  this.modalService.dismissAll();
                },
                (err) => {
                  this.Modelerror = err;
                }
              )
          )
        } else {
          this.isModelLoading = false
          this.Modelerror = result.error.message;
        }
      });
  }

  fetchCarts() {
    this.isLoading = true;

    this.subs.add(
      this.cartService
        .get({ nopaginate: "1" })
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.carts = res;
            this.cartService.carts = this.carts;
            this.order.carts = this.carts;
            this.offerCheck();
          },
          (err) => {
            this.carts = [];
            this.router.navigateByUrl('/menu');
          }
        )
    );
  }

  fetchCards() {
    if (!this.isConnect) {
      if (this.restaurant?.business?.connect_service) {
        this.isConnect = 'connect';
      } else {
        this.isConnect = 'normal';
      }
    }
    this.subs.add(
      this.stripeCustomerService
        .get({ customer_id: this.userId, service_type: this.isConnect, nopaginate: "1" })
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.stripeCustomers = res;
            this.order.card_id = this.stripeCustomers[0].id;
          },
          (err) => {
            this.stripeCustomers = [];
          }
        )
    );
  }

  fetchRestaurant() {
    this.isLoading = true;
    this.isRestaurantLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => { this.isLoading = false; this.isRestaurantLoading = false }))
      .subscribe(res => {
        this.restaurant = res;
        this.surcharges = res.surcharges;
        if (this.restaurant.meta_title) {
          this.titleService.setTitle(this.restaurant.meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.meta_description });
        } else {
          this.titleService.setTitle(this.restaurant.site_setting.meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.site_setting.meta_keywords });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.site_setting.meta_description });
        }
        if (this.restaurant?.business?.connect_service) {
          this.publishKey = this.restaurant?.business?.connect_stripe_public_key
          this.isConnect = 'connect';
        } else {
          if (this.restaurant?.site_setting?.finance_stripe_mode == 'Test') {
            this.publishKey = this.restaurant?.site_setting?.finance_stripe_publishkeyTest
          } else {
            this.publishKey = this.restaurant?.site_setting?.finance_stripe_publishkey
          }
          this.isConnect = 'normal';
        }
        this.stripe = Stripe(this.publishKey);
        if (this.restaurant?.site_setting?.paypal_mode == 'Test') {
          this.paypalClientKey = this.restaurant?.site_setting?.test_clientid
        } else {
          this.paypalClientKey = this.restaurant?.site_setting?.live_clientid
        }
        this.fetchCards();
        if (this.order.order_sub_total <= this.restaurant.card_minimum_order) {
          this.order.payment_method = 'COD';
        } else {
          this.order.payment_method = 'Stripe';
        }
        this.surcharges.forEach(sur => this.surchargeAmount += sur.surcharge_amount);

      }, err => this.error = err)
    );
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
    this.initCard()
  }

  getGrandTotal() {
    let grandTotal = 0;

    this.order.delivery_charge = this.order.delivery_charge > 0 ? this.order.delivery_charge : 0;
    this.order.service_charge = this.order.service_charge > 0 ? this.order.service_charge : 0;
    this.order.charity_amount = this.order.charity_amount > 0 ? this.order.charity_amount : 0;
    this.order.driver_tip = this.order.driver_tip > 0 ? this.order.driver_tip : 0;
    this.order.voucher_amount = this.order.voucher_amount > 0 ? this.order.voucher_amount : 0;
    this.order.offer_amount = this.order.offer_amount > 0 ? this.order.offer_amount : 0;
    this.order.wallet_amount = this.order.wallet_amount > 0 ? this.order.wallet_amount : 0;
    this.order.reward_offer = this.order.reward_offer > 0 ? this.order.reward_offer : 0;

    grandTotal = this.order.order_sub_total + this.order.delivery_charge + this.surchargeAmount + this.order.service_charge + this.order.driver_tip - this.order.voucher_amount - this.order.offer_amount + this.order.charity_amount - this.order.reward_offer;
    grandTotal = this.precise_round(grandTotal, 2);
    this.order.order_grand_total = grandTotal - this.order.charity_amount;
    if (this.order.payment_wallet) {
      if (this.user.wallet_amount <= grandTotal) {
        this.order.wallet_amount = this.user.wallet_amount;
      } else {
        this.order.wallet_amount = grandTotal;
      }
    }
    grandTotal = grandTotal - this.order.wallet_amount;
    if (grandTotal <= 0) {
      grandTotal = 0;
    } else {
    }
    return grandTotal;
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  uploadCart(cart: Cart) {
    this.subs.add(
      this.cartService.create(cart).
        pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.carts = res;
            this.cartService.carts = this.carts;
          },
          (err) => {
            this.carts = [];
          }
        )
    )
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  driverTip(tip: number) {
    if (tip > 0) {
      this.order.driver_tip = this.order.driver_tip + tip;
    } else {
      this.order.driver_tip = 0;
    }
    this.getGrandTotal();
  }

  uploadBulk(Carts: Cart[]) {
    this.subs.add(
      this.cartService.cartBulk(Carts).
        pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.carts = res;
          },
          (err) => { }
        )
    );
  }

  handleItemNotAvailable(type: string) {
    if (type == 'yes') {
      const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
      const itemsToRemove = this.carts.filter(cart => {
        const cartOrderType = cart.menu.product_order_type?.toLowerCase();
        const cartDaysRaw = cart.menu.product_day?.toLowerCase() || 'all';
        const cartDays = cartDaysRaw.split(',').map(day => day.trim());
        const orderTypeMismatch = cartOrderType !== 'both' && cartOrderType !== this.order.order_type.toLowerCase();
        const dayMismatch = cartDaysRaw !== 'all' && !cartDays.includes(selectedDay);
        return orderTypeMismatch || dayMismatch;
      });

      const deleteCalls = itemsToRemove.map(item =>
        this.subs.add(
          this.cartService.delete(item.id).
            pipe(finalize(() => { }))
            .subscribe(
              (res) => {
                this.fetchCarts();
              },
              (err) => {
                this.fetchCarts();
              }
            )
        )
      );
      // this.router.navigate(['/checkout']);
    }
    this.modalService.dismissAll();
  }

  validateCheckout() {
    this.errorPaymentMethod = false;
    this.hasOrderTypeMismatch = false;
    this.hasDayMismatch = false;

    const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
    this.carts.forEach(item => {
      const menu = item.menu;
      const itemOrderType = menu.product_order_type?.toLowerCase();
      const itemDaysRaw = menu.product_day?.toLowerCase() || 'all';
      const itemDays = itemDaysRaw.split(',').map(day => day.trim());
      if (itemOrderType !== 'both' && itemOrderType !== this.order.order_type.toLowerCase()) {
        this.hasOrderTypeMismatch = true;
      }
      if (itemDaysRaw !== 'all' && !itemDays.includes(selectedDay)) {
        this.hasDayMismatch = true;
      }
    });
    if (this.hasOrderTypeMismatch || this.hasDayMismatch) {
      this.modalService.dismissAll();
      this.modalService.open(this.itemNotAvailableModal, { size: 'lg' });
      return;
    }

    if (!this.order.payment_method) {
      window.scroll(0, 0);
      this.errorPaymentMethod = 'Please select payment method';
      this.isCheckoutLoading = false;
      return
    }

    if (this.order.payment_method != 'Stripe') {
      this.order.card_id = '';
      this.order.transaction_id = '';
    }

    if (this.order.order_type != 'delivery') {
      this.order.address_id = '';
      this.order.address = '';
      this.order.flat_no = '';
      this.order.destination_latitude = '';
      this.order.destination_longitude = '';
    }

    if (this.surcharges.length > 0) {
      this.order.surcharges = this.surcharges;
      this.order.surcharge_amount = this.surchargeAmount;
    }

    if (!this.errorPaymentMethod) {

      if (this.order.payment_wallet) {
        this.order.payment_wallet = 'Yes';
        if (this.user.wallet_amount >= this.order.order_grand_total) {
          this.order.card_id = '';
          this.order.transaction_id = '';
          this.order.payment_method = 'Wallet';
          this.order.split_payment = 'No';
          this.order.payment_status = 'P';
          this.order.paid_full = 'Yes';
          this.saveOrder();
        } else {
          this.order.split_payment = 'Yes';
        }
      }

      if (this.order.payment_method == 'Stripe' && !this.order.card_id) {
        window.scroll(0, 0);
        this.errorPaymentMethod = 'Please select credit card';
        this.isCheckoutLoading = false;
        return
      }

      if (this.order.payment_method == 'Stripe') {
        this.order.payment_status = 'P';
        this.order.paid_full = 'Yes';
        this.saveOrder();
        // this.stripeCustomer = this.stripeCustomers.find(card => card.id == this.order.card_id);
        // this.createPaymentIntent(this.stripeCustomer);
      } else if (this.order.payment_method == 'Paypal') {
        // this.order.payment_status = 'P';
      } else if (this.order.payment_method == 'COD') {
        this.order.payment_status = 'NP';
        this.saveOrder();
      } else {
      }
    }
  }

  async createPaymentIntent(stripeCustomerNew) {
    this.isCheckoutLoading = true;

    this.stripeCustomer = stripeCustomerNew;
    this.stripeCustomer.restaurant_id = this.order.restaurant_id;
    this.stripeCustomer.amount = this.getGrandTotal();
    this.stripeCustomer.order_id = this.order.id;
    this.subs.add(
      this.stripeCustomerService
        .payment_intent(this.stripeCustomer)
        .pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            this.continueToPayment(res.payment_intent_id);
          },
          (err) => {
            this.errorPaymentMethod = 'Sorry your Payment Faild! Please try again';
          }
        )
    );
  }

  async continueToPayment(paymentIntentId) {
    this.stripe.confirmCardPayment(paymentIntentId).then((result) => {
      if (result.error) {
        this.isCheckoutLoading = false;
        this.errorPaymentMethod = result.error.message;
        this.uploadBulk(this.order.carts);
      } else {
        if (result.paymentIntent.status === 'succeeded') {
          this.order.payment_status = 'P';
          this.order.paid_full = 'Yes';
          this.order.transaction_id = result.paymentIntent.id
          var orderId = btoa(this.order.id);
          localStorage.removeItem(environment.order);
          this.subs.add(this.userService.me()
            .pipe(finalize(() => this.isCheckoutLoading = false))
            .subscribe(res => {
              this.userService.saveUser(res);
            }, err => this.error = err)
          );
          // this.saveOrder();
          this.modalService.dismissAll();
          this.router.navigateByUrl(`/order-detail/${orderId}`);
          this.isCheckoutLoading = false;
        }
        // this.order.carts = this.carts;
      }
    });
  }

  saveOrder() {
    this.isCheckoutLoading = true

    if (!this.user.phone_number) {
      this.modalService.dismissAll();
      this.modalService.open(this.profileModal, { backdrop: 'static', size: 'md', backdropClass: 'customBackdrop', keyboard: false });
      return
    }

    if (this.order.payment_method == 'COD') {
      // this.order.payment_method = 'cod';
      if ((((!this.user.phone_verify && this.restaurant.site_setting.order_verify_type == 'phone') || !this.user.email_verify && this.restaurant.site_setting.order_verify_type == 'mail') || (this.restaurant.site_setting.order_verify_type == 'both' && (!this.user.phone_verify || !this.user.email_verify))) && this.restaurant.site_setting?.order_verify == '1') {
        this.otpSend();
        this.modalService.open(this.otpModal, { backdrop: 'static', size: 'md', backdropClass: 'customBackdrop', keyboard: false });
        return
      }
    }
    this.order.carts = this.order.carts.concat(this.order.applied_offers);
    this.order.status = 'Pending';
    this.order.type = 'Web';
    this.subs.add(
      this.orderService.create(this.order).
        pipe(finalize(() => { }))
        .subscribe(
          (res) => {
            if (this.order.payment_method == 'Stripe') {
              this.order.id = res.id;
              this.stripeCustomer = this.stripeCustomers.find(card => card.id == this.order.card_id);
              this.createPaymentIntent(this.stripeCustomer);
            } else {
              this.modalService.open(this.placeModal, { backdrop: 'static', size: 'md', centered: true, backdropClass: 'customBackdrop modal-dialog-centered', keyboard: false });
              localStorage.removeItem(environment.order);
              var orderId = btoa(res.id);
              this.subs.add(this.userService.me()
                .pipe(finalize(() => this.isCheckoutLoading = false))
                .subscribe(res => {
                  this.userService.saveUser(res);
                }, err => this.error = err)
              );
              this.modalService.dismissAll();
              this.router.navigateByUrl(`/order-detail/${orderId}`);
            }
          },
          (err) => {
            this.error = err;
          }
        )
    )
  }

  otpSend() {
    this.phoneTab = true;
    if (this.restaurant.site_setting.order_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.restaurant.site_setting.order_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.restaurant.site_setting.order_verify_type;
      }
    }

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.messagingService.success("Otp sent successfully !!");
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  resendOtp() {
    this.phoneTab = true;
    if (this.restaurant.site_setting.order_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.restaurant.site_setting.order_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.restaurant.site_setting.order_verify_type;
      }
    }

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.messagingService.success("Otp sent successfully !!");
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  validateOtp() {
    this.isModelOtpLoading = true;
    this.Modelotperror = false;

    if (this.restaurant.site_setting.order_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.restaurant.site_setting.order_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.restaurant.site_setting.order_verify_type;
      }
    }

    if (!this.verifyOtp && (this.user.verify_type == 'both' || this.user.verify_type == 'phone')) {
      this.messagingService.error("Please enter phone verification code !!");
      this.isModelOtpLoading = false;
    } else if (!this.user.email_otp && (this.user.verify_type == 'both' || this.user.verify_type == 'email')) {
      this.messagingService.error("Please enter email verification code !!");
      this.isModelOtpLoading = false;
    } else {
      this.user.otp = this.verifyOtp
      this.subs.add(
        this.userService.varifyOtp(this.user).
          pipe(finalize(() => this.isModelOtpLoading = false))
          .subscribe(
            (res) => {
              this.verifyOtp = '';
              this.phoneTab = false;
              this.subs.add(this.userService.me()
                .pipe(finalize(() => this.isModelOtpLoading = false))
                .subscribe(res => {
                  this.user = res
                  this.messagingService.success("Otp verify successfully !!");
                  this.saveOrder();
                }, err => this.Modelotperror = err)
              );
              this.modalService.dismissAll();
            },
            (err) => {
              this.messagingService.error(err);
            }
          )
      )
    }
  }

  validateCard(model) {
  }

  checkPaypalMin(payment_method_name: string) {
    this.errorPaymentMethod = null;
    this.errorPaypal = null;
    this.order.payment_method = payment_method_name;
    if (this.order.payment_method == 'Stripe' && this.stripeCustomers.length > 0) {
      this.order.card_id = this.stripeCustomers[0].id;
    }
  }

  addCard(model) {
    this.isModelLoading = true;
    this.addStripeCustomer = new StripeCustomer();
    this.openModal(model);
  }

  voucherCheck() {
    this.errorVoucher = false;

    if (!this.order.voucher_code || this.order.voucher_code == undefined) {
      this.errorVoucher = 'Please enter voucher code';
    }
    else {
      this.voucher.restaurant_id = this.restaurant.id;
      this.voucher.voucher_code = this.order.voucher_code;
      this.voucher.order_type = this.order.order_type;
      this.voucher.delivery_date = this.order.delivery_date;

      this.subs.add(
        this.voucherService.find(this.voucher).
          pipe(finalize(() => this.isLoading = false))
          .subscribe(
            (res) => {
              this.order.voucher_code = res.voucher_code;
              if (res.offer_mode == 'price') {
                this.order.voucher_amount = res.offer_value;
              }
              if (res.offer_mode == 'percentage') {
                this.order.voucher_percentage = res.offer_value;
                this.order.voucher_amount = this.order.order_sub_total * res.offer_value / 100;
              }
              if (res.offer_mode == 'free_delivery') {
                this.order.delivery_charge = 0;
              }
              this.order.offer_amount = 0;
              this.order.offer_percentage = 0;
              this.order.applied_offers = [];
            },
            (err) => {
              this.errorVoucher = err;
            }
          )
      )
    }
  }

  voucherRemove() {
    this.order.voucher_code = '';
    this.order.voucher_percentage = 0;
    this.order.voucher_amount = 0;
    let order = JSON.parse(this.cartService.getOrder());
    this.order.delivery_charge = order?.delivery_charge;
    this.getGrandTotal();
    this.offerCheck();
  }

  charityAdd(event) {
    if (event.target.checked === true) {
      this.order.charity_amount = this.restaurant?.site_setting?.charity_amount;
      this.order.charity_message = this.restaurant?.site_setting?.charity_message;
    } else {
      this.order.charity_amount = 0;
      this.order.charity_message = '';
    }
    this.getGrandTotal();
  }

  redeemAdd(event) {
    if (event.target.checked === true) {
      this.order.reward_used = 'Y';
      this.order.reward_offer = this.order.rewardPoint;
      this.order.reward_offer_percentage = this.order.rewardPercentage;
    } else {
      this.order.reward_used = 'N';
      this.order.reward_offer = 0;
      this.order.reward_offer_percentage = 0;
    }
    this.getGrandTotal();
  }

  walletAdd(event) {
    if (event.target.checked === true) {
      // if (this.user.wallet_amount < this.getGrandTotal()) {
      //   this.order.wallet_amount = this.user.wallet_amount;
      // } else {
      //   this.order.wallet_amount = this.getGrandTotal();
      // }
    } else {
      this.order.wallet_amount = 0;
    }
    this.getGrandTotal();
  }

  fetchMe() {
    this.subs.add(this.userService.me()
      .pipe(finalize(() => this.isModelOtpLoading = false))
      .subscribe(res => {
        this.user = res;
        this.userService.saveUser(res);
      }, err => this.Modelotperror = err)
    );
  }

  profileUpdate(model) {
    this.modalService.dismissAll();
    this.modalService.open(this.profileModal, { backdrop: 'static', size: 'md', backdropClass: 'customBackdrop', keyboard: false });
  }

  updateUser(form: NgForm) {
    this.isModelProfileLoading = true;
    this.ModelProfileerror = null;

    this.user.password = '';
    this.user.confirmPassword = '';
    this.user.current_password = '';

    if (this.userService.user.phone_number != this.user.phone_number) {
      this.user.phone_verify = false;

      this.userService
        .disabledPhoneVerify(this.user)
        .pipe(finalize(() => (this.isModelProfileLoading = false)))
        .subscribe(
          (res) => {
            this.order.customer_phone = this.user.phone_number
            this.isCheckoutLoading = false;
          },
          (err) => { }
        );
    }

    this.userService
      .update(this.user)
      .pipe(finalize(() => (this.isModelProfileLoading = false)))
      .subscribe(
        (res) => {
          this.fetchMe();
          this.messagingService.success("Profile updated successfully !!");
          this.modalService.dismissAll();
          if ((((!this.user.phone_verify && this.restaurant.site_setting.order_verify_type == 'phone') || !this.user.email_verify && this.restaurant.site_setting.order_verify_type == 'mail') || (this.restaurant.site_setting.order_verify_type == 'both' && (!this.user.phone_verify || !this.user.email_verify))) && this.restaurant.site_setting?.order_verify == '1') {
            this.otpSend();
            this.modalService.open(this.otpModal, { size: 'md', backdropClass: 'customBackdrop', });
          }
        },
        (err) => {
          this.ModelProfileerror = err;
        }
      );
  }

  keyPress(event: any) {
    const pattern = /[0-9]/;

    let inputChar = String.fromCharCode(event.charCode);
    if (event.keyCode != 8 && !pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd', 'en_US')
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
