export class Reward {
  id: string;
  order_id: string;
  customer_id: string;
  restaurant_name: string;

  total: string;
  points: string;
  type: string;

  reward_totalpoint: string;
  reward_validity: number;
  reward_offer: string;
  status: boolean = true;

  created: string;
  modified: string;

  static toFormData(reward: Reward) {
    const formData = new FormData();

    if (reward.id) formData.append('id', reward.id);
    if (reward.order_id) formData.append('order_id', reward.order_id);
    if (reward.customer_id) formData.append('customer_id', reward.customer_id);
    if (reward.restaurant_name) formData.append('restaurant_name', reward.restaurant_name);
    if (reward.total) formData.append('total', reward.total);
    if (reward.points) formData.append('points', reward.points);
    if (reward.type) formData.append('type', reward.type);

    return formData;
  }
}
