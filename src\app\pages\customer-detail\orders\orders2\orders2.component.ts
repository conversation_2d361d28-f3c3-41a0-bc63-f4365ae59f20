import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { OrderService } from 'src/app/core/services/order.service';
import { Order } from 'src/app/core/models/order';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { Review } from 'src/app/core/models/review';
import { ReviewService } from 'src/app/core/services/review.service';
import { NotificationService } from 'src/app/core/services/notification.service';
import { MessagingService } from 'src/app/core/services/messaging.service';

@Component({
  selector: 'app-orders2',
  templateUrl: './orders2.component.html',
  styleUrls: ['./orders2.component.scss']
})
export class Orders2Component implements OnInit {

  subs = new Subscription();
  isLoading = false;
  error = null;

  isModelLoading = false;
  Modelerror = null;

  user: User;
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;
  currentRate = 2;
  totalOrders = 0;
  last_page = 0;
  previousPage: any;
  page = 1;
  per_page = 10;

  orders: Order[] = [];
  restaurant: Restaurant = new Restaurant();
  reviewAdd: Review;

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private currencyPipe: CurrencyPipe,
    public orderService: OrderService,
    public reviewService: ReviewService,
    private restaurantService: RestaurantService,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef,
    private messagingService: MessagingService,
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    this.modalOptions = {
      backdrop: 'static',
      backdropClass: 'customBackdrop',
    };
    this.fetchRestaurant();
    this.fetchOrders();
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
      }, err => this.error = err)
    );
  }

  fetchOrders() {
    this.isLoading = true;

    this.subs.add(this.orderService.get({ customer_id: this.userId, restaurant_id: this.restaurant_id, page: this.page, per_page: this.per_page })
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        (res) => {
          this.orders = res.data;
          this.totalOrders = res.total;
          this.last_page = res.last_page;

          this.cdr.detectChanges();

        }, (err) => {
          this.orders = [];
          this.totalOrders = 0;
        }
      )
    )
  }

  loadPage(page: number) {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.fetchOrders();
    }
  }

  orderView(orderId) {
    var orderIds = btoa(orderId);
    this.router.navigateByUrl(`/order-detail/${orderIds}`);
  }

  reOrderCreate(orderId) {
    // this.isLoading = true;

    this.subs.add(this.orderService.reOrder(orderId)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(
        res => {
          this.router.navigateByUrl(`/menu`);
        }, err => this.error = err
      )
    )
  }

  addReview(model, order: Order) {
    this.reviewAdd = new Review();
    this.reviewAdd.order_id = order.id;
    this.reviewAdd.customer_id = order.customer_id;
    this.reviewAdd.restaurant_id = order.restaurant_id;
    this.openModal(model);
  }

  validateReview() {
    this.isModelLoading = true;
    this.Modelerror = false;

    if (!this.reviewAdd.rating) {
      this.Modelerror = 'Please select rating';
      this.isModelLoading = false
      return;
    }
    if (!this.reviewAdd.message) {
      this.Modelerror = 'Please enter message';
      this.isModelLoading = false
      return;
    }
    if (!this.Modelerror) {
      this.subs.add(this.reviewService.create(this.reviewAdd)
        .pipe(finalize(() => this.isModelLoading = false))
        .subscribe(
          (res) => {
            this.fetchOrders();
            this.messagingService.success("Thank's for your feedback");
            this.modalService.dismissAll();
          }, (err) => {
            this.Modelerror = err
          }
        )
      )
    }
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
