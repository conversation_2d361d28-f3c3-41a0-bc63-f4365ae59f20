import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorHandler } from 'src/app/shared/error-handler';
import { environment } from 'src/environments/environment';
import { Booking } from '../models/booking';

@Injectable({
  providedIn: 'root',
})
export class BookingService {
  private url = environment.apiBaseUrl + 'bookings/';
  public carts: Booking[] = []
  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.customer_id) params = params.set('customer_id', options.customer_id);
    if (options.restaurant_id) params = params.set('restaurant_id', options.restaurant_id);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<Booking> {
    return this.http.get<Booking>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(booking: Booking): Observable<any> {
    return this.http.post<Booking>(this.url, Booking.toFormData(booking))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(booking: Booking): Observable<any> {
    return this.http.post<Booking>(this.url + booking.id, Booking.toFormData(booking))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<any> {
    return this.http.delete<Booking>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }
}
