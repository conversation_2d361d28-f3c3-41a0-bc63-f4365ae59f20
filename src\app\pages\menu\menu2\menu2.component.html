<ng-container>
  <div class="main-section">
    <!-- Restaurant Details -->
    <div style="position: relative;">
      <div class="banner-wrapper w-100 d-flex">
        <img class="menu_banner" [src]="restaurant?.promotions?.[0]?.image_url ?? 'assets/menu_banner.jpg'"
          alt="Promotion Banner" (error)="handleImageError($event)">
        <div class="banner-overlay">&nbsp;</div>
      </div>
      <div class="container" style="position: relative;">
        <div class="restaurant_details shadow">
          <div class="restaurant_logo shadow">
            <img src="assets/logo.png" alt="" (error)="handleImageError($event)">
          </div>
          <div class="restaurant-name-info">
            <div class="name">
              <h3>{{restaurant.restaurant_name}}</h3>
              <p class="text-muted mb-0">
                <i class="fas fa-map-marker-alt"></i>
                {{restaurant.street_address}}
              </p>
            </div>
            <div class="info">
              <p class="text-primary mb-0" style="cursor: pointer;" (click)="navigateToInfo()">
                <i class="fas fa-info-circle"></i> Info
              </p>
              <p class="mb-0 text-muted">
                <i class="fas fa-star text-warning me-1"></i>
                <strong class="text-dark">{{ restaurant.average_rating }}</strong>
                ({{ formatReviews(restaurant.total_reviews) }})
              </p>
            </div>
          </div>
          <div
            class="d-flex flex-column flex-lg-row align-items-center justify-content-between w-100 px-1 px-md-4 mb-2 mb-lg-0"
            style="column-gap: 1rem;row-gap: 1rem;">
            <div class="delivery-pickup-wrapper">
              <button (click)="orderType('delivery')" class="delivery-btn"
                [ngClass]="{'active': order.order_type == 'delivery'}">
                <i class="fas fa-car me-1"></i>Delivery
                <br>
                <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_delivery != 'No'">
                  Est. {{ restaurant.estimate_time }} mins</p>
                <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_delivery != 'Yes'">Unavailable</p>
              </button>
              <button (click)="orderType('pickup')" class="pickup-btn"
                [ngClass]="{'active': order.order_type == 'pickup'}">
                <i class="fas fa-shopping-bag me-1"></i>Pickup
                <br>
                <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_pickup != 'No'">
                  Est. {{ restaurant.pickup_estimate_time }} mins</p>
                <p class="m-0" style="font-size:9px" *ngIf="restaurant.restaurant_pickup != 'Yes'">Unavailable</p>
              </button>
              <div
                [ngClass]="{'delivery-pickup-slider':true,'delivery-active': order.order_type == 'delivery','pickup-active': order.order_type == 'pickup'}">
                &nbsp;
              </div>
            </div>
            <div class="d-flex align-items-center gap-2">
              <ng-container *ngFor="let cuisine of restaurant.cuisine_names">
                <p class="m-0">
                  {{ cuisine.cuisine_name }}
                </p>
                <div class="divider-vertical"></div>
              </ng-container>
              <p class="m-0 d-flex align-items-center gap-1"
                [ngClass]="{'text-success': restaurant.restaurant_status === 'Open', 'text-danger': restaurant.restaurant_status === 'Closed'}">
                <i class="fas fa-clock text-primary"></i>
                {{ restaurant.restaurant_status === 'Open' ? 'Open Now' : 'Closed'}}
              </p>
              <div class="divider-vertical" *ngIf="restaurant.delivery_charge == 'Yes'"></div>
              <p class="m-0 d-flex align-items-center gap-1" *ngIf="restaurant.delivery_charge == 'Yes'">
                <i class="fas fa-map-marker-alt text-primary"></i>
                {{ restaurant.delivery_setting ? restaurant.delivery_setting[0]?.delivery_miles : 'N/A' }} Miles
              </p>

            </div>
          </div>
        </div>
      </div>
      <div class="restaurant-loader" *ngIf="isRestaurantLoading">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
    </div>

    <!-- Categories Section -->
    <div class="container mt-3 mb-5 h-100" style="position: relative;">
      <!-- Menu List -->
      <div class="row h-100">
        <div class="col-sm-12 col-lg-3 h-100 menu-list" *ngIf="!isMobile;else mobileMenu">
          <ng-container *ngFor="let category of categories">
            <div class="menu-item" [class.active]="category.id == selectedCategoryId"
              (click)="selectCategory(category.id)">
              {{category.category_name}}
            </div>
          </ng-container>
        </div>
        <ng-template #mobileMenu>
          <div class="floating-mobile-menu">
            <div class="mobile-menu-details" [class.expanded]="showMenu">
              <div class="menu-item" *ngFor="let category of categories"
                [class.active]="category.id == selectedCategoryId" (click)="selectCategory(category.id);toggleMenu()">
                {{category.category_name}}
              </div>
            </div>
            <div class="menu-button" (click)="toggleMenu()">
              <div class="cart-icon">
                <i class="fas fa-bars"></i>
              </div>
            </div>
          </div>
        </ng-template>
        <!-- Menu Details -->
        <div class="col-12 col-sm-12 col-lg-9 h-100">
          <ng-container *ngIf="restaurant.suggest_product_count > 0">
            <div class="recommend-menu mb-4">
              <div class="menu-name">
                <i class="far fa-thumbs-up"></i>
                RECOMMENDED FOR YOU
              </div>
              <div class="menu-wrapper">
                <button class="scroll-btn prev" (click)="scrollMenu('left')">
                  <i class="fas fa-chevron-left"></i>
                </button>
                <div class="menu-items" #menuItems>
                  <ng-container *ngFor="let category of categories;let i=index; trackBy: trackByFn">
                    <ng-container *ngFor="let menu of category.menu;let j=idx; trackBy: trackByFn">
                      <ng-container
                        *ngIf="menu.menu_addon == 'No' && menu.price_option == 'single' && menu.is_suggest == '1'">
                        <div class="item">
                          <span class="fw-bold name" [attr.title]="menu?.menu_name">
                            {{ menu?.menu_name }}
                          </span>
                          <span class="text-muted description" [attr.title]="menu?.menu_description">
                            {{ menu?.menu_description }}
                          </span>
                          <div class="d-flex align-items-center justify-content-between">
                            <ng-container *ngIf="menu.variants?.length == 1 && menu.product_percentage <= 0">
                              <span class="text-muted price">{{menu.variants[0].orginal_price | currency: "GBP"}}</span>
                            </ng-container>
                            <ng-container *ngIf="menu.variants?.length == 1 && menu.product_percentage > 0">
                              <div class="d-flex" style="column-gap: 10px;">
                                <span class="text-muted price" style="text-decoration: line-through;">
                                  {{menu.variants[0].orginal_price | currency: "GBP"}}
                                </span>
                                <span class="text-muted price">
                                  {{ (menu.variants[0].orginal_price - (menu.variants[0].orginal_price *
                                  menu.product_percentage / 100)) | currency: "GBP" }}
                                </span>
                              </div>
                            </ng-container>
                            <ng-container *ngIf="menu.variants?.length > 1">
                              <span class="text-muted price" style="font-size: 12px;">Multiple Options</span>
                            </ng-container>
                            <ng-container *ngIf="menu.variants?.length == 1;else multipleOptions">
                              <button class="recommended-add-to-cart"
                                (click)="addItemToCart(menuModal,menu,menu.id,menu.variants[0].id)">
                                <i class="fas fa-plus"></i>
                              </button>
                            </ng-container>
                            <ng-template #multipleOptions>
                              <button class="recommended-add-to-cart"
                                (click)="selectVariant(variantSelectionModal,menu)">
                                <i class="fas fa-plus"></i>
                              </button>
                            </ng-template>
                          </div>
                          <div class="recommended-item-count" *ngIf="getQuantity(menu.id) > 0">
                            {{ getQuantity(menu.id) }}
                          </div>
                        </div>
                      </ng-container>
                    </ng-container>
                  </ng-container>
                </div>
                <button class="scroll-btn next" (click)="scrollMenu('right')">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>
            </div>
          </ng-container>
          <div class="d-flex justify-content-between align-items-center mb-4 px-2">
            <h5 class="m-0">Menu</h5>
            <h5 class="text-right cursor m-0 ms-auto" (click)="allergyShow()" *ngIf="restaurant.is_allergy">
              <i class="fa fa-info-circle" aria-hidden="true"></i>
              Allergy
            </h5>
          </div>
          <ng-container *ngFor="let category of categories">
            <div [id]="'category_' + category.id" class="menu-details mb-4 category-section"
              [attr.data-category-id]="category.id" [aria-labelledby]="'category_' + category.id">
              <div class="menu-name">
                <span>{{category.category_name}}</span>
                <p class="text-muted description">{{category.description}}</p>
              </div>
              <div class="menu-items">
                <div class="menu-item-wrapper shadow border" *ngFor="let menu of category.menu;trackBy: trackByFn">
                  <div class="menu-item border">
                    <div class="details-container" [ngClass]="{'w-100': !menu.image_url}">
                      <span class="fw-medium name" [attr.title]="menu.menu_name">
                        {{menu.menu_name}}
                      </span>
                      <span class="text-muted description" [attr.title]="menu.menu_description">
                        {{menu.menu_description}}
                      </span>
                      <div class="d-flex my-2" style="column-gap: 4px;">
                        <span *ngIf="menu?.popular_dish =='Yes'">
                          <img src="./assets/alergy_icon/popular.png" title="Popular Dish" alt="popular dish"
                            (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.spicy_dish =='mild'">
                          <img src="./assets/alergy_icon/mint.png" title="Mild" alt="mild"
                            (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.spicy_dish =='medium'">
                          <img src="./assets/alergy_icon/med.png" title="Medium" alt="medium"
                            (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.spicy_dish =='extra_hot'">
                          <img src="./assets/alergy_icon/hot.png" title="Extra Hot" alt="extra hot"
                            (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.spicy_dish =='slightly_hot'">
                          <img src="./assets/alergy_icon/slightlyhot.png" title="Slightly Hot" alt="slightly hot"
                            (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.spicy_dish =='hot'">
                          <img src="./assets/alergy_icon/hot1.png" title="Hot" alt="hot" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.vegetarian">
                          <img src="./assets/alergy_icon/vega.png" title="Vegetarian" alt="vegetarian tiffintom"
                            (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.contains_nuts">
                          <img src="./assets/alergy_icon/Contains-nuts.png" title="Contains Nuts"
                            alt="contains nuts tiffintom" (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.milk">
                          <img src="./assets/alergy_icon/milk.png" title="Milk" alt="milk tiffintom"
                            (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.mustard">
                          <img src="./assets/alergy_icon/Mustard.png" title="Mustard" alt="mustard tiffintom"
                            (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.eggs">
                          <img src="./assets/alergy_icon/eggs.png" title="Eggs" alt="eggs tiffintom"
                            (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.fish">
                          <img src="./assets/alergy_icon/fish.png" title="Fish" alt="fish tiffintom"
                            (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.gluten_free">
                          <img src="./assets/alergy_icon/Gluten-Free.png" title="Gluten-Free"
                            alt="gluten free tiffintom" (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.vegan">
                          <img src="./assets/alergy_icon/vegan.png" title="Vegan" alt="vegan tiffintom"
                            (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.halal">
                          <img src="./assets/alergy_icon/halal.png" title="Halal" alt="halal tiffintom"
                            (error)="handleImageError($event)" />
                        </span>
                        <span class="alergy-icon" *ngIf="menu?.koshar">
                          <img src="./assets/alergy_icon/keshar.png" title="Koshar" alt="koshar tiffintom"
                            (error)="handleImageError($event)" />
                        </span>
                      </div>
                      <ng-container *ngIf="menu.variants?.length == 1 && menu.product_percentage <= 0">
                        <span class="mt-auto">{{menu.variants[0].orginal_price | currency: "GBP"}}</span>
                      </ng-container>
                      <ng-container *ngIf="menu.variants?.length == 1 && menu.product_percentage > 0">
                        <div class="d-flex" style="column-gap: 10px;">
                          <span class="mt-auto" style="text-decoration: line-through;">
                            {{menu.variants[0].orginal_price | currency: "GBP"}}
                          </span>
                          <span class="mt-auto">
                            {{ (menu.variants[0].orginal_price - (menu.variants[0].orginal_price *
                            menu.product_percentage / 100)) | currency: "GBP" }}
                          </span>
                        </div>
                      </ng-container>
                      <ng-container *ngIf="menu.variants?.length > 1">
                        <span class="mt-auto text-primary" style="font-size: 12px;">Multiple Options</span>
                      </ng-container>
                    </div>
                    <div class="image-container" *ngIf="menu.image_url;else addToCartButton">
                      <img *ngIf="menu.image_url" [src]="menu.image_url" [alt]="menu?.menu_name" class="menu-item-image"
                        (error)="handleImageError($event)">
                      <ng-container *ngTemplateOutlet="addToCartButton"></ng-container>
                    </div>
                    <!-- Add to Cart Button -->
                    <ng-template #addToCartButton>
                      <ng-container *ngIf="menu.variants?.length == 1;else multipleOptions">
                        <button class="add-to-cart" (click)="addItemToCart(menuModal,menu,menu.id,menu.variants[0].id)">
                          <i class="fas fa-plus"></i>
                        </button>
                      </ng-container>
                      <ng-template #multipleOptions>
                        <button class="add-to-cart" (click)="selectVariant(variantSelectionModal,menu)">
                          <i class="fas fa-plus"></i>
                        </button>
                      </ng-template>
                    </ng-template>
                    <!-- Item Count -->
                    <div class="item-count" *ngIf="getQuantity(menu.id) > 0">{{ getQuantity(menu.id) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
      <!-- Floating Cart -->
      <div class="floating-cart">
        <div class="cart-summary" (click)="toggleCart()">
          <div class="cart-icon">
            <i class="fas fa-shopping-basket"></i>
            <span class="cart-count">{{ carts.length }}</span>
          </div>
          <div class="cart-info">
            <div class="d-flex justify-content-between">
              <span class="cart-total">{{ carts.length }} Items</span>
              <span class="cart-total" *ngIf="totalPrice()">
                ({{ convertNumber(getGrandTotal()) }})
                <!-- ({{ totalPrice() | currency: "GBP" }}) -->
              </span>
            </div>
            <span class="cart-label">View Basket</span>
          </div>
        </div>
        <!-- Expandable Cart Details -->
        <div class="cart-details" [class.expanded]="showCart">
          <div #cartPanel class="cart-items-list">
            <ng-container *ngIf="carts && (carts.length > 0);let cartIndex = index;else noItem">
              <div class="text-primary fw-bold pb-2 border-bottom text-center">
                You have {{carts.length > 0 ? carts.length :''}} items
              </div>
              <div class="text-danger fw-bold pb-2 border-bottom text-center"
                *ngIf="carts.length != 0 && order.order_type == 'delivery' && restaurant.minimum_order >= getGrandTotal()">
                <span>Minimum Order {{convertNumber(restaurant.minimum_order)}}</span>
              </div>
              <ng-container *ngFor="let cart of carts; let i = index">
                <div class="cart-item">
                  <div class="d-flex flex-column">
                    <span class="item-name">{{ cart.menu_name }}</span>
                    <span class="text-muted item-description fs-6">{{ cart.subaddons_name }}</span>
                  </div>
                  <div class="d-flex align-items-center justify-content-between">
                    <div class="quantity-controls">
                      <button class="delete-btn" (click)="updateToCart(cart, 'remove')">
                        <i class="fas fa-minus"></i>
                      </button>
                      <span>{{ cart.quantity }}</span>
                      <button class="add-btn" (click)="updateToCart(cart, 'add')">
                        <i class="fas fa-plus"></i>
                      </button>
                    </div>
                    <div class="quantity-controls">
                      <span class="item-total">{{ convertNumber(cart.total_price) }}</span>
                      <button class="delete-btn" (click)="updateToCart(cart, 'delete')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </ng-container>
            </ng-container>
            <ng-template #noItem>
              <div class="empty-cart-cls">
                <img src="assets/cartitem.png" class="img-fluid m-4" (error)="handleImageError($event)">
                <span class="item-name">No Item(s) Added</span>
              </div>
            </ng-template>
          </div>
          <ng-container *ngIf="restaurant.online_order == 'Yes' && carts.length != 0 && order.order_type == 'delivery'">
            <button type="button" (click)="navigateToPreCheckout()" class="checkout-btn"
              [ngClass]="{'disabled': getGrandTotal() < restaurant.minimum_order,'disabled': restaurant.restaurant_delivery != 'Yes'}"
              [disabled]="getGrandTotal() < restaurant.minimum_order || restaurant.restaurant_delivery != 'Yes'">
              <p class="m-0 d-flex justify-content-between fs-5">
                <span class="text-start">
                  {{ restaurant.currentStatus == 'Open' ? 'Checkout' : 'Pre Order'}}
                  <i class="fas fa-arrow-right"></i>
                </span>
                <span class="spacing text-end ms-4">{{convertNumber(getGrandTotal())}}</span>
              </p>
            </button>
          </ng-container>
          <ng-container *ngIf="restaurant.online_order == 'Yes' && carts.length != 0 && order.order_type == 'pickup'">
            <button type="button" (click)="navigateToPreCheckout()" class="checkout-btn"
              [ngClass]="{'disabled': restaurant.restaurant_pickup != 'Yes'}"
              [disabled]="restaurant.restaurant_pickup != 'Yes'">
              <p class="m-0 d-flex justify-content-between fs-5">
                <span class="text-start">
                  {{ restaurant.currentStatus == 'Open' ? 'Checkout' : 'Pre Order' }}
                  <i class="fas fa-arrow-right"></i>
                </span>
                <span class="spacing text-end ms-4">{{convertNumber(getGrandTotal())}}</span>
              </p>
            </button>
          </ng-container>
          <div class="cart-loader" *ngIf="isCartLoading">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="category-loader" *ngIf="isCategoryLoading">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div> -->
    </div>
  </div>
</ng-container>

<ng-template #allergyModal let-modal>
  <div class="allergy-modal p-3">
    <div class="allergy-modal-header">
      <button class="allergy-close-btn" (click)="modal.dismiss('Cross click')">
        <i class="fas fa-times"></i>
      </button>
      <h4 class="allergy-modal-title text-primary fw-bold" id="modal-basic-title">
        Do you have a food allergy?
      </h4>
    </div>
    <div class="modal-body my-2">
      <div class="col-sm-12 col-xs-12 form-group mb-3" *ngIf="restaurant.allergy_message">
        {{restaurant.allergy_message}}
      </div>
    </div>
    <button class="allergy-modal-close w-100" (click)="modal.dismiss('Cross click')">
      Close
    </button>
  </div>
</ng-template>

<ng-template #itemNotAvailableModal let-modal>
  <div class="item-not-available-modal p-3">
    <div class="item-not-available-modal-header">
      <button class="item-not-available-close-btn" (click)="handleItemNotAvailable('no')">
        <i class="fas fa-times"></i>
      </button>
      <h4 class="item-not-available-title text-primary fw-bold" id="modal-basic-title">
        Not Available !
      </h4>
    </div>
    <div class="modal-body">
      <!-- Message for only Order Type mismatch -->
      <div class="col-sm-12 col-xs-12 form-group mb-3 text-center fw-bold pt-2"
        *ngIf="hasOrderTypeMismatch && !hasDayMismatch">
        Some items in your cart are not available for the selected order type.
      </div>
      <!-- Message for only Day mismatch -->
      <div class="col-sm-12 col-xs-12 form-group mb-3 text-center fw-bold pt-2"
        *ngIf="hasDayMismatch && !hasOrderTypeMismatch">
        Some items are not available on the selected day.
      </div>
      <!-- Message for both mismatches -->
      <div class="col-sm-12 col-xs-12 form-group mb-3 text-center fw-bold pt-2"
        *ngIf="hasOrderTypeMismatch && hasDayMismatch">
        Some items are not available for the selected order type or delivery day.
      </div>
    </div>
    <!-- Buttons -->
    <div class="row pt-3 g-3">
      <div class="col-sm-6 col-xs-6">
        <button class="item-not-available-modal-close w-100" (click)="handleItemNotAvailable('no')">
          No
        </button>
      </div>
      <div class="col-sm-6 col-xs-6">
        <button class="item-not-available-modal-close w-100" (click)="handleItemNotAvailable('yes')">
          Yes
        </button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #menuModal let-modal>
  <div class="row text-center align-middle justify-content-center" *ngIf="isModelLoading">
    <div class="col-md-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class="menu-modal p-3" *ngIf="!isModelLoading">
    <div class="menu-modal-header">
      <button class="menu-close-btn" (click)="modal.dismiss('Cross click')">
        <i class="fas fa-times"></i>
      </button>
      <h4 class="menu-modal-title text-primary fw-bold fs-5" id="modal-basic-title"
        *ngIf="selectedMenu && selectedMenu.product_percentage <= 0">
        {{ selectedMenu.menu_name }} : {{selectedVariant?.orginal_price | currency: "GBP"}}
      </h4>
      <h4 class="menu-modal-title text-primary fw-bold fs-5" id="modal-basic-title"
        *ngIf="selectedMenu && selectedMenu.product_percentage > 0">
        {{ selectedMenu.menu_name }} :
        <span class="px-1" style="text-decoration: line-through;">
          {{ selectedVariant?.orginal_price | currency: "GBP" }}
        </span>
        {{ (selectedVariant?.orginal_price - (selectedVariant?.orginal_price * selectedMenu?.product_percentage / 100))
        | currency: "GBP" }}
      </h4>
    </div>
    <div class="modal-body" *ngIf="selectedVariant && !isModelLoading">
      <ng-container *ngFor="let mainAddon of selectedVariant.main_addons; let i = index">
        <div class="w-100 mb-3">
          <div>
            <h6 class="text-dark fw-bold">
              {{ mainAddon?.mainaddons_name }}
              <span class="text-danger fs-6" *ngIf="mainAddon.mainaddons_mini_count!= 0">(Required)</span>
            </h6>
            <div *ngIf="mainAddon?.sub_addons?.length > 0">
              <div *ngFor="let subAddon of mainAddon.sub_addons;let isLast = last">
                <div class="radio radio-inline" [ngClass]="{'border-bottom': !isLast}"
                  *ngIf="mainAddon.mainaddons_count <= 1" style="padding: 0px 10px 0px;margin: 7px 0 2px;">
                  <input type="radio" id="subaddon_{{subAddon?.id}}" class="addon_ss subaddon_{{subAddon?.id}}"
                    [(ngModel)]="mainAddon.selectedSubAddonId" name="subaddon_radio_{{i}}" [value]="subAddon?.id">
                  <label for="subaddon_{{subAddon?.id}}" class="ps-4 pb-3">{{ subAddon?.subaddons_name }}
                    ( {{convertNumber(subAddon?.subaddons_price)}} )</label>
                </div>
                <div class="checkbox checkbox-inline" [ngClass]="{'border-bottom': !isLast}"
                  *ngIf="mainAddon.mainaddons_count > 1"
                  style="padding: 0px 10px 0px;margin: 7px 0 2px;border-bottom: 1px solid #ccc;">
                  <input type="checkbox" id="subaddon_{{subAddon?.id}}" class="addon_ss subaddon_{{subAddon?.id}}"
                    [(ngModel)]="subAddon.selected" name="subaddon_radio_{{i}}">
                  <label for="subaddon_{{subAddon?.id}}" class="ps-4 pb-3">{{ subAddon?.subaddons_name }}
                    ( {{convertNumber(subAddon?.subaddons_price)}} )</label>
                </div>
              </div>
              <small class="text-danger" *ngIf="mainAddon.max_error">You should select maximum
                {{mainAddon.mainaddons_count }} addons</small>
              <small class="text-danger" *ngIf="mainAddon.min_error">You should select minimum
                {{mainAddon.mainaddons_mini_count }} addons</small>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
    <button class="menu-modal-close w-100" (click)="validate(menuModal)">
      Add To Cart
    </button>
  </div>
</ng-template>

<ng-template #variantSelectionModal let-modal>
  <div class="variant-selection-modal p-3">
    <div class="w-100 d-flex align-items-start">
      <!-- Close Button -->
      <button class="close-btn" (click)="modal.dismiss('Cross click');quantity = 1;">
        <i class="fas fa-times"></i>
      </button>
      <!-- Product Title -->
      <div class="w-100 d-flex flex-column ms-3">
        <h5 class="product-title text-center fw-bold text-primary" [attr.title]="variantMenu?.menu_name">
          {{ variantMenu?.menu_name || 'Select Variant' }}
        </h5>
        <p class="text-center text-muted m-0 description-truncate" [attr.title]="variantMenu?.menu_description">
          {{ variantMenu?.menu_description }}
        </p>
      </div>
    </div>
    <!-- Choose Option Label -->
    <div class="option-label fw-bold mt-3 mb-2 fw-bold text-primary">Choose Option</div>
    <!-- Variant Options -->
    <div *ngFor="let variant of variantMenu.variants; let i = index" class="variant-radio">
      <label class="variant-option d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center gap-2">
          <input type="radio" name="selectedVariant" [value]="variant.id" [(ngModel)]="selectedVariantId"
            (ngModelChange)="setVariant()" />
          <span class="text-capitalize sub-name-truncate" [attr.title]="variant?.sub_name">
            {{ variant?.sub_name }}
          </span>
        </div>
        <span class="price text-muted">
          {{ getVariantPrice(variant) | currency: 'GBP' }}
        </span>
      </label>
    </div>
    <!-- Quantity Selector -->
    <div class="quantity-selector d-flex justify-content-center align-items-center mt-4 gap-3">
      <button class="qty-btn" (click)="decrementQty()">-</button>
      <span>{{ quantity }}</span>
      <button class="qty-btn" (click)="incrementQty()">+</button>
    </div>
    <!-- Add to Cart Button -->
    <button class="add-to-cart-btn mt-4 w-100" [disabled]="!selectedVariantId"
      (click)="checkAddOnAndProceed(addOnSelectionModal,modal,menuModal);">
      Add to cart
    </button>
    <div class="variant-loading" *ngIf="isModelLoading">
      <div class="row text-center align-middle justify-content-center my-5">
        <div class="col-md-12 ms-2 spinner-border text-primary text-center">
          <span class="visually-hidden text-center">Loading...</span>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #addOnSelectionModal let-modal>
  <div class="addons-selection-modal p-3">
    <!-- Close Button & Product Title -->
    <div class="d-flex align-items-start">
      <!-- Close Button -->
      <button class="back-to-variant-btn"
        (click)="modal.dismiss('Cross click');selectVariant(variantSelectionModal,variantMenu,'back')">
        <i class="fas fa-chevron-left"></i>
      </button>
      <!-- Product Title -->
      <div class="d-flex flex-column ms-3">
        <h5 class="product-title text-center fw-bold text-primary m-0" [attr.title]="variantMenu?.menu_name">
          {{ variantMenu?.menu_name || 'Select Variant' }}
        </h5>
        <div class="d-flex align-items-center gap-1">
          <span class="text-capitalize" style="font-size: 14px;" [attr.title]="selectedVariant?.sub_name">
            {{ selectedVariant?.sub_name }}
          </span>
          <span class="dot-divider"></span>
          <span class="price text-muted" style="font-size: 14px;">
            {{ getVariantPrice(selectedVariant) | currency: 'GBP' }}
          </span>
        </div>
      </div>
    </div>
    <!-- Addons Options -->
    <div class="w-100 mt-4">
      <ng-container *ngFor="let mainAddon of selectedVariant.main_addons; let i = index">
        <div class="w-100 mt-4">
          <div class="variant-option-title">
            <h6 class="w-100 text-dark fw-bold fs-6 title-truncate m-0" [attr.title]="mainAddon?.mainaddons_name">
              {{ mainAddon?.mainaddons_name }}
            </h6>
            <p class="w-100 m-0 fw-bold text-danger title-truncate" *ngIf="mainAddon.mainaddons_mini_count != 0">
              (Required) Choose upto {{ mainAddon.mainaddons_count }} - Min. {{ mainAddon.mainaddons_mini_count}}
            </p>
            <p class="w-100 m-0 fw-bold text-primary title-truncate" *ngIf="mainAddon.mainaddons_mini_count == 0">
              (Optional) Select up to {{ mainAddon.mainaddons_count }} add on(s)
            </p>
          </div>
          <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count== 0 && mainAddon.max_error">
            You can select a maximum of {{ mainAddon.mainaddons_count }} {{ mainAddon?.mainaddons_name }}.
          </span>
          <span class="text-danger" *ngIf="mainAddon.mainaddons_mini_count!= 0 && mainAddon.min_error">
            (Required) • Select at least {{ mainAddon.mainaddons_mini_count }} add on(s).
          </span>
          <span class="text-danger"
            *ngIf="mainAddon.mainaddons_mini_count!= 0 && mainAddon.mainaddons_count!= 0 && mainAddon.max_error">
            You can select a maximum of {{ mainAddon.mainaddons_count }} {{ mainAddon?.mainaddons_name }}.
          </span>
          <div *ngIf="mainAddon?.sub_addons?.length > 0">
            <div *ngFor="let subAddon of mainAddon.sub_addons;let isLast = last">
              <div class="radio radio-inline variant-radio" [ngClass]="{'border-0': isLast}"
                *ngIf="mainAddon.mainaddons_count <= 1">
                <input type="radio" id="subaddon_{{subAddon?.id}}" class="addon_ss subaddon_{{subAddon?.id}}"
                  style="margin-top: 5px !important;" [(ngModel)]="mainAddon.selectedSubAddonId"
                  name="subaddon_radio_{{i}}" [value]="subAddon?.id">
                <label for="subaddon_{{subAddon?.id}}" class="ps-4 pb-3">{{ subAddon?.subaddons_name }}
                  ( {{convertNumber(subAddon?.subaddons_price)}} )</label>
              </div>
              <div class="checkbox checkbox-inline variant-checkbox" [ngClass]="{'border-0': isLast}"
                *ngIf="mainAddon.mainaddons_count > 1">
                <input type="checkbox" id="subaddon_{{subAddon?.id}}" class="addon_ss subaddon_{{subAddon?.id}}"
                  style="margin-top: 5px !important;" [(ngModel)]="subAddon.selected" name="subaddon_radio_{{i}}">
                <label for="subaddon_{{subAddon?.id}}" class="ps-4 pb-3">{{ subAddon?.subaddons_name }}
                  ( {{convertNumber(subAddon?.subaddons_price)}} )
                </label>
              </div>
            </div>
            <small class="text-danger" *ngIf="mainAddon.max_error">You should select maximum
              {{mainAddon.mainaddons_count }} addons</small>
            <small class="text-danger" *ngIf="mainAddon.min_error">You should select minimum
              {{mainAddon.mainaddons_mini_count }} addons</small>
          </div>
        </div>
      </ng-container>
    </div>
    <!-- Confirm Button -->
    <button class="add-to-cart-btn mt-4 w-100" [disabled]="!selectedVariant" (click)="validate(menuModal)">
      Confirm
    </button>
  </div>
</ng-template>

<ng-template #otpModal let-modal>
  <div class="otp-modal p-3">
    <div class="otp-modal-header">
      <button class="otp-close-btn" (click)="modal.dismiss('Cross click')">
        <i class="fas fa-times"></i>
      </button>
      <h4 class="otp-modal-title text-primary fw-bold" id="modal-basic-title">
        <span class="fw-bold"
          *ngIf="(restaurant?.site_setting?.signup_verify_type == 'phone' || restaurant?.site_setting?.signup_verify_type == 'both') && !user.phone_verify">Phone
        </span>
        <span class="fw-bold"
          *ngIf="restaurant?.site_setting?.signup_verify_type == 'both' && !user.phone_verify && !user.email_verify"> &
        </span>
        <span class="fw-bold"
          *ngIf="(restaurant?.site_setting?.signup_verify_type == 'both' || restaurant?.site_setting?.signup_verify_type == 'mail') && !user.email_verify">Email
        </span>
        Verify
      </h4>
    </div>
    <div class="modal-body my-2">
      <p>Your otp has been sent on this
        <span class="fw-bold"
          *ngIf="(restaurant?.site_setting?.signup_verify_type == 'phone' || restaurant?.site_setting?.signup_verify_type == 'both') && !user.phone_verify">{{user.phone_number}}
        </span>
        <span class="fw-bold"
          *ngIf="restaurant?.site_setting?.signup_verify_type == 'both' && !user.phone_verify && !user.email_verify"> OR
        </span>
        <span class="fw-bold"
          *ngIf="(restaurant?.site_setting?.signup_verify_type == 'both' || restaurant?.site_setting?.signup_verify_type == 'mail') && !user.email_verify">{{user.username}}
        </span>
        <a (click)="profileUpdate(profileModal)" class="u-1 cursor"
          *ngIf="(restaurant?.site_setting?.signup_verify_type == 'phone' || restaurant?.site_setting?.signup_verify_type=='both') && !user.phone_verify">Edit</a>
      </p>
      <div class=" col-sm-12 col-xs-12 form-group"
        *ngIf="(restaurant?.site_setting?.signup_verify_type == 'phone' || restaurant?.site_setting?.signup_verify_type == 'both') && !user.phone_verify">
        <input type="text" class="form-control col-md-8" id="otp" name="otp" [(ngModel)]="verifyOtp"
          placeholder="Please enter phone verification code" />
      </div>
      <div class=" col-sm-12 col-xs-12 form-group mt-2"
        *ngIf="(restaurant?.site_setting?.signup_verify_type == 'both' || restaurant?.site_setting?.signup_verify_type == 'mail') && !user.email_verify">
        <input type="text" class="form-control col-md-8" id="email_otp" name="email_otp" [(ngModel)]="user.email_otp"
          placeholder="Please enter email verification code" />
      </div>
      <div *ngIf="Modelotperror">
        <span class="text-danger">{{ Modelotperror }}</span>
      </div>
    </div>
    <div class="modal-footer justify-content-between">
      <button class="otp-modal-close w-100" type="button" [disabled]="isModelOtpLoading" (click)="resendOtp()">
        Re-send
      </button>
      <button class="otp-modal-close w-100" type="button" [disabled]="isModelOtpLoading" (click)="validateOtp()">
        Submit
      </button>
    </div>
  </div>
</ng-template>