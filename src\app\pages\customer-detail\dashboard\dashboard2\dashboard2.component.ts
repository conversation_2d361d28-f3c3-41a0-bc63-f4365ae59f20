import { CurrencyPipe, formatDate, ViewportScroller } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from 'src/app/core/services/user.service';
import { environment } from 'src/environments/environment';
import { User } from 'src/app/core/models/user';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { Order } from 'src/app/core/models/order';
import { Booking } from 'src/app/core/models/booking';
import { StripeCustomer } from 'src/app/core/models/stripe-customer';
import { AddressBook } from 'src/app/core/models/address-book';
import { Review } from 'src/app/core/models/review';
import { ReviewService } from 'src/app/core/services/review.service';
import { NotificationService } from 'src/app/core/services/notification.service';
import { MessagingService } from 'src/app/core/services/messaging.service';

@Component({
  selector: 'app-dashboard2',
  templateUrl: './dashboard2.component.html',
  styleUrls: ['./dashboard2.component.scss']
})
export class Dashboard2Component implements OnInit {
  subs = new Subscription();
  isLoading = false;
  error = null;
  errorMessage = null;
  errorChangePassword = null;

  isModelLoading = false;
  Modelerror = null;

  phoneTab = false;
  emailTab = false;

  isModelOtpLoading = false;
  Modelotperror = null;

  isModelEmailOtpLoading = false;
  ModelEmailOtpError = null;

  user: User;
  modalOptions: NgbModalOptions;
  restaurant_id: string;
  userId: string;
  previousPage: any;
  verifyOtp: string;

  dashboardDetails: any;
  completeOrderList: Order[] = [];
  deliveredOrderList: Order[] = [];
  bookigList: Booking[] = [];
  cardList: StripeCustomer[] = [];
  addressList: AddressBook[] = [];
  reviewAdd: Review;
  booking: Booking = new Booking();

  restaurant: Restaurant = new Restaurant();

  options = { query: null, page: 1, per_page: 10, customer_id: null };

  stripe; // : stripe.Stripe;
  card;
  confirmation;
  cardElement;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private viewPortScroller: ViewportScroller,
    private modalService: NgbModal,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private currencyPipe: CurrencyPipe,
    private restaurantService: RestaurantService,
    private reviewService: ReviewService,
    private notificationService: NotificationService,
    private messagingService: MessagingService,
  ) { }

  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    let user = JSON.parse(this.userService.getUser());
    this.user = user;
    this.userId = user?.id;
    this.options.customer_id = user?.id;
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
    }
    this.modalOptions = {
      backdrop: 'static',
      size: 'lg',
      backdropClass: 'customBackdrop',
    };
    this.fetchRestaurant();
    this.fetchDashboard();

    if (this.route.snapshot.queryParamMap.get('code') && this.route.snapshot.queryParamMap.get('code') != null) {
      let code = atob(this.route.snapshot.queryParamMap.get('code'));
      this.subs.add(this.userService.show(code)
        .pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(res => {
          this.user.email_verify = true;
          this.userService
            .update(this.user)
            .pipe(finalize(() => (this.isLoading = false)))
            .subscribe(
              (res) => {
                this.subs.add(this.userService.me()
                  .pipe(finalize(() => this.isLoading = false))
                  .subscribe(res => {
                    this.user = res
                  }, err => this.errorMessage = err)
                );
              },
              (err) => {
                this.errorMessage = err;
              }
            );

        }, err => this.Modelotperror = err)
      );
    }
  }

  fetchRestaurant() {
    this.isLoading = true;

    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
      }, err => this.error = err)
    );
  }

  fetchDashboard() {
    this.isLoading = true;

    this.subs.add(this.userService.allDashboard()
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.dashboardDetails = res;
        this.completeOrderList = res.complete_order_list;
        this.deliveredOrderList = res.delivered_order_list;
        this.bookigList = res.booking_list;
        this.cardList = res.card_list;
        this.addressList = res.address_list;
      }, err => this.error = err)
    );
  }

  orderView(orderId) {
    var orderIds = btoa(orderId);
    this.router.navigateByUrl(`/order-detail/${orderIds}`);
  }

  addReview(model, order: Order) {
    this.reviewAdd = new Review();
    this.reviewAdd.order_id = order.id;
    this.reviewAdd.customer_id = order.customer_id;
    this.reviewAdd.restaurant_id = order.restaurant_id;
    this.openModal(model);
  }

  validateReview() {
    this.isModelLoading = true;
    this.Modelerror = false;

    if (!this.reviewAdd.rating) {
      this.Modelerror = 'Please select rating';
      this.isModelLoading = false
      return;
    }
    if (!this.reviewAdd.message) {
      this.Modelerror = 'Please enter message';
      this.isModelLoading = false
      return;
    }
    if (!this.Modelerror) {
      this.subs.add(this.reviewService.create(this.reviewAdd)
        .pipe(finalize(() => this.isModelLoading = false))
        .subscribe(
          (res) => {
            this.fetchDashboard();
            this.messagingService.success("Thank's for your feedback")
            this.modalService.dismissAll();
          }, (err) => {
            this.Modelerror = err
          }
        )
      )
    }
  }

  bookingView(model, booking) {
    this.booking = booking;
    this.openModal(model);
  }

  bookView(bookingId) {
    var bookingIds = btoa(bookingId);
    this.router.navigateByUrl(`/booking-detail/${bookingIds}`);
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  // otpSend(model) {
  //   this.openModal(model);
  //   this.subs.add(
  //     this.userService.sendOtp().
  //       pipe(finalize(() => this.isModelOtpLoading = false))
  //       .subscribe(
  //         (res) => {
  //         },
  //         (err) => {
  //           this.Modelotperror = err;
  //         }
  //       )
  //   )
  // }

  otpSend() {
    this.phoneTab = true;
    this.subs.add(
      this.userService.sendOtp().
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.messagingService.success("otp send successfully !!")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  resendOtp() {
    this.subs.add(
      this.userService.sendOtp().
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.messagingService.success("otp re-send successfully !!")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  validateOtp() {
    this.isModelOtpLoading = true;
    this.Modelotperror = false;

    if (!this.verifyOtp) {
      // this.Modelotperror = 'Please enter your one-time password';
      this.messagingService.error("Please enter your one-time password !!")
      this.isModelOtpLoading = false;
    } else {
      this.user.otp = this.verifyOtp
      this.subs.add(
        this.userService.varifyOtp(this.user).
          pipe(finalize(() => this.isModelOtpLoading = false))
          .subscribe(
            (res) => {
              this.verifyOtp = '';
              this.phoneTab = false;
              this.subs.add(this.userService.me()
                .pipe(finalize(() => this.isModelOtpLoading = false))
                .subscribe(res => {
                  this.user = res
                  this.messagingService.success("otp verify successfully !!")
                }, err => this.Modelotperror = err)
              );
              this.modalService.dismissAll();
            },
            (err) => {
              this.messagingService.error(err)
              // this.Modelotperror = err;
            }
          )
      )
    }
  }

  emailOtpSend() {
    this.emailTab = true;
    this.user.verify_type = 'email';

    this.subs.add(
      this.userService.sendBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.user.verify_type = '';
            this.messagingService.success("email otp sent successfully !!")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  validateEmail() {
    this.isModelEmailOtpLoading = true; this.ModelEmailOtpError = null;

    if (!this.verifyOtp) {
      this.messagingService.error("Please enter your one-time password !!")
      this.isModelEmailOtpLoading = false;
    } else {
      this.user.verify_type = 'email';
      this.user.email_otp = this.verifyOtp
      this.subs.add(
        this.userService.varifyBothOtp(this.user).
          pipe(finalize(() => this.isModelEmailOtpLoading = false))
          .subscribe(
            (res) => {
              this.user.verify_type = '';
              this.verifyOtp = '';
              this.emailTab = false;
              this.subs.add(this.userService.me()
                .pipe(finalize(() => this.isModelEmailOtpLoading = false))
                .subscribe(res => {
                  this.user = res
                  this.messagingService.success("email otp verify successfully !!")
                }, err => this.ModelEmailOtpError = err)
              );
              this.modalService.dismissAll();
            },
            (err) => {
              this.messagingService.error(err)
            }
          )
      )
    }
    // let code = location.origin + "/customer-detail?code=" + btoa(this.user.id);
    // this.subs.add(
    //   this.userService.varifyEmail({ code: code, id: this.user.id }).
    //     pipe(finalize(() => this.isModelOtpLoading = false))
    //     .subscribe(
    //       (res) => {
    //         this.messagingService.success("email send successfully !!")
    //       },
    //       (err) => {
    //         this.Modelotperror = err;
    //       }
    //     )
    // )
  }

  resendEmailOtp() {
    this.user.verify_type = 'email';
    this.subs.add(
      this.userService.sendOtp().
        pipe(finalize(() => this.isModelEmailOtpLoading = false))
        .subscribe(
          (res) => {
            this.user.verify_type = '';
            this.messagingService.success("otp re-send successfully !!")
          },
          (err) => {
            this.Modelotperror = err;
          }
        )
    )
  }

  public precise_round(num, decimals) {
    return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  convertNumber(event) {
    if (event >= 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      event = 0;
    }
    return val1
  }

  convertToDate(date, format?) {
    return formatDate(date, format ? format : 'yyyy-MM-dd H:m:s', 'en_US')
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
