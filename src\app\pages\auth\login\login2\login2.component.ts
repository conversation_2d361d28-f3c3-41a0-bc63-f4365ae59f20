import { Component, ElementRef, Input, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { User } from 'src/app/core/models/user';
import { UserService } from 'src/app/core/services/user.service';
import { SocialAuthService, FacebookLoginProvider, SocialUser, GoogleLoginProvider } from 'angularx-social-login';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { Restaurant } from 'src/app/core/models/restaurant';
import { environment } from 'src/environments/environment';
import { ViewportScroller } from '@angular/common';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { NotificationService } from 'src/app/core/services/notification.service';
import { LocationService } from 'src/app/core/services/location.service';
import { MessagingService } from 'src/app/core/services/messaging.service';

@Component({
  selector: 'app-login2',
  templateUrl: './login2.component.html',
  styleUrls: ['./login2.component.scss']
})
export class Login2Component implements OnInit {
  @ViewChild('changePasswordModel', { static: true }) changePasswordModel: ElementRef;

  subs = new Subscription();
  user: User = new User();
  forgotUser: User = new User();
  isLoading = false;
  error = null;
  isModelLoading = false;
  Modelerror = null;
  errorMessage: string;
  isModelPasswordLoading = false;
  ModelPassworderror = null;
  restaurant: Restaurant = new Restaurant();
  restaurant_id: string;
  modalOptions: NgbModalOptions;

  isEmailVerify = false;

  otp: string = '';
  isOtpLogin: boolean = false;
  isOtpSend: boolean = false;
  isModelOtpLoading: boolean = false;
  config = {
    allowNumbersOnly: false,
    length: 6,
    isPasswordInput: false,
    disableAutoFocus: false,
    placeholder: '',
    inputStyles: {
      width: '50px',
      height: '50px',
    },
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private userService: UserService,
    private location: LocationService,
    public activeModal: NgbActiveModal,
    private viewPortScroller: ViewportScroller,
    private messagingService: MessagingService,
    private socialAuthService: SocialAuthService,
    private restaurantService: RestaurantService,
    private notificationService: NotificationService,
  ) { }
  ngOnInit(): void {
    this.restaurant_id = environment.googleFirebase;
    this.socialAuthService.authState.subscribe((user) => {
      this.user.role_id = '3';
      this.user.social_id = user.id;
      this.user.first_name = user.firstName;
      this.user.last_name = user.lastName;
      this.user.username = user.email;
      this.user.password = '123123';

      this.userService
        .signup(this.user)
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.userService.saveUser(res);
            this.router.navigateByUrl('/');
          },
          (err) => {
            this.messagingService.error(err + "Tiffintom");
          }
        );
    });
    this.fetchRestaurant();
    this.modalOptions = {
      backdrop: 'static',
      size: 'md',
      backdropClass: 'customBackdrop',
      windowClass: 'rounded-modal'
    };
  }

  fetchRestaurant() {
    this.subs.add(this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.restaurant = res;
      }, err => this.error = err)
    );
  }

  loginWithFacebook(): void {
    this.socialAuthService.signIn(FacebookLoginProvider.PROVIDER_ID);
  }

  loginWithGoogle(): void {
    this.socialAuthService.signIn(GoogleLoginProvider.PROVIDER_ID);
  }

  resendOtp() {
    this.isModelOtpLoading = true;
    this.subs.add(
      this.userService.sendOtp().
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.messagingService.success("otp re-send successfully !!")
          },
          (err) => {
            this.messagingService.error(err);
          }
        )
    )
  }

  login(form: NgForm) {
    if (!form.valid) {
      Object.keys(form.controls).forEach(key => {
        form.controls[key].markAsDirty();
        form.controls[key].updateValueAndValidity();
      });
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;

    if (this.isOtpLogin) {
      this.user.login_type = 'otp';
    }

    if (this.isOtpLogin && this.isOtpSend) {
      if (this.otp.length == 6) {
        this.user.otp = this.otp;
      } else {
        this.isLoading = false;
        this.messagingService.error('Please enter valid otp');
        return;
      }
    }

    this.userService
      .login(this.user)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe(
        (res) => {
          this.userService.saveUser(res);

          if (this.user.otp) {
            this.isOtpLogin = false;
            this.isOtpSend = false;
            this.router.navigateByUrl('/menu');
          }

          if (this.isOtpLogin) {
            this.isOtpSend = true;
          } else {
            this.location.back('');
          }
        },
        (err) => {
          this.messagingService.error(err);
        }
      );
  }

  forgotPassword(model) {
    this.forgotUser = new User();
    this.openModal(model);
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        console.log(`Save  ${this.getDismissReason(result)}`);
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  public toggle(element: HTMLElement) {
    element.classList.toggle('d-none');
  }

  validateEmail() {
    this.isModelLoading = true;
    this.Modelerror = false;

    if (!this.forgotUser.username) {
      this.Modelerror = 'Please enter valid email';
      this.isModelLoading = false;
    } else {
      this.subs.add(
        this.userService.forgotpassword(this.forgotUser).
          pipe(finalize(() => this.isModelLoading = false))
          .subscribe(
            (res) => {
              this.forgotUser = res;
              this.isEmailVerify = true;
            },
            (err) => {
              this.Modelerror = err;
            }
          )
      );
    }
  }

  validateOtp() {
    this.isModelLoading = true;
    this.Modelerror = false;
    this.forgotUser.verify_type = 'email';

    if (!this.forgotUser.email_otp) {
      this.Modelerror = 'Please enter valid one time password';
      this.isModelLoading = false;
    } else {
      this.subs.add(
        this.userService.varifyBothOtp(this.forgotUser).
          pipe(finalize(() => this.isModelLoading = false))
          .subscribe(
            (res) => {
              this.modalService.dismissAll();
              this.modalService.open(this.changePasswordModel, {
                backdrop: 'static',
                size: 'md',
                centered: true,
                backdropClass: 'customBackdrop modal-dialog-centered',
                windowClass: 'rounded-modal',
                keyboard: false
              });
            },
            (err) => {
              this.Modelerror = err;
            }
          )
      );
    }
  }

  validatePassword() {
    this.isModelPasswordLoading = true;
    this.ModelPassworderror = false;
    this.forgotUser.verify_type = 'email';

    if (!this.forgotUser.password) {
      this.ModelPassworderror = 'Please enter valid new password';
      this.isModelPasswordLoading = false;
    } else if (!this.forgotUser.confirmPassword) {
      this.ModelPassworderror = 'Please enter valid confirm password';
      this.isModelPasswordLoading = false;
    } else if (this.forgotUser.password != this.forgotUser.confirmPassword) {
      this.ModelPassworderror = 'Password and confrim password not matched.';
      this.isModelPasswordLoading = false;
    } else {
      this.subs.add(
        this.userService.changepassword(this.forgotUser).
          pipe(finalize(() => this.isModelPasswordLoading = false))
          .subscribe(
            (res) => {
              this.modalService.dismissAll();
              this.userService.saveUser(res);
              this.location.back('');
            },
            (err) => {
              this.ModelPassworderror = err;
            }
          )
      );
    }
  }

  onOtpChange(otp: any) {
    this.otp = otp;
  }

  ngOnDestroy() { }
}
