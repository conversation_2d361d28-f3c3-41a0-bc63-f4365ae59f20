.review-summary {
  background: linear-gradient(180deg, #ffffff, #fafafa);
  border-radius: 1.2rem;
  padding: 1rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 1rem;
  text-align: center;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }

  .score {
    font-size: 2.8rem;
    font-weight: 700;
    color: #222;
    margin: 0;

    .out-of {
      font-size: 1.3rem;
      color: #999;
      font-weight: 400;
    }
  }

  .rating-stars {
    margin: 0;
  }

  .recommendation {
    font-size: 1.15rem;
    font-weight: 600;
    color: #28a745;
    margin-bottom: 0.4rem;
    letter-spacing: 0.3px;
  }

  .review-count {
    color: #777;
    font-size: 0.92rem;
    margin-bottom: 0.5rem;
  }

  hr {
    border: none;
    border-top: 1px solid #eee;
    margin: 1.2rem 0;
  }

  .filter-title {
    font-weight: 700;
    color: var(--primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.75rem;
    font-size: 0.85rem;
  }

  .filter-option {
    padding: 0 1rem;
    cursor: pointer;
    border-radius: 0.5rem;
    transition: background 0.25s ease, transform 0.15s ease;
    display: flex;
    justify-content: center;

    width: fit-content;
    margin: auto;
    min-width: 10rem;

    &:hover {
      // background: rgba(255, 102, 0, 0.06);
      background: var(--primary);
      color: #fff;
      transform: scale(1.02);

      // ::ng-deep app-rating ul li.selected {
      //   color: #000 !important;
      // }
    }

    &.active {
      // background: rgba(255, 102, 0, 0.12);
      background: var(--primary);
      color: #fff;
      font-weight: 600;

      // ::ng-deep app-rating ul li.selected {
      //   color: #000 !important;
      // }
    }
  }
}

.review-card {
  background: #fff;
  border-radius: 1rem;
  padding: 1.2rem 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.review-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.08);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.review-date {
  font-size: 0.85rem;
  color: #888;
  font-weight: 500;
}

.review-author {
  font-weight: 700;
  font-size: 1rem;
  margin-top: 0.3rem;
}

.review-message {
  margin-top: 0.4rem;
  color: #444;
  line-height: 1.4;
  word-wrap: break-word;
}

.review-response {
  background: #f9f9f9;
  border-left: 4px solid var(--primary);
  padding: 0.8rem 1rem;
  margin-top: 0.8rem;
  border-radius: 0.5rem;
}

.response-title {
  font-weight: 600;
  margin-bottom: 0.3rem;
  font-size: 0.92rem;
}

.response-text {
  color: #555;
  font-size: 0.9rem;
}
