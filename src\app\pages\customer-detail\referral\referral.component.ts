import { Component, OnInit } from '@angular/core';
import { UserService } from 'src/app/core/services/user.service';

@Component({
  selector: 'app-referral',
  templateUrl: './referral.component.html',
  styleUrls: ['./referral.component.scss'],
})
export class ReferralComponent implements OnInit {
  menuTheme: string;

  constructor(public userService: UserService) { }

  ngOnInit(): void {
    this.userService.getUserTheme().subscribe(res => {
      this.menuTheme = res;
    })
  }
}
