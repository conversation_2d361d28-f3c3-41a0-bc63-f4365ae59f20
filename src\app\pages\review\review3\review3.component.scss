::ng-deep {
  .ant-form-item-children-icon {
    display: none !important;
  }
}
.review-response {
  padding: 15px 10px 5px 20px;
  margin-top: 15px;
  background-color: #f4f3f3;
  border-radius: 0 20px 20px 0;
  border-left: 8px solid var(--primary);
}

.review-response h6 {
  font-size: 20px;
  margin-bottom: 5px;
}

.review-response h6 span {
  font-size: 14px;
  font-weight: 600;
  color: #b5b5b5;
  padding-left: 5px;
}
.review-response p {
  font-size: 16px;
  margin-bottom: 12px;
  line-height: 24px;
}
.product-main-box-section .container {
  display: block;
  padding-bottom: 3rem;
}
.view-menu-btn {
  font-family: "Visby CF";
  font-size: 16px;
  font-weight: 700;
  color: #000000;
  width: 185px;
  height: 42px;
  line-height: 38px;
  text-align: center;
  border-radius: 25px;
  display: inline-block;
  text-decoration: none;
  border: 2px solid #fff;
  transition: all 0.5s;
  box-shadow: 0px 3px 6px 2.25px #00000040;
  background-color: #fff;
  position: absolute;
  right: 40px;
  bottom: 25px;
}

.view-menu-btn:hover {
  color: var(--primary);
  border-color: var(--primary);
}

.filter-list {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0;
}

.filter-list li {
  margin-right: 10px;
}

.filter-list li:last-child {
  margin-right: 0;
}

.filter-list li button {
  font-size: 15px;
  color: #000000;
  font-weight: 700;
  cursor: pointer;
  padding: 9px 18px;
  border-radius: 35px;
  line-height: 20px;
  text-decoration: none;
  display: inline-block;
  background-color: #f9f9f9;
  border: 1px solid #f4f3f3;
}

.filter-list li button img {
  width: 23px;
  margin-right: 6px;
  margin-top: -4px;
}

.filter-list li button.active {
  color: #fff !important;
  background-color: #000;
}
.filter-list li button::after {
  display: none;
}
.filter-list li button .fa-sterling-sign {
  font-size: 23px;
  margin-right: 6px;
}

.filter-list li button img.white-icon {
  display: none;
}

.filter-list li button.active img.white-icon {
  display: inline-block;
}

.filter-list li button.active img.black-icon {
  display: none;
}

.filter-list li button .fa-chevron-down {
  font-size: 19px;
  margin-left: 6px;
  transition: all 0.5s;
}
.filter-list li.show button .fa-chevron-down {
  transform: rotate(180deg);
}

.filter-list .filter-box button.filter-btn {
  font-size: 20px;
  color: #000000;
  font-weight: 700;
  margin-left: 0;
}

.filter-list .filter-box button.filter-btn img {
  width: 34px;
  padding: 2px;
  margin-left: 5px;
}

.reviews-rating-box {
  padding: 25px 25px 12px 30px;
  border: 2px solid #f4f3f3;
  border-radius: 20px;

  @media screen and (max-width: 1750px) {
    padding-right: 25px;
  }

  @media screen and (max-width: 1500px) {
    padding: 22px 25px 10px 25px;
    border-radius: 15px;
  }
}

.reviews-rating-box a {
  text-decoration: none;
}

.reviews-rating-box ul.reviews-list button {
  font-size: 25px;
  color: var(--primary);
  padding: 0;
  border: 0;
  background-color: transparent;
}
.rating-box .rating-star li button {
  padding: 0;
  color: #000;
  background-color: transparent;
  border: 0;
}

.dietary-dropdown .dropdown-menu .star-icons {
  line-height: 26px;
  padding-left: 10px;
}

.dietary-dropdown .dropdown-menu .star-icons li {
  display: inline-block;
  margin-right: 4px;
}

.dietary-dropdown .dropdown-menu .star-icons li i {
  font-size: 15px;
  color: #fd811f;
}

.filter-rating-list {
  position: relative;
  display: flex;
  align-items: center;
  padding-right: 12px;
  margin-bottom: 20px;
  cursor: pointer;
  font-size: 13px;
  color: #000;
  padding: 5px 20px;
  background-color: #f4f3f3;
  border-color: #f4f3f3;
  width: fit-content;
  border-radius: 50px;
  border: 2px solid #fff;
  font-weight: 700;
}
.reviews-rating-box ul li.active {
  background-color: var(--primary);
  border-color: var(--primary);
  color: #fff;
  .star-icons {
    li i {
      color: #fff;
    }
  }
}

.reviews-rating-box .filter-list {
  justify-content: normal !important;
}

.filter-rating-list ul.star-icons {
  margin-bottom: 0;
}

.filter-rating-list ul.star-icons li {
  display: inline-block;
  margin-right: 3px;
}

.filter-rating-list ul.star-icons li span {
  font-size: 20px;
  font-weight: 700;
  color: #000;
  position: relative;
  top: 2px;
}

.filter-rating-list ul.star-icons li i {
  font-size: 16px;
  color: #fd811f;
}

@media screen and (max-width: 1500px) {
  .product-main-box-section {
    padding-bottom: 50px;
  }
  .review-response {
    padding: 10px 10px 5px 15px;
    border-radius: 0 15px 15px 0;
    border-left: 6px solid var(--primary);
  }
  .review-response h6 {
    font-size: 16px;
    margin-bottom: 0px;
  }
  .review-response h6 span {
    font-size: 12px;
    padding-left: 2px;
  }
  .review-response p {
    font-size: 14px;
    margin-bottom: 10px;
    line-height: 20px;
  }
  .view-menu-btn {
    font-size: 14px;
    width: 165px;
    height: 38px;
    line-height: 34px;
    right: 40px;
    bottom: 25px;
  }
  .filter-list li button {
    font-size: 14px;
    padding: 9px 14px;
  }

  .filter-list li button img {
    width: 17px;
    margin-right: 5px;
  }

  .filter-list li button .fa-sterling-sign {
    font-size: 17px;
    margin-right: 5px;
    position: relative;
    top: 2px;
  }

  .filter-list li button .fa-chevron-down {
    font-size: 15px;
    margin-left: 5px;
  }
  .reviews-rating-box ul.reviews-list button {
    font-size: 18px;
  }

  .dietary-dropdown .dropdown-menu .star-icons li i {
    font-size: 12px;
  }

  .dietary-dropdown .dropdown-menu .star-icons {
    padding-left: 8px;
    line-height: 16px;
  }

  .filter-rating-list ul.star-icons li span {
    font-size: 16px;
  }

  .filter-rating-list ul.star-icons li {
    margin-right: 3px;
  }

  .filter-rating-list ul.star-icons li i {
    font-size: 14px;
  }
}

@media screen and (max-width: 1199px) {
  .filter-list li {
    margin-right: 10px;
  }

  .filter-list li button img {
    width: 16px;
    margin-right: 4px;
  }

  .filter-list li button .fa-chevron-down {
    font-size: 14px;
    margin-left: 4px;
  }
}

@media screen and (max-width: 991px) {
  .view-menu-btn {
    font-size: 14px;
    width: 150px;
    height: 36px;
    line-height: 32px;
    right: 30px;
    bottom: 20px;
  }
  .filter-list li button {
    white-space: nowrap;
  }
  .filter-section-bg.fixed .filter-list {
    padding-bottom: 0;
  }

  .dietary-dropdown .dropdown-menu ul.star-icons li {
    width: auto;
  }

  .dietary-dropdown .dropdown-menu .star-icons li i {
    font-size: 10px;
  }
  .filter-rating-list {
    padding: 5px 15px;
  }
}

@media screen and (max-width: 767px) {
  .reviews-rating-box .filter-list {
    flex-wrap: wrap;
  }
  .filter-rating-list {
    width: 47%;
    margin-bottom: 10px;
    padding: 5px 15px;
  }
}

@media screen and (max-width: 575px) {
  .product-main-box-section {
    padding-bottom: 40px;
  }
  .reviews-rating-box {
    padding: 15px 15px 12px 15px;
    border-radius: 10px;
  }

  .rating-list .rating-box {
    padding-top: 10px;
  }
}

@media screen and (max-width: 480px) {
  .review-response {
    padding: 8px 8px 5px 10px;
    border-radius: 0 10px 10px 0;
    border-left: 5px solid var(--primary);
  }
  .review-response h6 {
    font-size: 14px;
    line-height: 20px;
  }
  .review-response h6 span {
    font-size: 11px;
    padding-left: 0px;
    width: 100%;
    display: inline-block;
  }
  .review-response p {
    font-size: 13px;
    margin-bottom: 5px;
  }
  .view-menu-btn {
    font-size: 13px;
    width: 130px;
    height: 34px;
    line-height: 30px;
    right: 15px;
    bottom: 15px;
  }
}

@media screen and (max-width: 425px) {
  .filter-list .filter-rating-list {
    width: 100%;
    margin-right: 0;
  }
}

// =================================================================================================================================================================================
@import "../../../../assets/css/all.min.css";
@import "../../../../assets/css/fonts.css";
@import "../../../../assets/css/slick.css";

.product-main-box {
  position: relative;
}

.product-main-box img {
  width: 100%;
}

.product-main-box img.product-main-image {
  height: 350px;
  object-fit: cover;
  border-radius: 20px;
}

.product-main-box .product-logo {
  padding: 0 10px;
  width: 120px;
  height: 120px;
  line-height: 118px;
  background-color: #fff;
  box-shadow: 0px 0px 25px 0px #00000033;
  border-radius: 50%;
  text-align: center;
  position: absolute;
  left: 35px;
  bottom: -40px;
}

.product-main-box .product-logo img {
  width: 100%;
  border-radius: 50%;
}

@media screen and (max-width: 1750px) {
  .product-main-box img.product-main-image {
    height: 320px;
  }
}

@media screen and (max-width: 1500px) {
  .product-main-box img.product-main-image {
    height: 260px;
  }

  .product-main-box .product-logo {
    padding: 0 5px;
    width: 90px;
    height: 90px;
    line-height: 85px;
    left: 30px;
    bottom: -30px;
  }

  .product-main-box .product-logo img {
    border-radius: 50%;
  }
}

@media screen and (max-width: 575px) {
  .product-main-box .product-logo {
    width: 80px;
    height: 80px;
    line-height: 75px;
    left: 20px;
    bottom: -25px;
  }
}

@media screen and (max-width: 480px) {
  .product-main-box img.product-main-image {
    height: 240px;
    border-radius: 15px;
  }

  .product-main-box .product-logo {
    width: 60px;
    height: 60px;
    line-height: 55px;
    left: 15px;
    bottom: -30px;
  }
}

.rating-main-box {
  padding-right: 60px;

  @media screen and (max-width: 1750px) {
    padding-right: 30px;
  }

  @media screen and (max-width: 1500px) {
    padding-right: 38px;
  }

  @media screen and (max-width: 1300px) {
    padding-right: 0px;
  }

  @media screen and (max-width: 1199px) {
    padding-right: 0px;
  }

  @media screen and (max-width: 991px) {
    margin-bottom: 30px;
  }
}

.product-name h5 {
  font-size: 45px;
  margin-bottom: 14px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  line-height: 58px;

  @media screen and (max-width: 1750px) {
    font-size: 42px;
  }

  @media screen and (max-width: 1500px) {
    font-size: 34px;
    margin-bottom: 16px;
  }

  @media screen and (max-width: 480px) {
    font-size: 32px;
    margin-bottom: 10px;
  }
}

.product-rating-section {
  padding-top: 72px;

  @media screen and (max-width: 1750px) {
    padding-top: 65px;
  }

  @media screen and (max-width: 1500px) {
    padding-top: 60px;
  }

  @media screen and (max-width: 575px) {
    padding-top: 50px;
  }
}

.product-rating {
  margin-bottom: 12px;
}

.product-rating ul {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

.product-rating ul li {
  font-size: 15px;
  font-weight: 700;
  color: #8f8f8a;
  font-family: "Visby CF";
  position: relative;
  padding-right: 16px;
  margin-right: 18px;
}

.product-rating ul li::before {
  position: absolute;
  content: "";
  width: 2px;
  height: 28px;
  background-color: #8b8f8f;
  top: 50%;
  transform: translate(0, -50%);
  right: 0;
}

.product-rating ul li:last-child {
  padding-right: 0;
  margin-right: 0;
}

.product-rating ul li:last-child::before {
  display: none;
}

.product-rating ul li .fa-circle {
  font-size: 5px;
  margin: 0 11px;
  position: relative;
  top: -3px;
}

.product-rating ul li span {
  font-size: 25px;
  font-weight: 700;
  color: #000000;
  font-family: "Visby CF";
}

.product-rating ul li .fa-star {
  width: 25px;
  font-size: 25px;
  color: var(--primary);
  position: relative;
  top: -2px;
  margin-right: 5px;
}

.product-rating ul li .fa-location-dot {
  font-size: 19px;
  color: var(--primary);
  margin-right: 8px;
}

@media screen and (max-width: 1500px) {
  .product-rating {
    margin-bottom: 6px;
  }

  .product-rating ul li {
    font-size: 12px;
    padding-right: 10px;
    margin-right: 12px;
  }

  .product-rating ul li:before {
    height: 20px;
  }

  .product-rating ul li span {
    font-size: 19px;
  }

  .product-rating ul li .fa-star {
    font-size: 19px;
    width: 20px;
  }
}

.open-close {
  margin-bottom: 45px;
}

.open-close ul {
  display: flex;
  margin-bottom: 0;
}

.open-close ul li {
  font-size: 15px;
  color: #8f8f8a;
  font-family: "Visby CF";
  font-weight: 700;
}

.open-close ul li.red-text {
  color: #ff0000;
}

.open-close ul li.green-text {
  color: #14a411;
}

.open-close ul li.green-text i {
  margin-right: 5px;
  position: relative;
  top: 1px;
}

.open-close ul li .fa-circle {
  font-size: 4px;
  top: -4px;
  position: relative;
  margin-left: 15px;
  margin-right: 15px;
}

@media screen and (max-width: 1500px) {
  .open-close {
    margin-bottom: 32px;
  }

  .open-close ul li {
    font-size: 12px;
  }

  .open-close ul li i {
    position: relative;
    top: 1px;
    margin-right: 3px;
  }

  .open-close ul li .fa-circle {
    top: -2px;
    font-size: 3px;
    margin-left: 12px;
    margin-right: 12px;
  }
}

@media screen and (max-width: 1330px) {
  .open-close {
    margin-bottom: 22px;
  }
}

.reviews-rating-main h6 {
  color: #000000;
  font-size: 30px;
  font-weight: 800;
  font-family: "Visby CF";
  margin-bottom: 30px;

  @media screen and (max-width: 1500px) {
    font-size: 22px;
    margin-bottom: 20px;
  }
}

.reviews-rating-detail {
  display: flex;
  margin-bottom: 30px;

  @media screen and (max-width: 1500px) {
    margin-bottom: 20px;
  }

  @media screen and (max-width: 575px) {
    .reviews-rating-detail {
      flex-wrap: wrap;
    }

    .reviews-show {
      width: 100%;
      margin-bottom: 20px;
    }

    .rating-show {
      padding: 0;
    }
  }
}

.reviews-show {
  width: 160px;
  text-align: center;
}

.reviews-show h4 {
  font-size: 60px;
  color: #000000;
  font-weight: 800;
  font-family: "Visby CF";
}

.reviews-show .reviews-list {
  display: flex;
  justify-content: center;
  margin-bottom: 5px;
}

.reviews-show .reviews-list li {
  margin: 0 1px;
}

.reviews-show .reviews-list li a {
  font-size: 25px;
  color: var(--primary);
  cursor: pointer;
}

.reviews-show .reviews-list li a i {
  color: var(--primary);
}

.reviews-show .reviews-list span {
  font-size: 15px;
  color: #8f8f8a;
  font-family: "Visby CF";
  font-weight: 700;
}

@media screen and (max-width: 1750px) {
  .reviews-show .reviews-list li a {
    font-size: 22px;
  }
}

@media screen and (max-width: 1500px) {
  .reviews-show h4 {
    font-size: 45px;
    line-height: 38px;
    margin-bottom: 10px;
  }

  .reviews-show .reviews-list li a {
    font-size: 18px;
  }

  .reviews-show .reviews-list {
    margin-bottom: 2px;
  }

  .reviews-show span {
    font-size: 12px;
    color: #8f8f8a;
    font-weight: 700;
  }
}

.rating-show {
  width: 100%;
  padding-left: 70px;

  @media screen and (max-width: 1750px) {
    padding-left: 40px;
  }

  @media screen and (max-width: 1500px) {
    padding-left: 45px;
  }
}

.progress-box {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.progress-box span {
  font-family: "Visby CF";
  font-size: 15px;
  font-weight: 700;
  padding-right: 25px;
  color: #000;
}

.progress-box .progress {
  width: 100%;
  border-radius: 10px;
  background-color: #f4f3f3;
}

.progress-box .progress .progress-bar {
  border-radius: 10px;
  background-color: var(--primary);
}

@media screen and (max-width: 1750px) {
  .progress-box .progress {
    height: 13px;
  }

  .progress-box span {
    font-size: 14px;
    padding-right: 15px;
    color: #000;
  }
}

@media screen and (max-width: 1500px) {
  .progress-box {
    margin-bottom: 1px;
  }

  .progress-box span {
    font-size: 12px;
    color: #000;
  }

  .progress-box .progress {
    height: 11px;
  }
}

// .grubz-loader {
//   position: relative !important;
//   overflow: hidden !important;
//   height: 150px !important;
//   width: 150px !important;
//   margin: auto !important;
//   margin-top: 50px !important;
// }

// .grubz-loader .circle {
//   width: 14px !important;
//   height: 14px !important;
//   position: absolute !important;
//   background: var(--primary) !important;
//   border-radius: 50% !important;
//   margin: -7px !important;
//   -webkit-animation: grubz 3s ease-in-out infinite -1.5s !important;
//   animation: grubz 3s ease-in-out infinite -1.5s !important;
// }

// .grubz-loader > div .circle:last-child {
//   -webkit-animation-delay: 0s !important;
//   animation-delay: 0s !important;
// }

// .grubz-loader > div {
//   position: absolute !important;
//   top: 50% !important;
//   left: 50% !important;
// }

// .grubz-loader > div:last-child {
//   -webkit-transform: rotate(90deg) !important;
//   -ms-transform: rotate(90deg) !important;
//   transform: rotate(90deg) !important;
// }

.product-box .product-content ul.rating-list {
  display: flex;
  margin-bottom: 5px;
}

.product-box .product-content ul.rating-list li {
  font-size: 16px;
  color: #8f8f8a;
  font-weight: 700;
  position: relative;
  padding-right: 10px;
  margin-right: 10px;
}

.product-box .product-content ul.rating-list li img {
  width: 13px;
  margin-left: 5px;
  margin-right: 5px;
  margin-top: -5px;
  display: inline-block;
}

.product-box .product-content ul.times-list li::before,
.product-box .product-content ul.rating-list li::before {
  position: absolute;
  content: "";
  width: 4px;
  height: 4px;
  border-radius: 50%;
  right: 0;
  top: 50%;
  transform: translate(0, -50%);
  background-color: #8f8f8a;
}

.product-box .product-content ul.rating-list li:last-child:before {
  display: none;
}

.rating-list .rating-box {
  padding-top: 15px;
  padding-bottom: 15px;
  border-top: 2px solid #f4f3f3;
}

.rating-list .rating-box p {
  font-size: 18px;
  color: #000000;
  font-weight: 600;
  font-family: "Visby CF";
  margin-bottom: 5px;
  word-wrap: break-word;
}

.rating-list .rating-box .rating-details {
  display: flex;
  align-items: center;
}

.rating-list .rating-box .rating-details span {
  color: #8f8f8a;
  font-size: 14px;
  font-weight: 700;
  font-family: "Visby CF";
}

.rating-list .rating-box .rating-details .rating-star {
  display: flex;
  margin-bottom: 0;
  margin-right: 12px;
}

.rating-list .rating-box .rating-details .rating-star li {
  margin-right: 2px;
}

.rating-list .rating-box .rating-details .rating-star li span {
  font-size: 14px;
  color: #000000;
}

.rating-list .rating-box .rating-details .fa-circle {
  font-size: 4px;
  color: #8f8f8a;
  margin-left: 12px;
  margin-right: 12px;
  top: 2px;
  position: relative;
}

@media screen and (max-width: 1750px) {
  .product-box .product-content ul.times-list li::before,
  .product-box .product-content ul.rating-list li::before {
    top: 55%;
    transform: translate(0, -55%);
  }
}

@media screen and (max-width: 1500px) {
  .product-box .product-content ul.rating-list li {
    font-size: 14px;
    padding-right: 10px;
    margin-right: 10px;
  }

  .product-box .product-content ul.rating-list li img {
    width: 11px;
    margin-left: 2px;
  }

  .product-box .product-content ul.rating-list li:last-child {
    margin-right: 0;
    padding-right: 0;
  }

  .product-box .product-content ul.rating-list li:before,
  .product-box .product-content ul.times-list li:before {
    width: 3px;
    height: 3px;
  }

  .rating-list .rating-box {
    padding-top: 12px;
    padding-bottom: 12px;
  }

  .rating-list .rating-box p {
    font-size: 14px;
  }

  .rating-list .rating-box .rating-details .rating-star {
    line-height: normal;
    margin-right: 8px;
  }

  .rating-list .rating-box .rating-details .rating-star li a {
    font-size: 10px;
  }

  .rating-list .rating-box .rating-details span {
    font-size: 10px;
    padding-top: 5px;
  }

  .rating-list .rating-box .rating-details .fa-circle {
    top: 4px;
    font-size: 3px;
    margin-left: 8px;
    margin-right: 8px;
  }
}

@media screen and (max-width: 1300px) {
  .product-box .product-content ul.rating-list li {
    font-size: 13px;
    padding-right: 8px;
    margin-right: 8px;
  }

  .product-box .product-content ul.rating-list li:last-child {
    padding-right: 0;
    margin-right: 0;
  }
}
