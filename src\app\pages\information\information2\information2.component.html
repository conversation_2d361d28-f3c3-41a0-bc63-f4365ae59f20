<div class="information2-container">
  <!-- Loading Spinner -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="spinner-border text-primary">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
  <!-- Main Content -->
  <div class="main-content" *ngIf="!isLoading && restaurant">

    <!-- Navigation Tabs -->
    <div class="info-navigation">
      <div class="container">
        <div class="nav-tabs-wrapper">
          <button class="nav-tab" [class.active]="activeTab === 'aboutSection'" (click)="setActiveTab('aboutSection')">
            <i class="fas fa-info-circle"></i>
            About
          </button>
          <button
            *ngIf="restaurant.alcahol_available || restaurant.alcahol_not_available || restaurant.alcahol_available || restaurant.soft_drink"
            class="nav-tab" [class.active]="activeTab === 'drinkSection'" (click)="setActiveTab('drinkSection')">
            <i class="fas fa-wine-bottle"></i>
            Drink Service
          </button>
          <button class="nav-tab" [class.active]="activeTab === 'locationSection'"
            (click)="setActiveTab('locationSection')">
            <i class="fas fa-map-marker-alt"></i>
            Location
          </button>
          <button class="nav-tab" [class.active]="activeTab === 'hoursSection'" (click)="setActiveTab('hoursSection')">
            <i class="fas fa-clock"></i>
            Hours
          </button>
        </div>
      </div>
    </div>

    <!-- Content Sections -->
    <div class="content-sections">
      <div class="container">
        <!-- About Section -->
        <div class="content-section" id="aboutSection">
          <div class="section-card">
            <div class="section-header">
              <h2>About {{ restaurant.restaurant_name }}</h2>
              <div class="section-icon">
                <i class="fas fa-utensils"></i>
              </div>
            </div>
            <div class="section-content">
              <p class="about-text" *ngIf="restaurant.restaurant_about">
                {{ restaurant.restaurant_about }}
              </p>
              <p *ngIf="!restaurant.restaurant_about">
                'Welcome to our restaurant! We serve delicious, fresh food made with the finest ingredients.'
              </p>

              <!-- Key Features -->
              <div class="features-grid">
                <div class="feature-item" *ngIf="restaurant.restaurant_delivery === 'Yes'">
                  <div class="feature-icon">
                    <i class="fas fa-truck"></i>
                  </div>
                  <div class="feature-content">
                    <h4>Free Delivery</h4>
                    <p>On orders over £{{ restaurant.minimum_order || 15 }}</p>
                  </div>
                </div>

                <div class="feature-item" *ngIf="restaurant.restaurant_pickup === 'Yes'">
                  <div class="feature-icon">
                    <i class="fas fa-shopping-bag"></i>
                  </div>
                  <div class="feature-content">
                    <h4>Pickup Available</h4>
                    <p>Quick and convenient</p>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="fas fa-leaf"></i>
                  </div>
                  <div class="feature-content">
                    <h4>Fresh Ingredients</h4>
                    <p>Quality guaranteed</p>
                  </div>
                </div>

                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="fas fa-award"></i>
                  </div>
                  <div class="feature-content">
                    <h4>Highly Rated</h4>
                    <p>{{ restaurant.average_rating }}/5 stars</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Drink Section -->
        <div class="content-section" id="drinkSection"
          *ngIf="restaurant.alcahol_available || restaurant.alcahol_not_available || restaurant.alcahol_available || restaurant.soft_drink">
          <div class="section-card">
            <div class="section-header">
              <h2>Drink Service</h2>
              <div class="section-icon">
                <i class="fas fa-wine-bottle"></i>
              </div>
            </div>
            <div class="section-content">
              <!-- Key Features -->
              <div class="features-grid">
                <div class="feature-item" *ngIf="restaurant.alcahol_available">
                  <div class="feature-icon bg-success">
                    <i class="fas fa-check"></i>
                  </div>
                  <div class="feature-content">
                    <h4>Alcohol Available</h4>
                  </div>
                </div>

                <div class="feature-item" *ngIf="restaurant.alcahol_not_available">
                  <div class="feature-icon bg-danger">
                    <i class="fas fa-x"></i>
                  </div>
                  <div class="feature-content">
                    <h4>Alcohol Not Allowed</h4>
                  </div>
                </div>

                <div class="feature-item" *ngIf="restaurant.bring_your_alcahol">
                  <div class="feature-icon bg-success">
                    <i class="fas fa-check"></i>
                  </div>
                  <div class="feature-content">
                    <h4>Bring Your Own Alcohol</h4>
                  </div>
                </div>

                <div class="feature-item" *ngIf="restaurant.soft_drink">
                  <div class="feature-icon bg-success">
                    <i class="fas fa-check"></i>
                  </div>
                  <div class="feature-content">
                    <h4>Soft Drinks Served</h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Location Section -->
        <div class="content-section" id="locationSection">
          <div class="section-card">
            <div class="section-header">
              <h2>Find Us</h2>
              <div class="section-icon">
                <i class="fas fa-map-marker-alt"></i>
              </div>
            </div>
            <div class="section-content">
              <div class="location-info">
                <div class="address-card">
                  <div class="address-icon">
                    <i class="fas fa-map-pin"></i>
                  </div>
                  <div class="address-details">
                    <h4>Our Address</h4>
                    <p>{{ restaurant.street_address }}</p>
                    <p>{{ restaurant.city ? restaurant.city + ', ' : '' }} {{ restaurant.postcode }}</p>
                  </div>
                </div>

                <!-- Map Container -->
                <div class="map-container" *ngIf="streetAddress">
                  <iframe [src]="streetAddress" class="location-map" frameborder="0" scrolling="no" marginheight="0"
                    marginwidth="0">
                  </iframe>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Hours Section -->
        <div class="content-section" id="hoursSection">
          <div class="section-card">
            <div class="section-header">
              <h2>Opening Hours</h2>
              <div class="section-icon">
                <i class="fas fa-clock"></i>
              </div>
            </div>
            <div class="section-content">
              <div class="row">
                <div class="col-12 col-lg-6">
                  <h3>Delivery Opening Hours</h3>
                  <div class="hours-grid">
                    <!-- Monday -->
                    <div class="hours-item">
                      <div class="day-name">Monday</div>
                      <div class="day-hours" *ngIf="restaurant.monday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.monday_first_opentime != '12:00 PM' || restaurant.monday_first_closetime != '12:00 PM'">
                            {{restaurant.monday_first_opentime}} - {{restaurant.monday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.monday_second_opentime != '12:00 PM' || restaurant.monday_second_closetime != '12:00 PM'">
                            {{restaurant.monday_second_opentime}} - {{restaurant.monday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.monday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                    <!-- Tuesday -->
                    <div class="hours-item">
                      <div class="day-name">Tuesday</div>
                      <div class="day-hours" *ngIf="restaurant.tuesday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.tuesday_first_opentime != '12:00 PM' || restaurant.tuesday_first_closetime != '12:00 PM'">
                            {{restaurant.tuesday_first_opentime}} - {{restaurant.tuesday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.tuesday_second_opentime != '12:00 PM' || restaurant.tuesday_second_closetime != '12:00 PM'">
                            {{restaurant.tuesday_second_opentime}} - {{restaurant.tuesday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.tuesday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                    <!-- Wednesday -->
                    <div class="hours-item">
                      <div class="day-name">Wednesday</div>
                      <div class="day-hours" *ngIf="restaurant.wednesday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.wednesday_first_opentime != '12:00 PM' || restaurant.wednesday_first_closetime != '12:00 PM'">
                            {{restaurant.wednesday_first_opentime}} - {{restaurant.wednesday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.wednesday_second_opentime != '12:00 PM' || restaurant.wednesday_second_closetime != '12:00 PM'">
                            {{restaurant.wednesday_second_opentime}} - {{restaurant.wednesday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.wednesday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                    <!-- Thursday -->
                    <div class="hours-item">
                      <div class="day-name">Thursday</div>
                      <div class="day-hours" *ngIf="restaurant.thursday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.thursday_first_opentime != '12:00 PM' || restaurant.thursday_first_closetime != '12:00 PM'">
                            {{restaurant.thursday_first_opentime}} - {{restaurant.thursday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.thursday_second_opentime != '12:00 PM' || restaurant.thursday_second_closetime != '12:00 PM'">
                            {{restaurant.thursday_second_opentime}} - {{restaurant.thursday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.thursday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                    <!-- Friday -->
                    <div class="hours-item">
                      <div class="day-name">Friday</div>
                      <div class="day-hours" *ngIf="restaurant.friday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.friday_first_opentime != '12:00 PM' || restaurant.friday_first_closetime != '12:00 PM'">
                            {{restaurant.friday_first_opentime}} - {{restaurant.friday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.friday_second_opentime != '12:00 PM' || restaurant.friday_second_closetime != '12:00 PM'">
                            {{restaurant.friday_second_opentime}} - {{restaurant.friday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.friday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                    <!-- Saturday -->
                    <div class="hours-item">
                      <div class="day-name">Saturday</div>
                      <div class="day-hours" *ngIf="restaurant.saturday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.saturday_first_opentime != '12:00 PM' || restaurant.saturday_first_closetime != '12:00 PM'">
                            {{restaurant.saturday_first_opentime}} - {{restaurant.saturday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.saturday_second_opentime != '12:00 PM' || restaurant.saturday_second_closetime != '12:00 PM'">
                            {{restaurant.saturday_second_opentime}} - {{restaurant.saturday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.saturday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                    <!-- Sunday -->
                    <div class="hours-item">
                      <div class="day-name">Sunday</div>
                      <div class="day-hours" *ngIf="restaurant.sunday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.sunday_first_opentime != '12:00 PM' || restaurant.sunday_first_closetime != '12:00 PM'">
                            {{restaurant.sunday_first_opentime}} - {{restaurant.sunday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.sunday_second_opentime != '12:00 PM' || restaurant.sunday_second_closetime != '12:00 PM'">
                            {{restaurant.sunday_second_opentime}} - {{restaurant.sunday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.sunday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-lg-6">
                  <h3>Pickup Opening Hours</h3>
                  <div class="hours-grid">
                    <!-- Monday -->
                    <div class="hours-item">
                      <div class="day-name">Monday</div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_monday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_monday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_monday_first_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_monday_first_opentime}} -
                            {{restaurant.restaurant_timing.pick_monday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_monday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_monday_second_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_monday_second_opentime}} -
                            {{restaurant.restaurant_timing.pick_monday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_monday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                    <!-- Tuesday -->
                    <div class="hours-item">
                      <div class="day-name">Tuesday</div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_tuesday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_tuesday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_tuesday_first_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_tuesday_first_opentime}} -
                            {{restaurant.restaurant_timing.pick_tuesday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_tuesday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_tuesday_second_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_tuesday_second_opentime}} -
                            {{restaurant.restaurant_timing.pick_tuesday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_tuesday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                    <!-- Wednesday -->
                    <div class="hours-item">
                      <div class="day-name">Wednesday</div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_wednesday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_wednesday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_wednesday_first_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_wednesday_first_opentime}} -
                            {{restaurant.restaurant_timing.pick_wednesday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_wednesday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_wednesday_second_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_wednesday_second_opentime}} -
                            {{restaurant.restaurant_timing.pick_wednesday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_wednesday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                    <!-- Thursday -->
                    <div class="hours-item">
                      <div class="day-name">Thursday</div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_thursday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_thursday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_thursday_first_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_thursday_first_opentime}} -
                            {{restaurant.restaurant_timing.pick_thursday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_thursday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_thursday_second_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_thursday_second_opentime}} -
                            {{restaurant.restaurant_timing.pick_thursday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_thursday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                    <!-- Friday -->
                    <div class="hours-item">
                      <div class="day-name">Friday</div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_friday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_friday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_friday_first_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_friday_first_opentime}} -
                            {{restaurant.restaurant_timing.pick_friday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_friday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_friday_second_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_friday_second_opentime}} -
                            {{restaurant.restaurant_timing.pick_friday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_friday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                    <!-- Saturday -->
                    <div class="hours-item">
                      <div class="day-name">Saturday</div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_saturday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_saturday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_saturday_first_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_saturday_first_opentime}} -
                            {{restaurant.restaurant_timing.pick_saturday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_saturday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_saturday_second_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_saturday_second_opentime}} -
                            {{restaurant.restaurant_timing.pick_saturday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_saturday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                    <!-- Sunday -->
                    <div class="hours-item">
                      <div class="day-name">Sunday</div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_sunday_status != 'Close'">
                        <div class="d-flex flex-column justify-content-end">
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_sunday_first_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_sunday_first_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_sunday_first_opentime}} -
                            {{restaurant.restaurant_timing.pick_sunday_first_closetime}}
                          </span>
                          <span
                            *ngIf="restaurant.restaurant_timing.pick_sunday_second_opentime != '12:00 PM' || restaurant.restaurant_timing.pick_sunday_second_closetime != '12:00 PM'">
                            {{restaurant.restaurant_timing.pick_sunday_second_opentime}} -
                            {{restaurant.restaurant_timing.pick_sunday_second_closetime}}
                          </span>
                        </div>
                      </div>
                      <div class="day-hours" *ngIf="restaurant.restaurant_timing.pick_sunday_status == 'Close'">
                        Closed
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="current-status">
                <div class="status-indicator" [class.open]="restaurant.currentStatus === 'Open'">
                  <i class="fas fa-circle"></i>
                </div>
                <span class="status-text">
                  Currently {{ restaurant.currentStatus || 'Closed' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>