<ng-container *ngIf="!userService.userThemeLoading; else themeLoading">
  <ng-container [ngSwitch]="menuTheme">
    <app-customer-details1 *ngSwitchCase="'theme1'"></app-customer-details1>
    <app-customer-details2 *ngSwitchCase="'theme2'"></app-customer-details2>
    <app-customer-details2 *ngSwitchCase="'theme3'"></app-customer-details2>

    <!-- Optional: fallback -->
    <div *ngSwitchDefault>
      <app-customer-details1></app-customer-details1>
    </div>
  </ng-container>
</ng-container>

<ng-template #themeLoading>
  <div class="d-flex justify-content-center align-items-center w-100 h-100" style="min-height: 80vh;">
    <div class="cart-loader">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  </div>
</ng-template>
