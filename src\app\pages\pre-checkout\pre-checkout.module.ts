import { NgModule } from '@angular/core';
import { PreCheckoutComponent } from './pre-checkout.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { PreCheckout2Component } from './pre-checkout2/pre-checkout2.component';
import { PreCheckoutThemeComponent } from './pre-checkout-theme/pre-checkout-theme.component';

const routes: Routes = [
  // { path: '', component: PreCheckoutComponent },
  { path: '', component: PreCheckoutThemeComponent },
];

@NgModule({
  declarations: [PreCheckoutComponent, PreCheckout2Component, PreCheckoutThemeComponent],
  imports: [RouterModule.forChild(routes), SharedModule, NgbModule],
})
export class PreCheckoutModule { }
