::ng-deep .rounded-modal > .modal-dialog > .modal-content {
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.address-details {
  background: #fff;
  border-radius: 1rem;
  overflow: auto;
  box-shadow: 0px 2px 5px rgba($color: #000000, $alpha: 0.1);
  padding: 1rem;
  margin-bottom: 1rem;

  .address-list {
    display: flex;
    flex-direction: column;
    row-gap: 0.5rem;
    padding: 1rem 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
  }

  .add-address-btn {
    width: 100%;
    background: var(--primary);
    color: white;
    border: none;
    padding: 8px 14px;
    border-radius: 2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    }
  }
}

.delivery-details {
  background: #fff;
  border-radius: 1rem;
  overflow: auto;
  box-shadow: 0px 2px 5px rgba($color: #000000, $alpha: 0.1);
  padding: 1rem;

  .choose-delivery-time {
    font-weight: 600;
    color: var(--primary);
    text-align: center;
    background: #f9f9f9;
    padding: 5px 0;
    border-radius: 1rem;
    border: 1px solid #eee;

    .delivery-time-select {
      width: 100%;
      cursor: pointer;
    }
  }
}

.order-details {
  background: #fff;
  border-radius: 1rem;
  overflow: auto;
  box-shadow: 0px 2px 5px rgba($color: #000000, $alpha: 0.1);
  padding: 1rem;

  .delivery-pickup-wrapper {
    display: flex;
    align-items: center;
    column-gap: 0.5rem;
    border-radius: 2rem;
    padding: 3px;
    background: #efefef;
    width: fit-content;
    margin: 0 auto;
    margin-bottom: 1rem;

    .delivery-btn,
    .pickup-btn {
      background: transparent;
      color: #000;
      border: none;
      padding: 0.25rem 2rem;
      border-radius: 2rem;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 600;

      transition: all 0.3s ease;

      &.active {
        background: var(--primary);
        color: #fff;
        border: 1px solid #fff;
      }
    }
  }

  .cart-items-list {
    max-height: calc(100dvh - 270px);
    overflow-y: auto;
    margin-bottom: 16px;
    padding: 0px 18px;

    @media screen and (max-width: 576px) {
      padding: 0px 0px;
      max-height: 100%;
    }

    @media screen and (max-width: 400px) {
      padding: 0px 0px;
      max-height: 100%;
    }

    .cart-item {
      display: flex;
      flex-direction: column;
      row-gap: 8px;
      padding: 8px 0;
      border-bottom: 1px solid #eee;
      margin-bottom: 1rem;

      &.suggested-item {
        // border-bottom: none;
        margin-bottom: 0;
      }

      &:last-child {
        border-bottom: none;
      }

      .item-name {
        flex: 1;
        font-weight: 500;
        color: #333;

        // display: -webkit-box;
        // -webkit-line-clamp: 2;
        // -webkit-box-orient: vertical;
        // overflow: hidden;
        // text-overflow: ellipsis;

        // /* Firefox fallback */
        // display: -moz-box;
        // -moz-box-orient: vertical;
        // -moz-line-clamp: 2;
      }

      .item-description {
        // display: -webkit-box;
        // -webkit-line-clamp: 2;
        // -webkit-box-orient: vertical;
        // overflow: hidden;
        // text-overflow: ellipsis;

        // /* Firefox fallback */
        // display: -moz-box;
        // -moz-box-orient: vertical;
        // -moz-line-clamp: 2;
      }

      .quantity-controls {
        display: flex;
        align-items: center;
        gap: 8px;

        button {
          width: 28px;
          height: 28px;
          border-radius: 50%;
          border: 1px solid #ddd;
          color: var(--primary);
          background: white;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          font-size: 12px;

          &:hover {
            background: #f5f5f5;
          }

          &.add-btn {
            color: #27ae60;
          }

          &.delete-btn {
            color: #ff0000;
          }
        }

        span {
          min-width: 20px;
          text-align: center;
          font-weight: 600;
        }
      }

      .item-total {
        font-weight: 600;
        color: var(--primary);
      }
    }

    .did-you-forget {
      font-weight: 600;
      color: #000;
      text-align: center;
      background: #f9f9f9;
      padding: 5px 0px;
      border-radius: 1rem;
      border: 1px solid #eee;
    }
  }

  .empty-cart-cls {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem 2rem;
    color: #6c757d;

    img {
      width: 60px;
      height: 45px;
      object-fit: contain;
    }

    .item-name {
      font-size: 1.2rem;
      font-weight: 600;
    }
  }

  .checkout-btn {
    width: 100%;
    background: var(--primary);
    color: white;
    border: none;
    padding: 8px 14px;
    border-radius: 2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    }

    &.disabled {
      background: #878787;
      cursor: not-allowed;
    }
  }
}

.address-modal {
  border-radius: 1rem;

  .address-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .address-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .address-modal-title {
      font-size: 1.5rem;
    }
  }

  .address-modal-close {
    background-color: var(--primary);
    color: #fff;
    padding: 0.75rem;
    border: none;
    border-radius: 2rem;
    font-size: 1rem;
    font-weight: 500;
    border: 1px solid #fff;
    cursor: pointer;

    &:hover {
      color: var(--primary);
      border: 1px solid var(--primary);
      background-color: #fff;
    }
  }
}
