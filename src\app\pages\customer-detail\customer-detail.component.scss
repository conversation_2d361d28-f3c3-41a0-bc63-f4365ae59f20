// @use 'style.scss'; // Path to _variables.scss Notice how we don't include the underscore or file extension

.menu-item {
  .menu-details {
    width: 100%;
  }

  .add-item {
    cursor: pointer;
  }
}

.cart-wrapper-fixed {
  position: fixed;
  z-index: 1;
  top: 70px !important;
}

.order-types {
  box-shadow: 0px 7px 9px 0px #3e3e3e21;
}

.footer-category {
  position: fixed;
  right: 12px;
  bottom: 10px;
  width: 220px;
  z-index: 12;
  max-height: 300px;
  letter-spacing: .3px;
  text-align: left;
  overflow-y: scroll;
}

.active {
  background-color: #ffffff;
  color: #ffffff;
}

.list-group-item .active {
  text-decoration: none !important;
  color: #000;
}

.list-group-item {
  background-color: #333;
  color: #fff;
  // border-bottom: 1px solid #fff;
  font-weight: 500;
  text-decoration: none !important;

  &.active {
    background-color: #fff;
    border-color: #333;
    color: #000;
    font-weight: 500;
    text-decoration: none !important;
  }

  a {
    color: #fff;
    font-weight: 500;
    text-decoration: none !important;
  }

  &.active a {
    color: #000;
    font-weight: 500;
    text-decoration: none !important;
  }
}

a {
  text-decoration: none !important;
}
