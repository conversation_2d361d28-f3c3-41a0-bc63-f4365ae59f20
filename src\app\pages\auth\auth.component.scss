.grubz-loader {
  position: relative !important;
  overflow: hidden !important;
  height: 150px !important;
  width: 150px !important;
  margin: auto !important;
  margin-top: 50px !important;
}

.grubz-loader .circle {
  width: 14px !important;
  height: 14px !important;
  position: absolute !important;
  background: var(--primary) !important;
  border-radius: 50% !important;
  margin: -7px !important;
  -webkit-animation: grubz 3s ease-in-out infinite -1.5s !important;
  animation: grubz 3s ease-in-out infinite -1.5s !important;
}

.grubz-loader > div .circle:last-child {
  -webkit-animation-delay: 0s !important;
  animation-delay: 0s !important;
}

.grubz-loader > div {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
}

.grubz-loader > div:last-child {
  -webkit-transform: rotate(90deg) !important;
  -ms-transform: rotate(90deg) !important;
  transform: rotate(90deg) !important;
}

@-webkit-keyframes grubz {
  0% {
    -webkit-transform-origin: 50% -100%;
    transform-origin: 50% -100%;
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  50% {
    -webkit-transform-origin: 50% -100%;
    transform-origin: 50% -100%;
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }

  50.1% {
    -webkit-transform-origin: 50% 200%;
    transform-origin: 50% 200%;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform-origin: 50% 200%;
    transform-origin: 50% 200%;
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes grubz {
  0% {
    -webkit-transform-origin: 50% -100%;
    transform-origin: 50% -100%;
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  50% {
    -webkit-transform-origin: 50% -100%;
    transform-origin: 50% -100%;
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }

  50.1% {
    -webkit-transform-origin: 50% 200%;
    transform-origin: 50% 200%;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform-origin: 50% 200%;
    transform-origin: 50% 200%;
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes buzz-out-on-hover {
  10% {
    -webkit-transform: translateX(3px) rotate(2deg);
    transform: translateX(3px) rotate(2deg);
  }

  20% {
    -webkit-transform: translateX(-3px) rotate(-2deg);
    transform: translateX(-3px) rotate(-2deg);
  }

  30% {
    -webkit-transform: translateX(3px) rotate(2deg);
    transform: translateX(3px) rotate(2deg);
  }

  40% {
    -webkit-transform: translateX(-3px) rotate(-2deg);
    transform: translateX(-3px) rotate(-2deg);
  }

  50% {
    -webkit-transform: translateX(2px) rotate(1deg);
    transform: translateX(2px) rotate(1deg);
  }

  60% {
    -webkit-transform: translateX(-2px) rotate(-1deg);
    transform: translateX(-2px) rotate(-1deg);
  }

  70% {
    -webkit-transform: translateX(2px) rotate(1deg);
    transform: translateX(2px) rotate(1deg);
  }

  80% {
    -webkit-transform: translateX(-2px) rotate(-1deg);
    transform: translateX(-2px) rotate(-1deg);
  }

  90% {
    -webkit-transform: translateX(1px) rotate(0);
    transform: translateX(1px) rotate(0);
  }

  100% {
    -webkit-transform: translateX(-1px) rotate(0);
    transform: translateX(-1px) rotate(0);
  }
}

.blink-animation {
  font-size: 14px;
  color: var(--primary);
  animation: blinker 6s linear infinite;
  font-family: "Fredoka";
  font-weight: 500;
  text-align: center;
  display: inline-block;
  width: 100%;
}

@keyframes blinker {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.loader-bg {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgb(0, 0, 0, 0.8);
  z-index: 900;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader-bg .grubz-loader {
  position: initial !important;
}
