import { Component, OnInit } from '@angular/core';
import { UserService } from 'src/app/core/services/user.service';

@Component({
  selector: 'app-checkout-theme',
  templateUrl: './checkout-theme.component.html',
  styleUrls: ['./checkout-theme.component.scss']
})
export class CheckoutThemeComponent implements OnInit {
  menuTheme: string;

  constructor(public userService: UserService) { }

  ngOnInit(): void {
    this.userService.getUserTheme().subscribe(res => {
      this.menuTheme = res;
    })
  }
}
