<div class="container mt-2 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class=" row" *ngIf="!isLoading">
    <div class="col-12">
      <div class="d-flex flex-column">
        <!-- sticky-responsive -->
        <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="order.status == 'Pending'">
          Your order has been placed
        </h3>
        <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="order.status == 'Pending'">
          Waiting for restaurant to accept
        </h3>
        <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="order.status == 'Accepted'">
          Your order preparation time
        </h3>
        <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="order.status == 'Delivered'">
          Your order has been Delivered
        </h3>
        <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="order.status == 'Collected'">
          Your order has been collected
        </h3>
        <h3 *ngIf="order.status == 'Waiting' || order.status == 'Driver Accepted'"
          class="col-sm-12 col-xs-12 text-center">
          Your order preparation time
        </h3>
        <h3 class="col-sm-12 col-xs-12 text-center" *ngIf="order.status == 'Failed'">
          Your order has been rejected
        </h3>

        <div class="center">
          <ng-lottie width="300px" height="150px" [options]="optionPending" loop autoplay
            *ngIf="order.status == 'Pending'" containerClass="moving-box another-class">
          </ng-lottie>
          <ng-lottie width="300px" height="150px" [options]="optionAccepted" loop autoplay
            *ngIf="order.status == 'Accepted'" containerClass="moving-box another-class">
          </ng-lottie>
          <ng-lottie width="300px" height="150px" [options]="optionDelivered" loop autoplay
            *ngIf="order.status == 'Delivered'" containerClass="moving-box another-class">
          </ng-lottie>
          <ng-lottie width="300px" height="150px" [options]="optionCollected" loop autoplay
            *ngIf="order.status == 'Collected'" containerClass="moving-box another-class">
          </ng-lottie>
          <ng-lottie width="300px" height="150px" [options]="optionWaiting" loop autoplay
            *ngIf="order.status == 'Waiting' || order.status == 'Driver Accepted'"
            containerClass="moving-box another-class">
          </ng-lottie>
          <ng-lottie width="300px" height="150px" [options]="optionFailed" loop autoplay
            *ngIf="order.status == 'Failed'" containerClass="moving-box another-class">
          </ng-lottie>
        </div>

        <div *ngIf="order.status == 'Accepted'" class="col-xs-12 col-sm-12 align-items-center text-center">
          <div *ngIf="daysToDday > 0 || hoursToDday > 0 || minutesToDday > 0 || secondsToDday > 0"
            class="p-2 d-flex justify-content-center">
            <span class="bg-white body-box-shadow rounded h-100 p-2 m-2">
              {{daysToDday}}
              <p class="m-0">Day</p>
            </span>
            <span class="bg-white body-box-shadow rounded h-100 p-2 m-2">
              {{hoursToDday}}
              <p class="m-0">Hours</p>
            </span>
            <span class="bg-white body-box-shadow rounded h-100 p-2 m-2">
              {{minutesToDday}}
              <p class="m-0">Minutes</p>
            </span>
            <span class="bg-white body-box-shadow rounded h-100 p-2 m-2">
              {{secondsToDday}}
              <p class="m-0">Seconds</p>
            </span>
          </div>
        </div>

        <div *ngIf="order.status != 'Failed'" class="stepper d-flex align-items-center justify-content-between py-4">
          <ng-container *ngFor="let status of orderStatuses">
            <div *ngIf="status.order_type=='all' || status.order_type==order.order_type"
              class="flex-fill text-center strikethrough">
              <div class="status d-flex flex-column align-items-center" [class.active]="status.checked">
                <div class="dot text-center" [class.active]="status.checked"></div>
                <div class="title" [class.active]="status.checked">{{status.title}}</div>
              </div>
            </div>
          </ng-container>
        </div>

        <div *ngIf="order.status == 'Failed'" class="stepper d-flex align-items-center justify-content-between py-4">
          <ng-container *ngFor="let status of orderRejectStatuses">
            <div *ngIf="status.order_type=='all' || status.order_type==order.order_type"
              class="flex-fill text-center strikethrough">
              <div class="status d-flex flex-column align-items-center" [class.active]="status.checked">
                <div class="dot text-center" [class.active]="status.checked"></div>
                <div class="title" [class.active]="status.checked">{{status.title}}</div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>

    <div class="col-12 col-sm-6 mt-2">
      <div class="d-flex flex-column">
        <div class="col-12">
          <!-- Pending Status -->
          <div class="status-message-card pending" *ngIf="order.status == 'Pending'">
            <div class="message-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="message-content">
              <h5 class="message-title">Order Placed Successfully!</h5>
              <p class="message-text m-0">
                <!-- Thank you, your order have been successfully placed.
                Please wait for the restaurant to confirm your order and delivery time. -->
                Thank you for your order. We're waiting for {{restaurant.restaurant_name}} to confirm your order and
                delivery time.
              </p>
            </div>
          </div>

          <!-- Until Not Delivered Status -->
          <div class="status-message-card pending" *ngIf="order.status != 'Delivered'">
            <div class="message-icon">
              <i class="fas fa-envelope"></i>
            </div>
            <div class="message-content">
              <h5 class="message-title">Verify Your Contact Details</h5>
              <p class="message-text m-0">
                In order to receive updates on your order via email or SMS, please verify your email and phone number.
                This can be done on the profile section.
                <br>
                Please contact the restaurant if you do not receive any notifications once verified.
              </p>
            </div>
          </div>

          <!-- Accepted Status -->
          <div class="status-message-card accepted" *ngIf="order.status == 'Accepted'">
            <div class="message-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="message-content">
              <h5 class="message-title">Order Accepted!</h5>
              <p class="message-text m-0">
                <!-- Your order has been accepted, pleased wait while your food is being prepared. -->
                Great news! {{restaurant.restaurant_name}} has accepted your order and is now preparing your delicious
                meal.
              </p>
            </div>
          </div>

          <!-- Delivered Status -->
          <div class="status-message-card delivered" *ngIf="order.status == 'Delivered'">
            <div class="message-icon">
              <i class="fas fa-check-double"></i>
            </div>
            <div class="message-content">
              <h5 class="message-title">Order Delivered!</h5>
              <p class="message-text m-0">
                <!-- Enjoy your meal. Remember to leave a review once your finished. -->
                Enjoy your meal! We hope you love every bite. Don't forget to share your experience with a review.
              </p>
            </div>
          </div>

          <!-- Collected Status -->
          <div class="status-message-card collected" *ngIf="order.status == 'Collected'">
            <div class="message-icon">
              <i class="fas fa-shipping-fast"></i>
            </div>
            <div class="message-content">
              <h5 class="message-title">Order On The Way!</h5>
              <p class="message-text m-0">
                <!-- Sit tight, your meal is on its way to you. -->
                Sit tight! Your delicious meal has been collected and is now on its way to you.
              </p>
            </div>
          </div>

          <!-- Waiting/Driver Accepted Status -->
          <div *ngIf="order.status == 'Waiting' || order.status == 'Driver Accepted'"
            class="status-message-card cooking">
            <div class="message-icon">
              <i class="fas fa-utensils"></i>
            </div>
            <div class="message-content">
              <h5 class="message-title">Cooking in Progress!</h5>
              <p class="message-text m-0">
                <!-- Your meal is being cooked up by the chef. -->
                Our talented chefs are working their magic to prepare your meal with love and care.
              </p>
            </div>
          </div>

          <!-- Failed Status -->
          <div class="status-message-card failed" *ngIf="order.status == 'Failed'">
            <div class="message-icon">
              <i class="fas fa-times-circle"></i>
            </div>
            <div class="message-content">
              <h5 class="message-title">Order Could Not Be Processed</h5>
              <p class="message-text m-0">
                <!-- Sorry, but your order from {{restaurant.restaurant_name}} cannot be accepted at the moment. -->
                We're sorry, but {{restaurant.restaurant_name}} cannot accept your order at this time.
              </p>
            </div>
          </div>
        </div>

        <div class="col-12 mt-2">
          <div class="bg-white shadow-sm p-4" style="border-radius: 1rem;">
            <h5 class="text-center fw-semibold mb-4 border-bottom pb-2">Order Info</h5>

            <div class="row gy-3">
              <div class="col-12 d-flex">
                <div class="text-secondary col-5">Order ID</div>
                <div class="fw-bold col-7">#{{order.order_number}}</div>
              </div>

              <div class="col-12 d-flex">
                <div class="text-secondary col-5">Restaurant</div>
                <div class="fw-bold col-7">{{restaurant.restaurant_name}}</div>
              </div>

              <div class="col-12 d-flex">
                <div class="text-secondary col-5">Order Type</div>
                <div class="fw-bold col-7">{{order.order_type | titlecase}}</div>
              </div>

              <div class="col-12 d-flex" *ngIf="order.address && order.order_type == 'delivery'">
                <div class="text-secondary col-5">Address</div>
                <div class="fw-bold col-7">{{order.address}}</div>
              </div>

              <div class="col-12 d-flex">
                <div class="text-secondary col-5">Delivery Time</div>
                <div class="fw-bold col-7">{{order.delivery_date}} {{order.delivery_time}}</div>
              </div>

              <div class="col-12 d-flex">
                <div class="text-secondary col-5">Order At</div>
                <div class="fw-bold col-7">{{convertToDate(order.created)}}</div>
              </div>

              <div class="col-12 d-flex">
                <div class="text-secondary col-5">Status</div>
                <div class="fw-bold col-7">
                  {{order.status == 'Failed'
                  ? order.status == 'Delivered' && order.order_type != 'delivery'
                  ? 'Pickedup'
                  : order.status
                  : order.status}}
                </div>
              </div>

              <div class="col-12 d-flex" *ngIf="order.status == 'Failed'">
                <div class="text-secondary col-5">Rejected Reason</div>
                <div class="fw-bold col-7">{{order.failed_reason}}</div>
              </div>

              <div class="col-12 d-flex" *ngIf="order.order_description">
                <div class="text-secondary col-5">Instruction</div>
                <div class="fw-bold col-7">{{order.order_description}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-12 col-sm-6">
      <div class="row">
        <!-- <div class="col-12 mt-2">
          <div class="bg-white shadow-sm p-4" style="border-radius: 1rem;">
            <h5 class="text-center fw-semibold mb-4 border-bottom pb-2">Order Info</h5>

            <div class="row gy-3">
              <div class="col-12 d-flex">
                <div class="text-secondary col-5">Order ID</div>
                <div class="fw-bold col-7">#{{order.order_number}}</div>
              </div>

              <div class="col-12 d-flex">
                <div class="text-secondary col-5">Restaurant</div>
                <div class="fw-bold col-7">{{restaurant.restaurant_name}}</div>
              </div>

              <div class="col-12 d-flex">
                <div class="text-secondary col-5">Order Type</div>
                <div class="fw-bold col-7">{{order.order_type | titlecase}}</div>
              </div>

              <div class="col-12 d-flex" *ngIf="order.address && order.order_type == 'delivery'">
                <div class="text-secondary col-5">Address</div>
                <div class="fw-bold col-7">{{order.address}}</div>
              </div>

              <div class="col-12 d-flex">
                <div class="text-secondary col-5">Delivery Time</div>
                <div class="fw-bold col-7">{{order.delivery_date}} {{order.delivery_time}}</div>
              </div>

              <div class="col-12 d-flex">
                <div class="text-secondary col-5">Order At</div>
                <div class="fw-bold col-7">{{convertToDate(order.created)}}</div>
              </div>

              <div class="col-12 d-flex">
                <div class="text-secondary col-5">Status</div>
                <div class="fw-bold col-7">
                  {{order.status == 'Failed'
                  ? order.status == 'Delivered' && order.order_type != 'delivery'
                  ? 'Pickedup'
                  : order.status
                  : order.status}}
                </div>
              </div>

              <div class="col-12 d-flex" *ngIf="order.status == 'Failed'">
                <div class="text-secondary col-5">Rejected Reason</div>
                <div class="fw-bold col-7">{{order.failed_reason}}</div>
              </div>

              <div class="col-12 d-flex" *ngIf="order.order_description">
                <div class="text-secondary col-5">Instruction</div>
                <div class="fw-bold col-7">{{order.order_description}}</div>
              </div>
            </div>
          </div>
        </div> -->

        <div class="col-12 mt-2">
          <div class="bg-white body-box-shadow p-2" style="border-radius: 1rem;">
            <div class="cart-items-list text-center">
              <div class="card shadow-sm rounded-4 pb-2">
                <!-- Header -->
                <div class="row fw-bold border-bottom p-2 pb-2 mb-2 mx-0 bg-light"
                  style="border-radius: 1rem 1rem 0 0;">
                  <div class="col-6 col-md-7 text-start">Item Name</div>
                  <div class="col-2 col-md-2 text-center">Qty</div>
                  <div class="col-4 col-md-3 text-end">Total</div>
                </div>
                <!-- Cart Items -->
                <ng-container *ngFor="let cart of order.cart_view; let i=index;let isLast = last">
                  <div class="row px-0 py-2 px-md-2 mx-0" [ngClass]="{ 'border-bottom': !isLast }">
                    <div class="col-6 col-md-7 text-start">
                      <div class="w-100 ml-1 fw-bold text-primary menu-name-truncate text-start">
                        {{ cart?.menu_name }}
                      </div>
                      <div class="w-100 menu-description-truncate text-start" style="font-size: 12px; color: #999;">
                        {{ cart?.subaddons_name }}
                      </div>
                    </div>
                    <div class="col-2 col-md-2 text-center fw-bold text-primary">{{ cart.quantity }}</div>
                    <div class="col-4 col-md-3 text-end fw-bold text-primary">
                      {{ convertNumber(cart.total_price) }}
                    </div>
                  </div>
                </ng-container>
                <!-- Offer Items -->
                <ng-container *ngIf="order?.applied_offers?.length > 0">
                  <div class="row fw-bold border-bottom p-2 pb-2 mb-2 mx-0 text-center text-primary bg-light">
                    <div class="col-12">Offer Items</div>
                  </div>
                  <ng-container *ngFor="let appliedOffer of order.applied_offers; let i=index;let isLast = last">
                    <div class="row align-items-center px-0 py-2 px-md-2 mx-0" [ngClass]="{ 'border-bottom': !isLast }">
                      <div class="col-6 col-md-7">
                        <div class="w-100 ml-1 fw-bold text-primary menu-name-truncate">{{ appliedOffer?.menu_name }}
                        </div>
                        <div class="w-100 menu-description-truncate" style="font-size: 12px; color: #999;">
                          {{ appliedOffer?.subaddons_name }}
                        </div>
                      </div>
                      <div class="col-2 col-md-2 text-center fw-bold text-primary">{{ appliedOffer?.quantity }}</div>
                      <div class="col-4 col-md-3 text-end fw-bold text-primary">
                        {{appliedOffer?.total_price==0?'Free':convertNumber(appliedOffer?.total_price)}}
                      </div>
                    </div>
                  </ng-container>
                </ng-container>
              </div>

              <!-- Sub Total -->
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2 pt-3">
                <div class="col-8 text-start">Sub Total:</div>
                <div class="col-4 text-end">{{convertNumber(order.order_sub_total)}}</div>
              </div>

              <!-- Surcharge -->
              <ng-container *ngIf="order?.surcharges?.length > 0">
                <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                  <ng-container *ngFor="let surcharge of order.surcharges">
                    <div class="col-8 text-start">{{ surcharge.surcharge_name }}</div>
                    <div class="col-4 text-end">{{ convertNumber(surcharge.surcharge_amount) }}</div>
                  </ng-container>
                </div>
              </ng-container>

              <!-- Service Charge -->
              <ng-container *ngIf="order.service_charge > 0">
                <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                  <div class="col-8 text-start">Service Charge:</div>
                  <div class="col-4 text-end">{{ convertNumber(order.service_charge) }}</div>
                </div>
              </ng-container>

              <!-- Delivery Charge -->
              <ng-container *ngIf="order.order_type == 'delivery' && order.delivery_charge > 0">
                <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                  <div class="col-8 text-start">Delivery Charge:</div>
                  <div class="col-4 text-end">{{ convertNumber(order.delivery_charge) }}</div>
                </div>
              </ng-container>

              <!-- Voucher -->
              <ng-container *ngIf="order.voucher_code && order.voucher_amount > 0">
                <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                  <span class="col-9 text-start">Voucher ({{order.voucher_code}})</span>
                  <span class="col-3 text-end cursor">
                    (-) {{convertNumber(order.voucher_amount)}}
                  </span>
                </div>
              </ng-container>

              <!-- Offer Amount -->
              <ng-container *ngIf="order.offer_amount > 0">
                <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                  <div class="col-8 text-start">
                    Offer {{order.offer_percentage?'('+order.offer_percentage+'%)':''}}
                  </div>
                  <div class="col-4 text-end">(-) {{convertNumber(order.offer_amount)}}</div>
                </div>
              </ng-container>

              <!-- Charity Amount -->
              <ng-container *ngIf="order.charity_amount > 0">
                <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                  <div class="col-8 text-start">
                    Charity {{order.charity_message}}
                  </div>
                  <div class="col-4 text-end">{{convertNumber(order.charity_amount)}}</div>
                </div>
              </ng-container>

              <!-- Reward Amount -->
              <ng-container *ngIf="order.reward_offer > 0 && order.reward_used == 'Y'">
                <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                  <div class="col-8 text-start">
                    Redeem Offer {{order.reward_offer_percentage?'('+order.reward_offer_percentage+'%)':''}}
                  </div>
                  <div class="col-4 text-end">(-) {{convertNumber(order.reward_offer)}}</div>
                </div>
              </ng-container>

              <!-- Driver Tip -->
              <ng-container *ngIf="order.driver_tip > 0">
                <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                  <div class="col-8 text-start">
                    Driver Tip
                  </div>
                  <div class="col-4 text-end">{{convertNumber(order.driver_tip)}}</div>
                </div>
              </ng-container>

              <!-- Driver Tip -->
              <ng-container *ngIf="order.wallet_amount > 0">
                <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                  <div class="col-8 text-start">
                    <img src="./assets/wallet.png" class="px-2">
                    My Wallet
                  </div>
                  <div class="col-4 text-end">(-) {{convertNumber(order.wallet_amount)}}</div>
                </div>
              </ng-container>

              <!-- Total Amount -->
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                <div class="col-8 text-start">Total Amount:</div>
                <div class="col-4 text-end">
                  {{convertNumber(order.order_grand_total + order.charity_amount)}}
                </div>
              </div>

              <!-- Paid Via -->
              <ng-container *ngIf="order.split_payment == 'Yes'">
                <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                  <div class="col-8 text-start">
                    Paid via Wallet
                    {{ getPaidVia() }}
                  </div>
                  <div class="col-4 text-end">
                    <img *ngIf="order.payment_method =='paypal'" src="./assets/payment-logo/paypal-small-logo.png"
                      alt="paypal tiffintom">
                    <img *ngIf="order.payment_method == 'cod'" src="./assets/payment-logo/cod2.png"
                      alt="paypal tiffintom">
                    <span *ngIf="order.payment_method == 'Stripe'"> XXXX-{{order.card_view?.card_number}}</span>
                  </div>
                </div>
              </ng-container>
              <ng-container *ngIf="order.split_payment == 'No'">
                <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2">
                  <div class="col-8 text-start">
                    <span class="col-9 text-start" *ngIf="order.payment_method != 'cod'">
                      Paid via
                      {{ getPaidViaNo() }}
                    </span>
                    <span class="col-9 text-start" *ngIf="order.payment_method == 'cod'">Payment Method</span>
                  </div>
                  <div class="col-4 text-end">
                    <img *ngIf="order.payment_method == 'paypal'" src="./assets/payment-logo/paypal-small-logo.png"
                      alt="paypal tiffintom">
                    <span *ngIf="order.payment_method == 'cod'">cash on delivery</span>
                    <span *ngIf="order.payment_method == 'Stripe'"> XXXX-{{order.card_view?.card_number}}</span>
                  </div>
                </div>
              </ng-container>

              <!-- Payment Status -->
              <div class="row fw-bold mx-0 p-0 p-md-2 pb-2 pb-md-2" *ngIf="order.payment_method == 'cod'">
                <div class="col-8 text-start">Payment Status:</div>
                <div class="col-4 text-end">
                  Unpaid
                </div>
              </div>

              <div class="col-sm-12 empty-cart-cls text-center" *ngIf="order?.cart_view?.length == 0">
                <img src="assets/cartitem.png" class="img-fluid mb-4 mr-3">
                <p><strong>No Item(s) Added</strong></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-12 my-4 text-center align-items-center">
      <a class="back-home-btn" routerLink="/menu">
        Back To Homepage
      </a>
    </div>
  </div>