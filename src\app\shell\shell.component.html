<div class="top-header2">
  <div class="container">
    <nav class="navbar navbar-expand-lg navbar-light" style="padding: 0px;">
      <a class="navbar-brand" routerLink="/menu"><img src="assets/logo.png"></a>
      <button class="navbar-toggler" type="button" data-toggle="collapse" (click)="isCollapsed = !isCollapsed"
        data-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false"
        aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNavDropdown" [ngClass]="{'show':isCollapsed}"
        style="justify-content: flex-end;">
        <ul class="navbar-nav">
          <li class="nav-item"><a class="nav-link fw-bold text-primary" href="./index.html">Home</a></li>
          <li class="nav-item"><a class="nav-link fw-bold text-primary" href="./about-us.html">About Us</a>
          </li>
          <li class="nav-item"><a class="nav-link fw-bold text-primary" (click)="isCollapsed = false"
              routerLink="/menu">Menu</a></li>
          <li class="nav-item" *ngIf="this.userService.user?.id"><a class="nav-link fw-bold text-primary"
              (click)="isCollapsed = false" routerLink="/booking">Booking</a>
          </li>
          <li class="nav-item"><a class="nav-link fw-bold text-primary" (click)="isCollapsed = false"
              routerLink="/review">Review</a>
          </li>
          <li class="nav-item"><a class="nav-link fw-bold text-primary" (click)="isCollapsed = false"
              routerLink="/info">Info</a>
          </li>
          <li class="nav-item"><a class="nav-link fw-bold text-primary" href="./gallery.html">Gallery</a></li>
          <li class="nav-item"><a class="nav-link fw-bold text-primary" href="./contact-us.html">Contact</a></li>
          <li class="nav-item" *ngIf="!this.userService.user?.id"><a class="nav-link fw-bold text-primary"
              (click)="isCollapsed = false" (click)="isCollapsed = false" routerLink="/auth" role="button">Login</a>
          </li>
          <li class="nav-item" *ngIf="this.userService.user?.id"><a class="nav-link fw-bold text-primary"
              (click)="isCollapsed = false" routerLink="/customer-detail">My Account</a>
          </li>
          <li class="nav-item" *ngIf="this.userService.user?.id"><a class="nav-link fw-bold text-primary"
              (click)="isCollapsed = false" routerLink="/customer-detail">Hi,
              {{this.userService.user?.first_name}}</a></li>
          <li class="nav-item" *ngIf="this.userService.user?.id"><a class="nav-link fw-bold text-primary"
              (click)="isCollapsed = false" (click)="logout()" role="button">Logout</a>
          </li>
        </ul>
      </div>
    </nav>
  </div>
</div>

<div style="height:100%;min-height: calc(100vh - 142px);" oncontextmenu="return false">
  <router-outlet></router-outlet>
</div>

<div id="sticky-footer" class="flex-shrink-0 py-3 bg-primary text-white d-none d-lg-block">
  <div class="container">
    &copy; Copyrights <b><a href="https://www.gogrubz.com" target="_blank"
        class="text-light text-decoration-none">Gogrubz</a></b> All
    Rights Reserved.
    <span style="float:right;">
      Follow us
      <a href="https://www.facebook.com/tiffintom/" class="px-1" target="_blank"><img src="./assets/facebook.png"></a>
      <a href="https://twitter.com/TiffinTomUK" class="px-1" target="_blank"><img src="./assets/twitter.png"></a>
      <a href="https://www.instagram.com/tiffintomuk/" class="px-1" target="_blank"><img
          src="./assets/instagram.png"></a>
    </span>
  </div>
</div>