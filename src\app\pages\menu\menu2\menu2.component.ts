import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DOCUMENT } from '@angular/common';
import { Component, ElementRef, HostListener, Inject, OnInit, ViewChild } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { ModalDismissReasons, NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import { finalize } from 'rxjs/operators';
import { Cart } from 'src/app/core/models/cart';
import { Category } from 'src/app/core/models/category';
import { Menu } from 'src/app/core/models/menu';
import { Order } from 'src/app/core/models/order';
import { Restaurant } from 'src/app/core/models/restaurant';
import { User } from 'src/app/core/models/user';
import { Variant } from 'src/app/core/models/variant';
import { CartService } from 'src/app/core/services/cart.service';
import { CategoryService } from 'src/app/core/services/category.service';
import { LoaderService } from 'src/app/core/services/loader.service';
import { MenuService } from 'src/app/core/services/menu.service';
import { MessagingService } from 'src/app/core/services/messaging.service';
import { NotificationService } from 'src/app/core/services/notification.service';
import { RestaurantService } from 'src/app/core/services/restaurant.service';
import { UserService } from 'src/app/core/services/user.service';
import { environment } from 'src/environments/environment';


@Component({
  selector: 'app-menu2',
  templateUrl: './menu2.component.html',
  styleUrls: ['./menu2.component.scss']
})
export class Menu2Component implements OnInit {
  @ViewChild('allergyModal', { static: true }) allergyModal: ElementRef;
  @ViewChild('itemNotAvailableModal', { static: true }) itemNotAvailableModal: ElementRef;
  @ViewChild('cartPanel') cartPanel!: ElementRef<HTMLDivElement>;
  @ViewChild('otpModal', { static: true }) otpModal: ElementRef;

  showMenu = false;
  showCart = false;
  viewMode: 'table' | 'grid' = 'table';

  userId: string;
  verifyOtp: string;
  restaurant_id: string;
  selectedCategoryId: string;
  expandableCategoryId: string;
  expandableMainAddonId: string;
  Modelotperror = null;

  isModelLoading = false;
  hasDayMismatch = false;
  mobile: boolean = false;
  isMobile: boolean = false;
  hasOrderTypeMismatch = false;
  showCategory: boolean = false;
  isCartLoading = false;
  isRestaurantLoading = false;
  isCategoryLoading = false;
  isLoading = false;
  phoneTab = false;
  isModelOtpLoading = false;

  user: User;
  variantMenu: Menu;
  selectedMenu: Menu;
  selectedVariant: Variant;

  order: Order = new Order();
  restaurant: Restaurant = new Restaurant();
  selectedCategory: Category = new Category();

  carts: Cart[] = [];
  categories: Category[] = [];
  originalCategories: Category[] = [];

  modalOptions: NgbModalOptions;

  // New variables for variant selection modal
  selectedVariantId: string;
  quantity: number = 1;

  @ViewChild('menuItems', { static: false }) menuItems!: ElementRef;

  constructor(
    private router: Router,
    private modalService: NgbModal,
    public userService: UserService,
    private cartService: CartService,
    private menuService: MenuService,
    private currencyPipe: CurrencyPipe,
    private categoryService: CategoryService,
    private restaurantService: RestaurantService,
    @Inject(DOCUMENT) private document: Document,
    private loaderService: LoaderService,
    private metaTagService: Meta,
    private titleService: Title,
    private notificationService: NotificationService,
    private messagingService: MessagingService,
  ) { }

  ngOnInit(): void {
    if (window.screen.width <= 820) {
      this.mobile = true;
    }
    this.modalOptions = {
      backdrop: 'static',
      backdropClass: 'customBackdrop',
      windowClass: 'rounded-modal'
    };
    this.isMobile = window.innerWidth <= 992;
    this.restaurant_id = environment.googleFirebase;
    this.order.order_type = this.cartService.getOrderType();
    let user = JSON.parse(this.userService.getUser());
    this.userId = user?.id
    if (this.userId) {
      this.fetchCarts();
      this.user = user
    }
    this.fetchRestaurant();
    this.fetchCategories();
  }

  private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return `with: ${reason}`;
    }
  }

  @HostListener('window:scroll', ['$event']) onWindowScroll(): void {
    const scrollPosition = window.scrollY;
    const categoryElements = this.document.querySelectorAll('.category-section');
    for (let i = categoryElements.length - 1; i >= 0; i--) {
      const categoryElement = categoryElements[i] as HTMLElement;
      const categoryTop = categoryElement.offsetTop;
      if (scrollPosition >= (categoryTop + 350)) {
        this.selectedCategoryId = categoryElement.getAttribute('data-category-id')
        break;
      }
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.isMobile = event.target.innerWidth <= 992;
  }

  scrollMenu(direction: 'left' | 'right') {
    const element = this.menuItems.nativeElement;
    const scrollAmount = 200; // adjust as needed

    if (direction === 'left') {
      element.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
    } else {
      element.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  }

  fetchRestaurant() {
    this.isRestaurantLoading = true;
    this.restaurantService.show(this.restaurant_id)
      .pipe(finalize(() => {
        this.isLoading = false;
        this.isRestaurantLoading = false;
      }))
      .subscribe(res => {
        this.restaurant = res;
        if (this.restaurant.meta_title) {
          this.titleService.setTitle(this.restaurant.meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.meta_keyword });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.meta_description });
        } else {
          this.titleService.setTitle(this.restaurant.site_setting.meta_title);
          this.metaTagService.updateTag({ name: 'keywords', content: this.restaurant.site_setting.meta_keywords });
          this.metaTagService.updateTag({ name: 'description', content: this.restaurant.site_setting.meta_description });
        }
        if (this.order.order_type == 'delivery' && res.restaurant_delivery == 'No') {
          this.orderType('pickup');
        }
        if (this.order.order_type == 'pickup' && this.restaurant.restaurant_pickup == 'No') {
          this.orderType('delivery');
        }

        if (this.userId) {
          if ((((!this.user.phone_verify && this.restaurant.site_setting.signup_verify_type == 'phone') || !this.user.email_verify && this.restaurant.site_setting.signup_verify_type == 'mail') || (this.restaurant.site_setting.signup_verify_type == 'both' && (!this.user.phone_verify || !this.user.email_verify))) && this.restaurant.site_setting?.signup_verify == '1') {
            this.otpSend();
            this.modalService.open(this.otpModal, { size: 'md', backdropClass: 'customBackdrop', windowClass: 'rounded-modal' });
          }
        }

      }, err => this.messagingService.error(err))
  }

  fetchCategories() {
    this.isCategoryLoading = true;
    this.categoryService
      .get({ nopaginate: 1, prefilled: 1, restaurant_id: this.restaurant_id, order_type: this.order.order_type })
      .pipe(finalize(() => (this.isCategoryLoading = false)))
      .subscribe(
        (res) => {
          this.originalCategories = res;
          this.categories = res;
          if (this.categories.length > 0) {
            this.selectedCategoryId = this.categories[0].id;
            this.selectedCategory = this.categories.find(category => category.id == this.selectedCategoryId);
          }
        },
        (err) => {
          this.categories = [];
        }
      )
  }

  fetchCarts() {
    this.isCartLoading = true;
    this.cartService
      .get({ restaurant_id: this.restaurant_id, nopaginate: "1" })
      .pipe(finalize(() => (this.isCartLoading = false)))
      .subscribe(
        (res) => {
          this.carts = res;
          this.cartService.carts = this.carts;
        },
        (err) => {
          this.carts = [];
        }
      )
  }

  fetchMenuItem(model, menuId, varaintId) {
    this.isModelLoading = true;
    this.isCategoryLoading = true;
    this.loaderService.show();
    this.menuService
      .show(menuId)
      .pipe(finalize(() => {
        this.isModelLoading = false;
        this.isCategoryLoading = false;
        this.loaderService.hide();
      }))
      .subscribe(
        (res) => {
          this.selectedMenu = res;
          this.selectedVariant = this.selectedMenu.variants.find(
            (varint) => varint.id == varaintId
          );
          this.openModal(model);
        },
        (err) => { }
      )
  }

  otpSend() {
    this.phoneTab = true;
    if (this.restaurant.site_setting.signup_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.restaurant.site_setting.signup_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.restaurant.site_setting.signup_verify_type;
      }
    }

    this.userService.sendBothOtp(this.user).
      pipe(finalize(() => this.isModelOtpLoading = false))
      .subscribe(
        (res) => {
          this.verifyOtp = '';
          this.phoneTab = false;
          // this.modalService.dismissAll();
          this.messagingService.success("otp send successfully !!")
        },
        (err) => {
          this.messagingService.error(err)
        }
      )
  }

  resendOtp() {
    if (this.restaurant.site_setting.signup_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.restaurant.site_setting.signup_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.restaurant.site_setting.signup_verify_type;
      }
    }

    this.userService.sendBothOtp(this.user).
      pipe(finalize(() => this.isModelOtpLoading = false))
      .subscribe(
        (res) => {
          this.messagingService.success("otp sent successfully !!")
        },
        (err) => {
          this.Modelotperror = err;
        }
      )
  }

  validateOtp() {
    this.isModelOtpLoading = true;
    this.Modelotperror = false;

    if (this.restaurant.site_setting.signup_verify_type == 'both') {
      if (!this.user.phone_verify && !this.user.email_verify) {
        this.user.verify_type = 'both';
      } else {
        if (!this.user.phone_verify && this.user.email_verify) {
          this.user.verify_type = 'phone';
        }
        if (this.user.phone_verify && !this.user.email_verify) {
          this.user.verify_type = 'email';
        }
      }
    } else {
      if (this.restaurant.site_setting.signup_verify_type == 'mail') {
        this.user.verify_type = 'email';
      } else {
        this.user.verify_type = this.restaurant.site_setting.signup_verify_type;
      }
    }

    if (!this.verifyOtp && (this.user.verify_type == 'both' || this.user.verify_type == 'phone')) {
      this.messagingService.error("Please enter phone verification code !!")
      this.isModelOtpLoading = false;
    } else if (!this.user.email_otp && (this.user.verify_type == 'both' || this.user.verify_type == 'email')) {
      this.messagingService.error("Please enter email verification code !!")
      this.isModelOtpLoading = false;
    } else {
      this.user.otp = this.verifyOtp
      this.userService.varifyBothOtp(this.user).
        pipe(finalize(() => this.isModelOtpLoading = false))
        .subscribe(
          (res) => {
            this.verifyOtp = '';
            this.phoneTab = false;
            this.fetchMe();
            this.modalService.dismissAll();
            this.messagingService.success("otp verify successfully !!")
          },
          (err) => {
            this.messagingService.error(err)
          }
        )
    }
  }

  fetchMe() {
    this.userService.me()
      .pipe(finalize(() => this.isModelOtpLoading = false))
      .subscribe(res => {
        this.user = res;
        this.userService.saveUser(res);
      }, err => this.Modelotperror = err)
  }

  openModal(model) {
    this.modalService.open(model, this.modalOptions).result.then(
      (result) => {
        let selectedSubAddons = "";
        if (this.selectedVariant.main_addons) {
          for (let mainAddon of this.selectedVariant.main_addons) {

            for (let subAddon of mainAddon.sub_addons) {
              if (mainAddon.selectedSubAddonId == subAddon.id) {
                console.log(subAddon.subaddons_name + " is selected")
              }
              if (subAddon.selected) {
                console.log(subAddon.subaddons_name + " is selected")
              }
            }
          }
        }
      },
      (reason) => {
        console.log(`Dismissed ${this.getDismissReason(reason)}`);
      }
    );
  }

  allergyShow() {
    this.modalService.dismissAll();
    this.modalService.open(this.allergyModal, {
      size: 'md',
      backdrop: 'static',
      backdropClass: 'customBackdrop',
      windowClass: 'rounded-modal'
    });
  }

  search(search: string) {
    if (!search) {
      this.categories = this.originalCategories
    } else {
      let tempAllItems = Object.assign([], this.originalCategories)
      let count = 0;
      let filteredArray = []
      tempAllItems.forEach(category => {
        let tempCategory = Object.assign({}, category);
        let searchedMenu = []
        tempCategory.menu.forEach(menu => {
          if (menu?.menu_name?.toLocaleLowerCase().includes(search.toLocaleLowerCase())) {
            searchedMenu.push(Object.assign({}, menu));
          }
        });
        if (searchedMenu.length > 0) {
          tempCategory.menu = searchedMenu;
          if (count <= 0) {
            this.expandableCategoryId = tempCategory?.id;
            count++;
          }
          filteredArray.push(tempCategory);
        }
      });
      this.categories = Object.assign([], filteredArray);
      if (this.categories.length > 0) {
        this.selectedCategory = this.categories[0];
        this.selectedCategoryId = this.categories[0].id;
      }
      return;

    }
  }

  clickOnAccordion(categoryId: any) {
    this.selectedCategoryId = categoryId;
    this.showCategory = false
    if (categoryId == this.expandableCategoryId) {
      categoryId = null;
    }
    this.expandableCategoryId = categoryId;
    if (categoryId != null) {
      this.scroll('category_' + categoryId);
    }
  }

  scroll(id) {
    if (window.innerWidth <= 1199) {
      var top = document.getElementById(id).offsetTop + 450;
    } else {
      var top = document.getElementById(id).offsetTop + 450;
    }
    window.scrollTo(0, top);

    this.selectedCategoryId = id;
    this.selectedCategory = this.categories.find(category => category.id == this.selectedCategoryId);
  }

  addItemToCart(model, menu, menuId, variantId) {
    this.loaderService.show();
    if (!this.userId || this.userId == undefined || this.userId == null) {
      this.loaderService.hide();
      this.router.navigate(['/auth']);
      return;
    } else {
      this.selectedMenu = menu;
      if (this.selectedMenu.menu_addon == 'No' && this.selectedMenu.price_option == 'single') {
        let cart = new Cart();
        cart.menu_id = this.selectedMenu.id;
        cart.restaurant_id = this.restaurant_id;
        cart.menu_name = this.selectedMenu.menu_name;
        if (this.selectedMenu.product_percentage > 0) {
          cart.menu_price = this.selectedMenu.variants[0]?.orginal_price - (this.selectedMenu.variants[0]?.orginal_price * this.selectedMenu.product_percentage / 100);
          cart.total_price = this.selectedMenu.variants[0]?.orginal_price - (this.selectedMenu.variants[0]?.orginal_price * this.selectedMenu.product_percentage / 100);
          cart.total_price = cart.total_price * this.quantity;
        } else {
          cart.menu_price = this.selectedMenu.variants[0]?.orginal_price;
          cart.total_price = this.selectedMenu.variants[0]?.orginal_price;
          cart.total_price = cart.total_price * this.quantity;
        }
        // cart.quantity = 1;
        cart.quantity = this.quantity;
        cart.customer_id = this.userId;
        this.uploadCart(cart);
        this.quantity = 1;
        // this.loaderService.hide();
      } else if (this.selectedMenu.menu_addon == 'No' && this.selectedMenu.price_option == 'multiple') {
        let cart = new Cart();
        const selectedVariant = this.selectedMenu.variants.find(
          (varint) => varint.id == variantId
        );
        cart.menu_id = this.selectedMenu.id;
        cart.restaurant_id = this.restaurant_id;
        cart.menu_name = this.selectedMenu.menu_name;
        if (this.selectedMenu.product_percentage > 0) {
          cart.menu_price = selectedVariant?.orginal_price - (selectedVariant?.orginal_price * this.selectedMenu.product_percentage / 100);
          cart.total_price = selectedVariant?.orginal_price - (selectedVariant?.orginal_price * this.selectedMenu.product_percentage / 100);
          cart.total_price = cart.total_price * this.quantity;
        } else {
          cart.menu_price = selectedVariant?.orginal_price;
          cart.total_price = selectedVariant?.orginal_price;
          cart.total_price = cart.total_price * this.quantity;
        }
        cart.subaddons_name = selectedVariant?.sub_name;
        // cart.quantity = 1;
        cart.quantity = this.quantity
        cart.customer_id = this.userId;
        this.uploadCart(cart);
        this.quantity = 1;
        // this.loaderService.hide();
      }
      else {
        this.fetchMenuItem(model, menuId, variantId);
      }
    }
  }

  uploadCart(cart: Cart) {
    this.isCategoryLoading = true;
    this.cartService.create(cart)
      .pipe(finalize(() => { this.isCategoryLoading = false; this.loaderService.hide(); }))
      .subscribe(
        (res) => {
          this.carts = res;
          this.cartService.carts = this.carts;
        },
        (err) => {
          this.carts = [];
        }
      )
  }

  clickOnMainAddon(mainAddonId) {
    this.expandableMainAddonId = mainAddonId;
  }

  orderType(order_type: string) {
    let oldOrderType = this.order.order_type;
    this.order.order_type = order_type;
    this.cartService.saveOrderType(order_type);
    // Reset mismatch flags
    this.hasOrderTypeMismatch = false;
    this.hasDayMismatch = false;

    if (this.cartService.carts.length > 0) {
      const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
      this.carts.forEach(item => {
        const menu = item.menu;
        const itemOrderType = menu.product_order_type?.toLowerCase();
        const itemDaysRaw = menu.product_day?.toLowerCase() || 'all';
        const itemDays = itemDaysRaw.split(',').map(day => day.trim());
        if (itemOrderType !== 'both' && itemOrderType !== order_type.toLowerCase()) {
          this.hasOrderTypeMismatch = true;
        }
        if (itemDaysRaw !== 'all' && !itemDays.includes(selectedDay)) {
          this.hasDayMismatch = true;
        }
      });
      if (this.hasOrderTypeMismatch || this.hasDayMismatch) {
        this.modalService.dismissAll();
        this.modalService.open(this.itemNotAvailableModal, {
          size: 'lg',
          backdrop: 'static',
          backdropClass: 'customBackdrop',
          windowClass: 'rounded-modal'
        });
        return order_type;
      }

      // ✅ Save new order type if no mismatch
      if ((oldOrderType !== order_type) && !this.hasOrderTypeMismatch && !this.hasDayMismatch) {
        this.fetchCategories();
      }
    } else {
      if ((oldOrderType !== order_type) && !this.hasOrderTypeMismatch && !this.hasDayMismatch) {
        this.fetchCategories();
      }
    }

    return order_type;
  }

  handleItemNotAvailable(type: string) {
    if (type === 'yes') {
      const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
      const itemsToRemove = this.carts.filter(cart => {
        const cartOrderType = cart.menu.product_order_type?.toLowerCase();
        const cartDaysRaw = cart.menu.product_day?.toLowerCase() || 'all';
        const cartDays = cartDaysRaw.split(',').map(day => day.trim());
        const orderTypeMismatch = cartOrderType !== 'both' && cartOrderType !== this.order.order_type.toLowerCase();
        const dayMismatch = cartDaysRaw !== 'all' && !cartDays.includes(selectedDay);
        return orderTypeMismatch || dayMismatch;
      });

      const deleteCalls = itemsToRemove.map(item =>
        this.cartService.delete(item.id).
          pipe(finalize(() => { }))
          .subscribe(
            (res) => {
              this.carts = res;
              this.cartService.carts = this.carts;
            },
            (err) => {
              this.fetchCarts();
            }
          )
      );
      // this.router.navigate(['/menu']);
      this.fetchCategories();
    } else {
      const selectedDay = new Date().toLocaleDateString('en-us', { weekday: 'long' }).toLowerCase();
      const itemsToRemove = this.carts.filter(cart => {
        const cartOrderType = cart.menu.product_order_type?.toLowerCase();
        const cartDaysRaw = cart.menu.product_day?.toLowerCase() || 'all';
        const cartDays = cartDaysRaw.split(',').map(day => day.trim());
        const orderTypeMismatch = cartOrderType !== 'both' && cartOrderType !== this.order.order_type.toLowerCase();
        const dayMismatch = cartDaysRaw !== 'all' && !cartDays.includes(selectedDay);
        return orderTypeMismatch || dayMismatch;
      });

      if (itemsToRemove.length > 0) {
        if (this.order.order_type == 'delivery') {
          this.order.order_type = 'pickup';
          this.cartService.saveOrderType('pickup');
        } else if (this.order.order_type == 'pickup') {
          this.order.order_type = 'delivery';
          this.cartService.saveOrderType('delivery');
        } else {
          this.order.order_type = 'delivery';
          this.cartService.saveOrderType('delivery');
        }
      }
    }

    this.modalService.dismissAll();
  }

  validate(menuModal) {
    if (this.selectedVariant.main_addons) {
      for (let mainAddon of this.selectedVariant.main_addons) {
        mainAddon.max_error = false;
        mainAddon.min_error = false;
        var subAddonCount: number = 0;
        if (mainAddon.mainaddons_count == 1 && mainAddon.selectedSubAddonId != null) {
          subAddonCount = subAddonCount + 1;
        } else {
          for (let subAddon of mainAddon.sub_addons) {
            if (subAddon.selected) {
              subAddonCount = subAddonCount + 1;
            }
          }
        }
        if (subAddonCount < mainAddon.mainaddons_mini_count) {
          mainAddon.min_error = true;
          this.expandableMainAddonId = mainAddon.id;
          return;
        }
        if (subAddonCount > mainAddon.mainaddons_count) {
          mainAddon.max_error = true;
          this.expandableMainAddonId = mainAddon.id;
          return;
        }
      }
    }

    // create cart menu addon string 
    let selectedSubAddonstring = "";
    var selectedSubAddonPrice = 0;
    var mainAddonCount: number = 0;
    if (this.selectedVariant.main_addons) {
      for (let mainAddon of this.selectedVariant.main_addons) {
        var subAddonCount: number = 0;
        if (mainAddonCount <= 0) {
          if (this.selectedVariant.sub_name && this.selectedMenu.variants.length > 1) {
            selectedSubAddonstring += this.selectedVariant.sub_name + ',';
          }
          if (this.selectedMenu.product_percentage > 0) {
            selectedSubAddonPrice = selectedSubAddonPrice + (this.selectedVariant.orginal_price - (this.selectedVariant.orginal_price * this.selectedMenu.product_percentage / 100));
          } else {
            selectedSubAddonPrice = selectedSubAddonPrice + this.selectedVariant.orginal_price;
          }
        }
        for (let subAddon of mainAddon.sub_addons) {
          if (subAddonCount == 0 && (subAddon.selected || mainAddon.selectedSubAddonId == subAddon.id)) {
            selectedSubAddonstring += mainAddon.mainaddons_name;
          }
          if (subAddon.selected || mainAddon.selectedSubAddonId == subAddon.id) {
            selectedSubAddonstring += ' (' + subAddon.subaddons_name + ') +';
            // '(' + this.currencyPipe.transform(subAddon.subaddons_price, 'GBP', 'symbol', '1.2-2') + ')' +
            selectedSubAddonPrice = selectedSubAddonPrice + subAddon.subaddons_price;
            subAddonCount = subAddonCount + 1;
          }
        }
        mainAddonCount = mainAddonCount + 1;
        var lastChar = selectedSubAddonstring.slice(-1);
        if (lastChar == '+') {
          selectedSubAddonstring = selectedSubAddonstring.slice(0, -1); // trim last character
        }
        if ((mainAddonCount) != this.selectedVariant.main_addons.length) {
          for (let subAddon of mainAddon.sub_addons) {
            if (subAddon.selected || mainAddon.selectedSubAddonId == subAddon.id) {
              selectedSubAddonstring = selectedSubAddonstring + ",";
            }
          }
        }
      }
    } else {
      if (this.selectedMenu.product_percentage > 0) {
        selectedSubAddonPrice = this.selectedMenu.variants[0]?.orginal_price - (this.selectedMenu.variants[0]?.orginal_price * this.selectedMenu.product_percentage / 100);
      } else {
        selectedSubAddonPrice = this.selectedMenu.variants[0]?.orginal_price;
      }
    }

    let cart = new Cart();
    cart.menu_id = this.selectedMenu.id;
    cart.restaurant_id = this.restaurant_id;
    cart.menu_name = this.selectedMenu.menu_name;
    cart.subaddons_name = selectedSubAddonstring;
    cart.menu_price = selectedSubAddonPrice;

    if (this.quantity > 1) {
      cart.quantity = this.quantity;
      cart.total_price = selectedSubAddonPrice * this.quantity;
    } else {
      cart.quantity = this.quantity;
      cart.total_price = selectedSubAddonPrice;
    }

    cart.customer_id = this.userId;
    this.uploadCart(cart);
    this.quantity = 1;
    this.modalService.dismissAll();
  }

  convertNumber(event) {
    if (event > 0) {
      var val = parseFloat(event);
      var val1 = (val).toFixed(2);
      val1 = this.currencyPipe.transform(val1, 'GBP', 'symbol', '1.2-2')
    } else {
      var val1 = 'Free'
    }
    return val1
  }

  selectCategory(id: any) {
    // this.selectedCategoryId = id;
    // this.selectedCategory = this.categories.find(category => category.id == this.selectedCategoryId);

    this.scroll('category_' + id);
  }

  totalPrice(): number {
    return this.carts.reduce((sum, item) => sum + (item.total_price * item.quantity), 0);
  }

  formatReviews(value: number): string {
    if (value >= 1000000) {
      return (value / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
    }
    if (value >= 1000) {
      return (value / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
    }
    return value?.toString();
  }

  trackByFn(index, item) {
    return item.id;
  }

  toggleMenu() {
    this.showMenu = !this.showMenu;
  }

  toggleCart() {
    this.showCart = !this.showCart;
  }

  getQuantity(menuId: string): number {
    return this.carts
      .filter(item => item.menu_id === menuId)
      .reduce((total, item) => total + item.quantity, 0);
  }

  getGrandTotal() {
    let grand_total = 0;
    this.carts.forEach(item => {
      grand_total = grand_total + item.total_price;
    });
    return grand_total;
  }

  updateToCart(cart: Cart, event: string) {

    if (event == 'add') {
      cart.quantity = cart.quantity + 1;
      cart.total_price = cart.menu_price * cart.quantity;
      this.isCartLoading = true;
      this.cartService.update(cart)
        .pipe(finalize(() => { this.isCartLoading = false; }))
        .subscribe(
          (res) => {
            this.carts = res;
            this.cartService.carts = this.carts;

            this.cartPanel.nativeElement.scroll({
              top: this.cartPanel.nativeElement.scrollHeight,
              behavior: 'smooth'
            });

          },
          (err) => {
            this.carts = [];
          }
        )
    }

    if (event == 'remove') {
      var quantity = cart.quantity - 1;
      cart.total_price = cart.menu_price * cart.quantity;
      if (quantity > 0) {
        cart.quantity = quantity;
        this.isCartLoading = true;
        this.cartService.update(cart)
          .pipe(finalize(() => {
            this.isCartLoading = false;
          }))
          .subscribe(
            (res) => {
              this.carts = res;
              this.cartService.carts = this.carts;

              this.cartPanel.nativeElement.scroll({
                top: this.cartPanel.nativeElement.scrollHeight,
                behavior: 'smooth'
              });

            },
            (err) => {
              this.carts = [];
            }
          )
      } else {
        this.isCartLoading = true;
        this.cartService.delete(cart.id)
          .pipe(finalize(() => {
            this.isCartLoading = false;
          }))
          .subscribe(
            (res) => {
              this.carts = res;
              this.cartService.carts = this.carts;

              this.cartPanel.nativeElement.scroll({
                top: this.cartPanel.nativeElement.scrollHeight,
                behavior: 'smooth'
              });

              if (!(this.carts && this.carts.length > 0)) {
                this.toggleCart();
              }
            },
            (err) => {
              this.toggleCart();
              this.fetchCarts();
            }
          )
      }
    }

    if (event == 'delete') {
      this.isCartLoading = true;
      this.cartService.delete(cart.id)
        .pipe(finalize(() => {
          this.isCartLoading = false;
        }))
        .subscribe(
          (res) => {
            this.carts = res;
            this.cartService.carts = this.carts;
            if (!(this.carts && this.carts.length > 0)) {
              this.toggleCart();
            }
          },
          (err) => {
            this.toggleCart();
            this.fetchCarts();
          }
        )
    }
  }

  // New method for variant selection
  selectVariant(variantSelectionModal, menu, type?: string) {
    if (!this.userId) {
      this.router.navigateByUrl('/auth');
      return;
    }

    if (type == 'back' && (this.variantMenu?.id == menu.id)) {
      this.openModal(variantSelectionModal);
      return;
    }

    this.variantMenu = menu;
    this.selectedVariant = menu.variants[0];
    this.selectedVariantId = menu.variants[0].id;
    this.quantity = 1;

    this.fetchMenuItem(variantSelectionModal, menu.id, menu.variants[0].id);
  }

  incrementQty() {
    this.quantity++;
  }

  decrementQty() {
    if (this.quantity > 1) {
      this.quantity--;
    }
  }

  setVariant() {
    this.selectedVariant = this.variantMenu.variants.find(
      (varint) => varint.id == this.selectedVariantId
    );
  }

  checkAddOnAndProceed(addOnSelectionModal, modal, menuModal) {
    this.isModelLoading = true;
    this.menuService
      .show(this.variantMenu.id)
      .pipe(finalize(() => {
        this.isModelLoading = false
        modal.close('Cross click')
      }))
      .subscribe(
        (res) => {
          this.selectedMenu = res;
          this.selectedVariant = this.selectedMenu.variants.find(
            (varint) => varint.id == this.selectedVariant.id
          );
          if (this.selectedVariant.main_addons && this.selectedVariant.main_addons.length > 0) {
            this.openModal(addOnSelectionModal);
          } else {
            this.addItemToCart(menuModal, this.variantMenu, this.variantMenu.id, this.selectedVariant?.id)
          }
        },
        (err) => { }
      )

  }

  getVariantPrice(variant: any): number {
    if (!variant || !this.variantMenu) return 0;

    const discount = this.variantMenu.product_percentage || 0;
    const price = variant.orginal_price || 0;

    return discount > 0
      ? price - (price * discount / 100)
      : price;
  }

  navigateToPreCheckout() {
    this.router.navigate(['/pre-checkout']);
  }

  navigateToInfo() {
    this.router.navigate(['/info']);
  }

  handleImageError(event: any) {
    event.target.src = 'assets/no_image1.png';
  }

  ngOnDestroy() {
    this.modalService.dismissAll();
  }
}

