import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorHandler } from 'src/app/shared/error-handler';
import { environment } from 'src/environments/environment';
import { User } from '../models/user';
import * as CryptoJS from 'crypto-js';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  public user: User;

  private url = environment.apiBaseUrl + 'users/';
  private urlRMS = `${environment.rmsUrl}`

  private userTheme = new BehaviorSubject<string>('');
  userTheme$ = this.userTheme.asObservable();
  userThemeLoading: boolean = true;

  constructor(private http: HttpClient, private router: Router) { }

  login(user: User): Observable<User> {
    return this.http
      .post<User>(this.url + 'login', User.loginForm(user), {})
      .pipe(catchError(ErrorHandler.handleError));
  }
  signup(user: User): Observable<User> {
    return this.http
      .post<User>(this.url + 'signup', User.signupForm(user), {})
      .pipe(catchError(ErrorHandler.handleError));
  }
  me(): Observable<User> {
    return this.http
      .get<User>(this.url + 'me', {})
      .pipe(catchError(ErrorHandler.handleError));
  }
  show(id: string): Observable<User> {
    return this.http.get<User>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  saveUser(user: User) {
    this.user = user;
    const key = CryptoJS.enc.Utf8.parse(environment.garble);
    const iv = CryptoJS.enc.Utf8.parse(environment.jumble);
    const encrypted = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(JSON.stringify(user)), key,
      {
        keySize: 128 / 8,
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });
    localStorage.setItem(environment.userKey, encrypted.toString());
  }

  getUser() {
    const key = CryptoJS.enc.Utf8.parse(environment.garble);
    const iv = CryptoJS.enc.Utf8.parse(environment.jumble);
    const encrypted = localStorage.getItem(environment.userKey);
    if (encrypted) {
      const decrypted = CryptoJS.AES.decrypt(encrypted, key, {
        keySize: 128 / 8,
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });
      return decrypted.toString(CryptoJS.enc.Utf8);
    } else {
      return encrypted;
    }
    // return localStorage.getItem(environment.userKey);
  }

  setTokens(tokens) {
    localStorage.setItem(environment.accessTokenKey, tokens.accessToken);
    localStorage.setItem(environment.refreshTokenKey, tokens.refreshToken);
  }

  hasTokens(): boolean {
    return (
      !!localStorage.getItem(environment.accessTokenKey) &&
      !!localStorage.getItem(environment.refreshTokenKey)
    );
  }

  logout() {
    this.user = null;
    localStorage.removeItem(environment.accessTokenKey);
    localStorage.removeItem(environment.refreshTokenKey);
    localStorage.removeItem(environment.userKey);
    localStorage.removeItem(environment.order_type);
    localStorage.removeItem(environment.order);
    this.router.navigateByUrl('/auth');
  }

  postcode(postcode: string): Observable<any> {
    return this.http.get<any>(this.urlRMS + "postcodes/find/" + postcode)
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(user: User): Observable<any> {
    return this.http.post<User>(this.url + user.id, User.toFormData(user))
      .pipe(catchError(ErrorHandler.handleError));
  }

  disabledPhoneVerify(user: User): Observable<any> {
    return this.http.post<User>(this.url + user.id + "/verify", User.toPhoneVerifyFormData(user))
      .pipe(catchError(ErrorHandler.handleError));
  }

  changepassword(user: User): Observable<User> {
    return this.http.post<User>(this.url + user.id + '/change-password', User.toFormData(user))
      .pipe(catchError(ErrorHandler.handleError));
  }

  forgotpassword(user: User): Observable<any> {
    return this.http.post<User>(this.url + 'forgot-password-direct', User.toForgotMailFormData(user))
      .pipe(catchError(ErrorHandler.handleError));
  }

  allDashboard(options: any = {}): Observable<any> {
    return this.http.get<any>(this.url + 'dashboard')
      .pipe(catchError(ErrorHandler.handleError));
  }

  sendLink(options: any = {}): Observable<any> {
    let params = new HttpParams();
    if (options.phone_number) params = params.set('phone_number', options.phone_number);
    if (options.message) params = params.set('message', options.message);
    return this.http.get<any>(`${this.url}send-referral`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  sendLinkEmail(options: any = {}): Observable<any> {
    let params = new HttpParams();
    if (options.email) params = params.set('email', options.email);
    if (options.message) params = params.set('message', options.message);
    return this.http.get<any>(`${this.url}send-referral-email`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  sendOtp(options: any = {}): Observable<any> {
    return this.http.get<any>(this.url + 'send-otp')
      .pipe(catchError(ErrorHandler.handleError));
  }

  sendBothOtp(user: User): Observable<any> {
    return this.http.post<User>(this.url + user.id + '/send-otp-both', User.toFormData(user))
      .pipe(catchError(ErrorHandler.handleError));
  }

  varifyBothOtp(user: User): Observable<any> {
    return this.http.post<User>(this.url + user.id + '/verify-otp-both', User.toFormData(user))
      .pipe(catchError(ErrorHandler.handleError));
  }

  varifyOtp(user: User): Observable<any> {
    return this.http.post<User>(this.url + user.id + '/verify-otp', User.toFormData(user))
      .pipe(catchError(ErrorHandler.handleError));
  }

  varifyEmail(options: any = {}): Observable<any> {
    let params = new HttpParams();
    if (options.code) params = params.set('code', options.code);
    if (options.id) params = params.set('id', options.id);

    return this.http.get<any>(this.url + 'verify-email', { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  setLoader(isLoading: boolean) {
    this.userThemeLoading = isLoading;
  }

  setUserTheme(theme: string) {
    this.userTheme.next(theme);
  }

  getUserTheme() {
    return this.userTheme.asObservable();
  }
}
