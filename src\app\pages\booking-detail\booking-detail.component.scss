// @use 'style.scss'; // Path to _variables.scss Notice how we don't include the underscore or file extension

.menu-item {
  .menu-details {
    width: 100%;
  }

  .add-item {
    cursor: pointer;
  }
}

.cart-wrapper-fixed {
  position: fixed;
  z-index: 1;
  top: 70px !important;
}

.order-types {
  box-shadow: 0px 7px 9px 0px #3e3e3e21;
}

.footer-category {
  position: fixed;
  right: 12px;
  bottom: 10px;
  width: 220px;
  z-index: 12;
  max-height: 300px;
  letter-spacing: .3px;
  text-align: left;
  overflow-y: scroll;
}

.strikethrough {
  position: relative;
}

.strikethrough:before {
  position: absolute;
  content: "";
  left: 0;
  top: 10px;
  right: 0;
  border-top: 5px solid grey !important;
  border-color: inherit;
}

.strikethrough .active:before {
  position: absolute;
  content: "";
  left: 0;
  top: 10px;
  right: 0;
  border-top: 5px solid var(--primary);
  border-color: inherit;
}

.strikethrough .active:after {
  position: absolute;
  content: "";
  left: 0;
  top: 10px;
  right: 0;
  border-top: 5px solid var(--primary);
  border-color: inherit;
}

.strikethrough div.active:after {
  color: var(--primary);
}

.status {
  ::before {
    height: 2px;
    width: 40px;
    background: grey;
  }

  ::after {
    height: 2px;
    width: 40px;
    background: grey;
  }

  .dot {
    height: 25px;
    width: 25px;
    z-index: 1;
    border-radius: 50%;
    background: grey;
  }

  .title {
    font-size: 12px;
    color: black;
    margin-top: 5px;
    font-weight: bold;
  }

  &.active {
    .dot {
      background: var(--primary);
    }

    .title {
      color: var(--primary);
    }
  }
}

.center {
  display: flex;
  justify-content: center;
}
