<ng-container *ngIf="!userService.userThemeLoading; else themeLoading">
  <ng-container [ngSwitch]="menuTheme">
    <app-pre-checkout *ngSwitchCase="'theme1'"></app-pre-checkout>
    <app-pre-checkout2 *ngSwitchCase="'theme2'"></app-pre-checkout2>
    <app-pre-checkout2 *ngSwitchCase="'theme3'"></app-pre-checkout2>

    <!-- Optional: fallback -->
    <div *ngSwitchDefault>
      <app-pre-checkout></app-pre-checkout>
    </div>
  </ng-container>
</ng-container>

<ng-template #themeLoading>
  <div class="d-flex justify-content-center align-items-center w-100 h-100" style="min-height: 80vh;">
    <div class="cart-loader">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  </div>
</ng-template>