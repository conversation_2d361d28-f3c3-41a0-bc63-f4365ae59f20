<div class="container mt-3 mb-3">
  <div class="row text-center align-middle justify-content-center" *ngIf="isLoading">
    <div class="col-sm-12 ms-2 spinner-border text-primary text-center">
      <span class="visually-hidden text-center">Loading...</span>
    </div>
  </div>
  <div class="row" style="row-gap: .75rem;" *ngIf="!isLoading">
    <div class="col-sm-12 col-md-4" *ngIf="originalReviews.length > 0">
      <div class="review-summary">
        <h1 class="score">{{finalReview}}<span class="out-of">/5</span></h1>

        <div class="rating-stars">
          <app-rating [stars]="'5'" [rating]="finalReview" [viewOnly]="true"></app-rating>
        </div>

        <div class="recommendation" *ngIf="finalReview >= 4">Highly Recommended</div>
        <div class="review-count">({{reviews.length}} Reviews)</div>

        <hr class="my-2 border">

        <div class="filter-title">Filter by</div>

        <div class="filter-option py-2 mb-3" [class.active]="selectedFilter === 'all'" (click)="reviewFilter('all')">
          All
        </div>

        <div class="filter-option  mb-1" *ngFor="let r of [5,4,3,2,1]" [class.active]="selectedFilter === r"
          (click)="reviewFilter(r)">
          <app-rating [stars]="'5'" [rating]="r" [viewOnly]="true"></app-rating>
        </div>
      </div>
    </div>

    <div class="col-sm-12 col-md-8" *ngIf="reviews.length > 0">
      <div class="review-card" *ngFor="let review of reviews">
        <div class="review-header">
          <app-rating [stars]="'5'" [rating]="review.rating" [viewOnly]="true"></app-rating>
          <span class="review-date">{{convertToDate(review.created)}}</span>
        </div>

        <div class="review-author">{{review.first_name}}</div>

        <div class="review-message">
          {{review.message}}
        </div>

        <div class="review-response" *ngIf="review.responce">
          <div class="response-title">Response from the Restaurant</div>
          <div class="response-text">{{review.responce}}</div>
        </div>
      </div>
    </div>

    <div class="col-sm-12 col-md-8 bg-white body-box-shadow empty-cart-cls text-center py-5"
      style="border-radius: 1rem;position: sticky;top: 1rem;" *ngIf="reviews.length <= 0">
      <img src="assets/boxitem.png" class="img-fluid mb-4 mr-3">
      <p><strong>No review(s) Found</strong></p>
    </div>
  </div>
</div>