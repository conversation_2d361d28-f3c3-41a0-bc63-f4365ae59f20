<form nz-form class="login-form mx-auto" #loginForm="ngForm" (ngSubmit)="login(loginForm)">
  <h5 class="text-center">Login</h5>
  <div class="alert alert-danger alert-dismissible fade show" role="alert" *ngIf="errorMessage">
    {{ errorMessage }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
  </div>
  <div class="mb-3">
    <label for="email" class="form-label">Email</label>
    <input name="email" type="email" class="form-control" (keydown.space)="$event.preventDefault()" id="email"
      placeholder="Enter email" [(ngModel)]="user.username" />
  </div>
  <div class="mb-3">
    <label for="password" class="form-label">Password</label>
    <input name="password" type="password" class="form-control" (keydown.space)="$event.preventDefault()" id="password"
      placeholder="Enter password" [(ngModel)]="user.password" />
  </div>
  <div class="mb-2 text-end">
    <label class="form-label cursor" (click)="forgotPassword(forgotModel)">Forgot Password ?</label>
  </div>
  <div class="d-flex justify-content-center">
    <button type="submit" [disabled]="isLoading" class="text-center btn btn-primary w-25 text-white">
      <div class="ms-2 spinner-border" *ngIf="isLoading" style="width: 20px;height: 20px;" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Login
    </button>

  </div>
  <div class="row py-2">
    <div class="col-lg-12 col-md-12 col-xs-12 col-sm-12 text-center py-1 text-muted"
      *ngIf="(restaurant.facebook_login == 'Y' || restaurant.google_login == 'Y') && false">
      [ OR ]
    </div>
    <div class="col-lg-6 col-md-6 col-xs-12 col-sm-6 py-1" *ngIf="restaurant.facebook_login == 'Y' && false">
      <span (click)="loginWithFacebook()" class="btn w-100" style="background-color: #3b5998;color:#fff">
        <img src="./assets/facebook_white.png"> Signin with Facebook </span>
    </div>
    <div class="col-lg-6 col-md-6 col-xs-12 col-sm-6 py-1" *ngIf="restaurant.google_login == 'Y' && false">
      <span (click)="loginWithGoogle()" class="btn btn-danger w-100">
        <img src="./assets/google_white.png" class="px-1"> Signin with Google</span>
    </div>
  </div>
</form>

<ng-template #forgotModel let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Forgot Password
    </h4>
    <button type="button" class="close bg-primary text-white" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">

    <div class="col-sm-12 col-xs-12 form-group">
      <label>Email</label>
      <!-- <div class="col-12 d-flex"> -->
      <input type="email" class="form-control" id="email" name="email" [(ngModel)]="forgotUser.username"
        [disabled]="isEmailVerify" required placeholder="Enter your email" />
      <!-- <div class="text-primary fw-bold col-3 cursor" (click)="isEmailVerify = false" *ngIf="isEmailVerify">Edit</div> -->
      <!-- </div> -->
    </div>

    <div class="col-sm-12 col-xs-12 form-group mt-2" *ngIf="isEmailVerify">
      <label>Otp</label>
      <input type="text" class="form-control" id="email_otp" name="email_otp" [(ngModel)]="forgotUser.email_otp"
        required placeholder="Enter your valide otp" />
    </div>

    <div *ngIf="Modelerror">
      <span class="text-danger">{{ Modelerror }}</span>
    </div>

  </div>
  <div class="modal-footer">
    <button type="button" [disabled]="isModelLoading" class="btn btn-primary text-white text-center"
      (click)=" validateEmail()" *ngIf="!isEmailVerify">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Submit
    </button>
    <button type="button" [disabled]="isModelLoading" class="btn btn-primary text-white text-center"
      (click)=" validateOtp()" *ngIf="isEmailVerify">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Submit
    </button>
  </div>
</ng-template>

<ng-template #changePasswordModel let-modal>
  <div class="modal-header bg-primary text-white">
    <h4 class="modal-title" id="modal-basic-title">
      Change Password
    </h4>
    <button type="button" class="close bg-primary text-white" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">

    <div class="col-sm-12 col-xs-12 form-group">
      <label>New Password</label>
      <input type="password" class="form-control" id="password" name="password" [(ngModel)]="forgotUser.password"
        required placeholder="Enter your password" />
    </div>

    <div class="col-sm-12 col-xs-12 mt-2 form-group">
      <label>Confirm Password</label>
      <input type="password" class="form-control" id="confirmPassword" name="confirmPassword"
        [(ngModel)]="forgotUser.confirmPassword" required placeholder="Enter your confirm password" />
    </div>

    <div *ngIf="ModelPassworderror">
      <span class="text-danger">{{ ModelPassworderror }}</span>
    </div>

  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-primary text-white text-center" (click)=" validatePassword()">
      <div class="ms-2 spinner-border text-white" style="width: 20px;height: 20px;" *ngIf="isModelPasswordLoading"
        role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      Submit
    </button>
  </div>
</ng-template>