import { Component, OnInit } from '@angular/core';
import { UserService } from 'src/app/core/services/user.service';

@Component({
  selector: 'app-info-theme',
  templateUrl: './info-theme.component.html',
  styleUrls: ['./info-theme.component.scss']
})
export class InfoThemeComponent implements OnInit {
  menuTheme: string;

  constructor(public userService: UserService) { }

  ngOnInit(): void {
    this.userService.getUserTheme().subscribe(res => {
      this.menuTheme = res;
    })
  }
}
