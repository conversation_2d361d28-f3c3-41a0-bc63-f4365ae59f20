$color_success: #00c851;
$color-error: #ff4444;
$color-warning: #f59e0b;
$color-info: var(--primary);
$stroke-width: 5px;
$size: 40px;
$checkMark-color: white;

.popup-container {
  position: fixed;
  top: 0px;
  right: 0px;
  z-index: 1100;
  transition: opacity 0.3s;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.box-body {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  height: 100%;
  padding: 5rem 0rem;
}

.message-box {
  background-color: white;
}

._success {
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 0.5rem;
  box-shadow: 0 15px 25px #00000019;
  width: max-content;
  max-width: 20rem;
  min-width: 20rem;
  height: auto;
  max-height: 20rem;
  text-align: center;
  border: 2px solid #efefef;
  border-radius: 1rem;
  padding: 1rem;
}

._success i {
  font-size: 55px;
  color: #28a745;
}

._success h2 {
  margin-bottom: 12px;
  font-size: 40px;
  font-weight: 500;
  line-height: 1.2;
  margin-top: 10px;
}

._success p {
  font-size: 16px;
  color: #062e6f;
  font-weight: 400;
  width: 100%;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

._success button {
  background-color: var(--primary);
  color: #fff;
  padding: 0.25rem 1rem;
  border: 1px solid #fff;
  border-radius: 2rem;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;

  &.success {
    background: #00c851 !important;
  }

  &.error {
    background: crimson !important;
  }

  &.warning {
    background: #f59e0b !important;
  }
}

// For Animation
.wrapper {
  flex-direction: column;
  justify-content: center;
  padding: 0rem 0.5rem;
}

.checkMark {
  width: $size;
  height: $size;
  border-radius: 50%;
  display: block;
  stroke-width: $stroke-width;
  stroke: $checkMark-color;
  stroke-miterlimit: 10;
  margin: 0% auto;

  &.success {
    box-shadow: inset 0px 0px 0px $color_success;
    animation: fillSuccess 0.4s ease-in-out 0.4s forwards,
      scale 0.3s ease-in-out 0.9s both;
  }

  &.error {
    box-shadow: inset 0px 0px 0px $color-error;
    animation: fillError 0.4s ease-in-out 0.4s forwards,
      scale 0.3s ease-in-out 0.9s both;
  }

  &.warning {
    stroke-width: 2px;
    box-shadow: inset 0px 0px 0px $color-warning;
    animation: fillWarning 0.4s ease-in-out 0.4s forwards,
      scale 0.3s ease-in-out 0.9s both;
  }

  &.info {
    box-shadow: inset 0px 0px 0px $color-info;
    animation: fillInfo 0.4s ease-in-out 0.4s forwards,
      scale 0.3s ease-in-out 0.9s both;
  }
}

.checkMark_circle_success {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: $stroke-width;
  stroke-miterlimit: 10;
  stroke: $color_success;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkMark_circle_error {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: $stroke-width;
  stroke-miterlimit: 10;
  stroke: $color-error;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkMark_circle_warning {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: $stroke-width;
  stroke-miterlimit: 10;
  stroke: $color-warning;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkMark_circle_info {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: $stroke-width;
  stroke-miterlimit: 10;
  stroke: $color-info;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkMark_check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.9s forwards;

  &.warning {
    stroke-dasharray: 141;
    stroke-dashoffset: 141;
    animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.9s forwards;
  }
}

.progress {
  transform: rotate(-90deg);
  stroke: black;
}

.progress circle {
  stroke-dasharray: 130;
  stroke-dashoffset: 130;
  animation: dash 1.5s infinite;
}

@keyframes dash {
  50% {
    stroke-dashoffset: 0;
  }

  100% {
    stroke-dashoffset: -130;
  }
}

.progress {
  position: absolute;
  top: 5%;
  left: 5%;

  &.progress--thin {
    left: auto;
    right: 5%;
  }
}

@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes scale {
  0%,
  100% {
    transform: none;
  }

  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}

@keyframes fillSuccess {
  100% {
    box-shadow: inset 0px 0px 0px $size/2 $color_success;
  }
}

@keyframes fillError {
  100% {
    box-shadow: inset 0px 0px 0px $size/2 $color-error;
  }
}

@keyframes fillWarning {
  100% {
    box-shadow: inset 0px 0px 0px $size/2 $color-warning;
  }
}

@keyframes fillInfo {
  100% {
    box-shadow: inset 0px 0px 0px $size/2 $color-info;
  }
}

.message-truncate {
  display: -webkit-box;
  max-width: 100%;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
