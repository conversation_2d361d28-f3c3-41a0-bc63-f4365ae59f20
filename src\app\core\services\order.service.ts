import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorHandler } from 'src/app/shared/error-handler';
import { environment } from 'src/environments/environment';
import { Order } from '../models/order';

@Injectable({
  providedIn: 'root',
})
export class OrderService {
  private url = environment.apiBaseUrl + 'orders/';
  constructor(private http: HttpClient) { }

  get(options: any = {}): Observable<any> {
    let params = new HttpParams();

    if (options.page) params = params.set('page', options.page);
    if (options.per_page) params = params.set('per_page', options.per_page);
    if (options.customer_id) params = params.set('customer_id', options.customer_id);
    if (options.restaurant_id) params = params.set('restaurant_id', options.restaurant_id);
    if (options.nopaginate) params = params.set('nopaginate', options.nopaginate);

    return this.http.get<any>(`${this.url}`, { params })
      .pipe(catchError(ErrorHandler.handleError));
  }

  show(id: string): Observable<Order> {
    return this.http.get<Order>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  create(order: Order): Observable<any> {
    return this.http.post<Order>(this.url, Order.toFormData(order))
      .pipe(catchError(ErrorHandler.handleError));
  }

  update(order: Order): Observable<any> {
    return this.http.post<Order>(this.url + order.id, Order.toFormData(order))
      .pipe(catchError(ErrorHandler.handleError));
  }

  delete(id: string): Observable<any> {
    return this.http.delete<Order>(this.url + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

  offercheck(order: Order): Observable<any> {
    return this.http.post<Order>(this.url + 'offer-check', Order.toFormData(order))
      .pipe(catchError(ErrorHandler.handleError));
  }

  reOrder(id: string): Observable<any> {
    return this.http.get<any>(this.url + 're-order/' + id)
      .pipe(catchError(ErrorHandler.handleError));
  }

}
