import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BookingAddComponent } from './booking-add.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgxPayPalModule } from 'ngx-paypal';
import { BookingAddThemeComponent } from './booking-add-theme/booking-add-theme.component';
import { BookingAdd3Component } from './booking-add3/booking-add3.component';
import { BookingAdd2Component } from './booking-add2/booking-add2.component';

const routes: Routes = [
  // { path: '', component: BookingAddComponent },
  { path: '', component: BookingAddThemeComponent }
];

@NgModule({
  declarations: [BookingAddComponent, BookingAddThemeComponent, BookingAdd3Component, BookingAdd2Component],
  imports: [RouterModule.forChild(routes), SharedModule, NgbModule, NgxPayPalModule],
})
export class BookingAddModule { }
