.referral-hero {
  text-align: center;
  padding: 2rem 1rem;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary) 100%);
  border-radius: 1rem;
  color: white;
  margin-bottom: 2rem;

  .referral-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
  }

  .referral-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .referral-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    line-height: 1.5;
    max-width: 600px;
    margin: 0 auto;
  }
}

.referral-code-section {
  background: #f8f9fa;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 2px dashed #dee2e6;

  .code-label {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    text-align: center;
  }

  .code-display {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 0.5rem;
    padding: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .code-text {
      flex: 1;
      font-size: 1.2rem;
      font-weight: 700;
      color: #495057;
      text-align: center;
      letter-spacing: 2px;
    }

    .copy-btn {
      background: var(--primary);
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 0.375rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: var(--primary);
        transform: translateY(-1px);
      }

      i {
        margin-right: 0.5rem;
      }
    }
  }
}

.share-section {
  margin-bottom: 2rem;

  .share-title {
    text-align: center;
    font-weight: 700;
    color: #495057;
    margin-bottom: 1.5rem;
  }

  .share-grid {
    // display: grid;
    // grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    // gap: 1rem;
    max-width: 700px;
    margin: 0 auto;

    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .share-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    // border-radius: 0.75rem;
    border-radius: 100%;
    width: 4rem;
    height: 4rem;
    text-decoration: none;
    color: #fff;
    font-weight: 600;
    transition: all 0.3s;
    border: none;
    cursor: pointer;

    i {
      font-size: 1.5rem;
      // margin-bottom: 0.5rem;
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      color: white;
    }

    &.facebook {
      background: #3b5998;
    }
    &.twitter {
      background: #1da1f2;
    }
    &.whatsapp {
      background: #25d366;
    }
    &.sms {
      background: #ff6b35;
    }
    &.email {
      background: #ea4335;
    }
  }
}

.url-share-section {
  margin-bottom: 2rem;

  .url-input-group {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    overflow: hidden;

    .url-input {
      flex: 1;
      padding: 0.75rem 1rem;
      border: none;
      font-size: 0.9rem;
      background: white;

      &:focus {
        outline: none;
      }
    }

    .url-copy-btn {
      background: #28a745;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      cursor: pointer;
      transition: background 0.2s;

      &:hover {
        background: #218838;
      }
    }
  }
}

.how-it-works {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  // box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 0px 10px -3px #00000070;

  .works-title {
    text-align: center;
    font-weight: 700;
    color: #495057;
    margin-bottom: 2rem;
  }

  .steps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
  }

  .step {
    text-align: center;

    .step-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, var(--primary), var(--primary));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;

      img {
        width: 30px;
        height: 30px;
        filter: brightness(0) invert(1);
      }
    }

    .step-content {
      h6 {
        font-weight: 700;
        color: #495057;
        margin-bottom: 0.5rem;
      }

      p {
        color: #6c757d;
        font-size: 0.9rem;
        line-height: 1.4;
      }
    }
  }
}

.referral-list-card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: background 0.2s;

    &:hover {
      background: linear-gradient(135deg, #e9ecef, #dee2e6);
    }

    .card-title {
      font-weight: 700;
      color: #495057;
      margin: 0;
    }

    i {
      color: #6c757d;
      transition: transform 0.3s;
    }
  }

  .card-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;

    &.expanded {
      max-height: 500px;
    }
  }

  .table-container {
    overflow-x: auto;
  }

  .referral-table {
    width: 100%;
    border-collapse: collapse;

    th {
      background: #f8f9fa;
      padding: 1rem;
      text-align: left;
      font-weight: 600;
      color: #495057;
      border-bottom: 2px solid #dee2e6;
    }

    td {
      padding: 1rem;
      border-bottom: 1px solid #dee2e6;
      color: #495057;
    }

    tr:hover {
      background: #f8f9fa;
    }
  }

  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;

    &.pending {
      background: #fff3cd;
      color: #856404;
    }

    &.completed {
      background: #d4edda;
      color: #155724;
    }
  }

  .empty-state {
    text-align: center;
    padding: 3rem 2rem;

    img {
      width: 80px;
      opacity: 0.5;
      margin-bottom: 1rem;
    }

    h6 {
      font-weight: 700;
      color: #495057;
      margin-bottom: 0.5rem;
    }

    p {
      color: #6c757d;
      font-size: 0.9rem;
    }
  }
}

@media (max-width: 768px) {
  .share-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .steps-container {
    grid-template-columns: 1fr;
  }

  .referral-hero {
    padding: 1.5rem 1rem;

    .referral-title {
      font-size: 1.5rem;
    }
  }
}
