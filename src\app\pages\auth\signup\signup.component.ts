import { Component, Input, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { Referral } from 'src/app/core/models/referral';
import { User } from 'src/app/core/models/user';
import { LocationService } from 'src/app/core/services/location.service';
import { ReferralService } from 'src/app/core/services/referral.service';
import { UserService } from 'src/app/core/services/user.service';

@Component({
  selector: 'app-signup',
  templateUrl: './signup.component.html',
  styleUrls: ['./signup.component.scss'],
})
export class SignupComponent implements OnInit {
  sub = new Subscription();

  user: User = new User();
  referral: Referral = new Referral();

  isLoading = false;
  error = null;
  errorMessage: string;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private userService: UserService,
    private location: LocationService,
    private referralService: ReferralService,
  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.user.referred_by = params['referral'];
    });
    this.fetchReferral();
  }

  signup(form: NgForm) {
    this.isLoading = true;
    this.errorMessage = null;

    this.userService
      .signup(this.user)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe(
        (res) => {
          this.userService.saveUser(res);
          // this.router.navigateByUrl('/');
          this.location.back('');
        },
        (err) => {
          this.errorMessage = err;
        }
      );
  }

  fetchReferral() {
    this.isLoading = true;

    this.sub.add(this.referralService.show('1')
      .pipe(finalize(() => this.isLoading = false))
      .subscribe(res => {
        this.referral = res;
      }, err => this.error = err)
    );
  }

  keyPress(event: any) {
    const pattern = /[0-9\+\-\ ]/;

    let inputChar = String.fromCharCode(event.charCode);
    if (event.keyCode != 8 && !pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  onlyNumeric(): boolean {
    if (Number(this.user.phone_number)) {
    } else {
      this.user.phone_number = '';
    }
    return false
  }
  ngOnDestroy() { }
}
