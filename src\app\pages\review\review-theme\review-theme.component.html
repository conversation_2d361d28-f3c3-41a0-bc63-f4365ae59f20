<ng-container *ngIf="!userService.userThemeLoading; else themeLoading">
  <ng-container [ngSwitch]="menuTheme">
    <app-review *ngSwitchCase="'theme1'"></app-review>
    <app-review2 *ngSwitchCase="'theme2'"></app-review2>
    <app-review3 *ngSwitchCase="'theme3'"></app-review3>

    <!-- Optional: fallback -->
    <div *ngSwitchDefault>
      <app-review></app-review>
    </div>
  </ng-container>
</ng-container>

<ng-template #themeLoading>
  <div class="d-flex justify-content-center align-items-center w-100 h-100" style="min-height: 80vh;">
    <div class="cart-loader">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>
  </div>
</ng-template>