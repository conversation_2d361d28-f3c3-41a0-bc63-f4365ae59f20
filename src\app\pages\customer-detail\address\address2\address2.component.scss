::ng-deep .rounded-modal > .modal-dialog > .modal-content {
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.address-modal {
  border-radius: 1rem;

  .address-modal-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 1rem;

    .address-close-btn {
      background: #f5f5f5;
      border: none;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .address-modal-title {
      font-size: 1.5rem;
    }
  }

  .address-modal-close {
    background-color: var(--primary);
    color: #fff;
    padding: 0.75rem;
    border-radius: 2rem;
    font-size: 1rem;
    font-weight: 500;
    border: 1px solid #fff;
    cursor: pointer;

    &:hover {
      color: var(--primary);
      border: 1px solid var(--primary);
      background-color: #fff;
    }
  }
}

.add-address-button {
  background: var(--primary);
  color: white;
  border: 1px solid #fff;
  padding: 8px 14px;
  border-radius: 2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: fit-content;
  margin-left: auto;

  &:hover {
    color: var(--primary);
    border: 1px solid var(--primary);
    background-color: #fff;
  }
}

.card {
  width: 49%;

  @media (max-width: 768px) {
    width: 100%;
  }
}
