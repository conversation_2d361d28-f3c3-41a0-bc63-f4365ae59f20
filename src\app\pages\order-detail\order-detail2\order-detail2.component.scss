.menu-item {
  .menu-details {
    width: 100%;
  }

  .add-item {
    cursor: pointer;
  }
}

.cart-wrapper-fixed {
  position: fixed;
  z-index: 1;
  top: 70px !important;
}

.order-types {
  box-shadow: 0px 7px 9px 0px #3e3e3e21;
}

.footer-category {
  position: fixed;
  right: 12px;
  bottom: 10px;
  width: 220px;
  z-index: 12;
  max-height: 300px;
  letter-spacing: 0.3px;
  text-align: left;
  overflow-y: scroll;
}

.strikethrough {
  position: relative;
}

.strikethrough:before {
  position: absolute;
  content: "";
  left: 0;
  top: 10px;
  right: 0;
  border-top: 5px solid grey !important;
  border-color: inherit;
}

.strikethrough .active:before {
  position: absolute;
  content: "";
  left: 0;
  top: 10px;
  right: 0;
  border-top: 5px solid var(--primary);
  border-color: inherit;
}

.strikethrough .active:after {
  position: absolute;
  content: "";
  left: 0;
  top: 10px;
  right: 0;
  border-top: 5px solid var(--primary);
  border-color: inherit;
}

.strikethrough div.active:after {
  color: var(--primary);
}

.status {
  ::before {
    height: 2px;
    width: 40px;
    background: grey;
  }

  ::after {
    height: 2px;
    width: 40px;
    background: grey;
  }

  .dot {
    height: 25px;
    width: 25px;
    z-index: 1;
    border-radius: 50%;
    background: grey;
  }

  .title {
    font-size: 12px;
    color: black;
    margin-top: 5px;
    font-weight: bold;
  }

  &.active {
    .dot {
      background: var(--primary);
    }

    .title {
      color: var(--primary);
    }
  }
}

.center {
  display: flex;
  justify-content: center;
}

.cart-items-list {
  margin-top: 1rem;

  .card {
    max-height: 25rem;
    overflow-y: scroll;
  }
}

.back-home-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: 6px 16px;
  border-radius: 2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  text-decoration: none;

  &:hover {
    transform: translateY(-5%);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  }
}

.sticky-responsive {
  position: relative;

  @media (min-width: 768px) {
    position: sticky;
    top: 1rem;
  }
}

.status-message-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.1)
    );
    border-radius: 50%;
    transform: translate(30px, -30px);
  }

  .message-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    position: relative;
    z-index: 1;
  }

  .message-content {
    flex: 1;
    position: relative;
    z-index: 1;

    .message-title {
      font-size: 1.1rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      color: #2c3e50;
    }

    .message-text {
      color: #5a6c7d;
      line-height: 1.5;
      margin-bottom: 1rem;
      font-size: 0.95rem;
    }
  }

  .message-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    margin-top: 1rem;
  }

  .action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;

    &:hover {
      transform: translateY(-1px);
    }

    &.primary {
      background: #3498db;
      color: white;

      &:hover {
        background: #2980b9;
      }
    }

    &.secondary {
      background: #ecf0f1;
      color: #2c3e50;

      &:hover {
        background: #d5dbdb;
      }
    }

    i {
      font-size: 0.9rem;
    }
  }

  // Status-specific styles
  &.pending {
    border-left-color: #f39c12;

    .message-icon {
      background: linear-gradient(135deg, #f39c12, #e67e22);
    }
  }

  &.info {
    border-left-color: #3498db;

    .message-icon {
      background: linear-gradient(135deg, #3498db, #2980b9);
    }
  }

  &.accepted {
    border-left-color: #27ae60;

    .message-icon {
      background: linear-gradient(135deg, #27ae60, #229954);
    }
  }

  &.delivered {
    border-left-color: #2ecc71;

    .message-icon {
      background: linear-gradient(135deg, #2ecc71, #27ae60);
    }
  }

  &.collected {
    border-left-color: #9b59b6;

    .message-icon {
      background: linear-gradient(135deg, #9b59b6, #8e44ad);
    }
  }

  &.cooking {
    border-left-color: #e74c3c;

    .message-icon {
      background: linear-gradient(135deg, #e74c3c, #c0392b);
    }
  }

  &.failed {
    border-left-color: #e74c3c;

    .message-icon {
      background: linear-gradient(135deg, #e74c3c, #c0392b);
    }
  }
}

.verification-status {
  display: flex;
  gap: 1rem;
  margin: 0.75rem 0;
  flex-wrap: wrap;

  .verify-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    font-size: 0.85rem;

    &.verified {
      background: #d4edda;
      color: #155724;

      i {
        color: #28a745;
      }
    }

    &:not(.verified) {
      background: #f8d7da;
      color: #721c24;

      i {
        color: #dc3545;
      }
    }

    i {
      font-size: 0.9rem;
    }
  }
}

.estimated-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #e8f4fd;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  color: #1e88e5;
  font-size: 0.85rem;
  font-weight: 600;
  margin-top: 0.75rem;

  i {
    font-size: 0.9rem;
  }
}

.failure-reason {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  padding: 0.75rem;
  border-radius: 0.5rem;
  color: #856404;
  font-size: 0.9rem;
  margin: 0.75rem 0;

  strong {
    color: #533f03;
  }
}

.cooking-animation {
  margin-top: 0.75rem;

  .cooking-dots {
    display: flex;
    gap: 0.25rem;
    align-items: center;

    span {
      width: 8px;
      height: 8px;
      background: #e74c3c;
      border-radius: 50%;
      animation: cookingPulse 1.5s infinite;

      &:nth-child(2) {
        animation-delay: 0.3s;
      }

      &:nth-child(3) {
        animation-delay: 0.6s;
      }
    }
  }
}

@keyframes cookingPulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// Responsive design
@media (max-width: 768px) {
  .status-message-card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;

    .message-icon {
      align-self: center;
      margin-bottom: 0.5rem;
    }

    .message-actions {
      justify-content: center;
    }

    .verification-status {
      justify-content: center;
    }
  }

  .action-btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}
