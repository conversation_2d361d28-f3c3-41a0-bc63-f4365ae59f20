import { Component, OnInit } from '@angular/core';
import { UserService } from 'src/app/core/services/user.service';

@Component({
  selector: 'app-pre-checkout-theme',
  templateUrl: './pre-checkout-theme.component.html',
  styleUrls: ['./pre-checkout-theme.component.scss']
})
export class PreCheckoutThemeComponent implements OnInit {
  menuTheme: string;

  constructor(public userService: UserService) { }

  ngOnInit(): void {
    this.userService.getUserTheme().subscribe(res => {
      this.menuTheme = res;
    })
  }
}
