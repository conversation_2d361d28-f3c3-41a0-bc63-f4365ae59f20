import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { finalize } from 'rxjs/operators';
import { UserService } from 'src/app/core/services/user.service';

@Component({
  selector: 'app-auth',
  templateUrl: './auth.component.html',
  styleUrls: ['./auth.component.scss'],
})
export class AuthComponent implements OnInit {
  userId: string;
  menuTheme: string;

  constructor(
    public userService: UserService,
    private router: Router,
  ) { }

  ngOnInit(): void {
    let user = JSON.parse(this.userService.getUser());
    this.userId = user?.id

    if (this.userId) {
      this.router.navigateByUrl('/');
    }

    this.userService.getUserTheme().subscribe(res => {
      this.menuTheme = res;
    })
    window.scroll(0, 0);
  }
}
